/**
 * Completed Challenges List Component
 * @description Component for displaying completed challenges in the admin panel
 * @version 1.0.0
 * @status stable
 */

import React, { useState } from 'react';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow,
  Badge,
  Button,
  Input
} from '@/common/components/ui';
import { Search, Trophy, Calendar, DollarSign, Users, ArrowUpDown, ChevronRight } from 'lucide-react';
import { formatCurrency, formatDate } from '@/common/utils/formatters';

interface Challenge {
  id: number;
  type: string;
  startDate: string;
  endDate: string;
  prizePool: number;
  entryFee: number;
  status: string;
  prizesCalculated: boolean;
  _count?: {
    entries: number;
  };
}

interface CompletedChallengesListProps {
  challenges: Challenge[];
  isLoading: boolean;
  onSelectChallenge: (challengeId: number) => void;
}

/**
 * Completed Challenges List Component
 * @param props - Component props
 * @returns Completed Challenges List Component
 */
const CompletedChallengesList: React.FC<CompletedChallengesListProps> = ({ 
  challenges, 
  isLoading,
  onSelectChallenge
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [sortField, setSortField] = useState<string>('endDate');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Handle sort
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Filter and sort challenges
  const filteredChallenges = challenges
    .filter(challenge => 
      challenge.type.toLowerCase().includes(searchQuery.toLowerCase())
    )
    .sort((a, b) => {
      if (sortField === 'prizePool' || sortField === 'entryFee') {
        return sortDirection === 'asc' 
          ? a[sortField] - b[sortField]
          : b[sortField] - a[sortField];
      } else if (sortField === 'startDate' || sortField === 'endDate') {
        return sortDirection === 'asc'
          ? new Date(a[sortField]).getTime() - new Date(b[sortField]).getTime()
          : new Date(b[sortField]).getTime() - new Date(a[sortField]).getTime();
      } else {
        return sortDirection === 'asc'
          ? a[sortField]?.localeCompare(b[sortField])
          : b[sortField]?.localeCompare(a[sortField]);
      }
    });

  // Get challenge type badge color
  const getChallengeTypeBadge = (type: string) => {
    switch (type.toLowerCase()) {
      case 'daily':
        return <Badge variant="outline" className="bg-blue-500/10 text-blue-500 border-blue-500/20">Daily</Badge>;
      case 'weekly':
        return <Badge variant="outline" className="bg-purple-500/10 text-purple-500 border-purple-500/20">Weekly</Badge>;
      case 'monthly':
        return <Badge variant="outline" className="bg-amber-500/10 text-amber-500 border-amber-500/20">Monthly</Badge>;
      default:
        return <Badge variant="outline">{type}</Badge>;
    }
  };

  // Get prize calculation status badge
  const getPrizeCalculationBadge = (calculated: boolean) => {
    return calculated 
      ? <Badge variant="outline" className="bg-green-500/10 text-green-500 border-green-500/20">Calculated</Badge>
      : <Badge variant="outline" className="bg-red-500/10 text-red-500 border-red-500/20">Not Calculated</Badge>;
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="w-8 h-8 border-4 border-forex-primary border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-forex-muted" />
          <Input
            placeholder="Search challenges..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      {filteredChallenges.length === 0 ? (
        <div className="text-center py-8">
          <Trophy className="h-12 w-12 text-forex-muted mx-auto mb-3 opacity-20" />
          <p className="text-forex-muted">No completed challenges found</p>
        </div>
      ) : (
        <div className="rounded-md border border-forex-border/30">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[100px]">
                  <div 
                    className="flex items-center cursor-pointer"
                    onClick={() => handleSort('type')}
                  >
                    Type
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                  </div>
                </TableHead>
                <TableHead>
                  <div 
                    className="flex items-center cursor-pointer"
                    onClick={() => handleSort('endDate')}
                  >
                    End Date
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                  </div>
                </TableHead>
                <TableHead>
                  <div 
                    className="flex items-center cursor-pointer"
                    onClick={() => handleSort('prizePool')}
                  >
                    Prize Pool
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                  </div>
                </TableHead>
                <TableHead>Entries</TableHead>
                <TableHead>Prizes</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredChallenges.map((challenge) => (
                <TableRow key={challenge.id}>
                  <TableCell>{getChallengeTypeBadge(challenge.type)}</TableCell>
                  <TableCell>{formatDate(challenge.endDate)}</TableCell>
                  <TableCell>{formatCurrency(challenge.prizePool)}</TableCell>
                  <TableCell>{challenge._count?.entries || 0}</TableCell>
                  <TableCell>{getPrizeCalculationBadge(challenge.prizesCalculated)}</TableCell>
                  <TableCell className="text-right">
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => onSelectChallenge(challenge.id)}
                    >
                      Manage
                      <ChevronRight className="ml-2 h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  );
};

export default CompletedChallengesList;
