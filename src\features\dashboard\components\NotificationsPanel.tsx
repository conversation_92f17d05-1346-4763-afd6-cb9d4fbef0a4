import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
import { X, Bell, Trophy, Wallet, AlertCircle, Check, Clock } from 'lucide-react';
import {
  <PERSON>,
  CardHeader,
  CardT<PERSON>le,
  CardContent,
  <PERSON>ton,
  Badge
} from '@/common/components/ui';
import { useAppDispatch, useAppSelector } from '@/app/store';
import {
  fetchNotifications,
  markAsRead,
  markAllAsRead,
  selectNotifications,
  selectNotificationsLoading
} from '@/features/notifications/notificationsSlice';
import { formatDistanceToNow } from 'date-fns';

interface NotificationsPanelProps {
  onClose: () => void;
}

/**
 * Notifications panel component
 *
 * Displays user notifications with animations and visual styling.
 *
 * @component
 * @example
 * <NotificationsPanel onClose={() => setShowNotifications(false)} />
 */
const NotificationsPanel: React.FC<NotificationsPanelProps> = ({ onClose }) => {
  const dispatch = useAppDispatch();
  const notifications = useAppSelector(selectNotifications);
  const loading = useAppSelector(selectNotificationsLoading);

  // Fetch notifications when component mounts
  useEffect(() => {
    dispatch(fetchNotifications({ limit: 10 }));
  }, [dispatch]);

  // Get icon based on notification priority
  const getNotificationIcon = (priority: string) => {
    switch (priority) {
      case 'high':
        return <AlertCircle size={16} className="text-red-400" />;
      case 'normal':
        return <Bell size={16} className="text-blue-400" />;
      case 'low':
        return <Bell size={16} className="text-green-400" />;
      default:
        return <Bell size={16} className="text-blue-400" />;
    }
  };

  // Handle mark as read
  const handleMarkAsRead = (id: number) => {
    dispatch(markAsRead(id));
  };

  // Handle mark all as read
  const handleMarkAllAsRead = () => {
    dispatch(markAllAsRead());
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      transition={{ duration: 0.2 }}
    >
      <Card className="w-full shadow-lg border-blue-500/20 bg-[#0f1c2e]/95 backdrop-blur-md">
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-lg font-medium">Notifications</CardTitle>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X size={18} />
          </Button>
        </CardHeader>
        <CardContent className="p-0">
          {loading ? (
            <div className="py-6 text-center">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto mb-2"></div>
              <p className="text-gray-400">Loading notifications...</p>
            </div>
          ) : notifications.length === 0 ? (
            <div className="py-6 text-center text-gray-400">
              <Bell size={24} className="mx-auto mb-2 opacity-50" />
              <p>No notifications</p>
            </div>
          ) : (
            <div className="max-h-[350px] overflow-y-auto">
              {notifications.map((notification) => (
                <motion.div
                  key={notification.id}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.2 }}
                  className={`p-3 border-b border-blue-500/10 hover:bg-blue-500/5 transition-colors duration-200 ${
                    !notification.seen ? 'bg-blue-500/10' : ''
                  }`}
                >
                  <div className="flex items-start">
                    <div className="mr-3 mt-1">
                      {getNotificationIcon(notification.priority)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h4 className="text-sm font-medium text-white">
                          {notification.title}
                        </h4>
                        <span className="text-xs text-gray-400">
                          {formatDistanceToNow(new Date(notification.timestamp), { addSuffix: true })}
                        </span>
                      </div>
                      <p className="text-xs text-gray-300 mt-1">
                        {notification.description}
                      </p>
                    </div>
                    {!notification.seen && (
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6 ml-1"
                        onClick={() => handleMarkAsRead(notification.id)}
                      >
                        <Check className="h-3.5 w-3.5 text-blue-400 hover:text-blue-300" />
                      </Button>
                    )}
                  </div>
                </motion.div>
              ))}
            </div>
          )}

          <div className="p-3 text-center border-t border-blue-500/10">
            <Button
              variant="ghost"
              size="sm"
              className="text-blue-400 hover:text-blue-300 text-xs"
              onClick={handleMarkAllAsRead}
              disabled={notifications.length === 0 || notifications.every(n => n.seen)}
            >
              Mark all as read
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="text-blue-400 hover:text-blue-300 text-xs"
              onClick={onClose}
            >
              Close
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default NotificationsPanel;
