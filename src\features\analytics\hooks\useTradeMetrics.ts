import { useState, useEffect } from 'react';
import { TradeData } from '../context/AnalyticsContext';

/**
 * Hook for calculating trade metrics from trade data
 * 
 * @param trades - Array of trade data
 * @param initialBalance - Initial account balance
 * @returns Calculated trade metrics
 */
export const useTradeMetrics = (trades: TradeData[], initialBalance: number = 10000) => {
  const [metrics, setMetrics] = useState({
    totalTrades: 0,
    winningTrades: 0,
    losingTrades: 0,
    totalPnl: 0,
    pnlPercent: 0,
    winRate: 0,
    averagePnl: 0,
    maxDrawdown: 0,
    dailyDrawdown: 0,
    averageDuration: 0,
    profitFactor: 0,
    expectancy: 0,
    averageWin: 0,
    averageLoss: 0,
    largestWin: 0,
    largestLoss: 0,
    maxConsecutiveWins: 0,
    maxConsecutiveLosses: 0,
    symbolPerformance: {} as Record<string, {
      totalTrades: number;
      winningTrades: number;
      pnl: number;
      winRate: number;
    }>,
    timePerformance: {} as Record<string, {
      totalTrades: number;
      winningTrades: number;
      pnl: number;
    }>,
  });

  useEffect(() => {
    if (!trades || trades.length === 0) return;

    // Basic metrics
    const totalTrades = trades.length;
    const winningTrades = trades.filter(trade => trade.pnl > 0).length;
    const losingTrades = totalTrades - winningTrades;
    const totalPnl = trades.reduce((sum, trade) => sum + trade.pnl, 0);
    const pnlPercent = (totalPnl / initialBalance) * 100;
    const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;
    const averagePnl = totalTrades > 0 ? totalPnl / totalTrades : 0;
    
    // Duration metrics
    const averageDuration = totalTrades > 0 
      ? trades.reduce((sum, trade) => sum + trade.duration, 0) / totalTrades 
      : 0;
    
    // Win/loss metrics
    const winningTradesArray = trades.filter(trade => trade.pnl > 0);
    const losingTradesArray = trades.filter(trade => trade.pnl <= 0);
    
    const totalWinAmount = winningTradesArray.reduce((sum, trade) => sum + trade.pnl, 0);
    const totalLossAmount = Math.abs(losingTradesArray.reduce((sum, trade) => sum + trade.pnl, 0));
    
    const profitFactor = totalLossAmount > 0 ? totalWinAmount / totalLossAmount : 0;
    
    const averageWin = winningTradesArray.length > 0 
      ? totalWinAmount / winningTradesArray.length 
      : 0;
      
    const averageLoss = losingTradesArray.length > 0 
      ? totalLossAmount / losingTradesArray.length 
      : 0;
    
    const expectancy = (winRate / 100 * averageWin) - ((100 - winRate) / 100 * averageLoss);
    
    // Extreme values
    const largestWin = winningTradesArray.length > 0 
      ? Math.max(...winningTradesArray.map(trade => trade.pnl)) 
      : 0;
      
    const largestLoss = losingTradesArray.length > 0 
      ? Math.min(...losingTradesArray.map(trade => trade.pnl)) 
      : 0;
    
    // Consecutive wins/losses
    let currentStreak = 0;
    let maxWinStreak = 0;
    let maxLossStreak = 0;
    
    // Sort trades by time
    const sortedTrades = [...trades].sort((a, b) => 
      new Date(a.exitTime).getTime() - new Date(b.exitTime).getTime()
    );
    
    sortedTrades.forEach(trade => {
      if (trade.pnl > 0) {
        // Winning trade
        if (currentStreak > 0) {
          currentStreak++;
        } else {
          currentStreak = 1;
        }
        
        if (currentStreak > maxWinStreak) {
          maxWinStreak = currentStreak;
        }
      } else {
        // Losing trade
        if (currentStreak < 0) {
          currentStreak--;
        } else {
          currentStreak = -1;
        }
        
        if (Math.abs(currentStreak) > maxLossStreak) {
          maxLossStreak = Math.abs(currentStreak);
        }
      }
    });
    
    // Calculate drawdown
    let maxDrawdown = 0;
    let peak = initialBalance;
    let currentBalance = initialBalance;
    
    sortedTrades.forEach(trade => {
      currentBalance += trade.pnl;
      
      if (currentBalance > peak) {
        peak = currentBalance;
      }
      
      const drawdown = ((peak - currentBalance) / peak) * 100;
      if (drawdown > maxDrawdown) {
        maxDrawdown = drawdown;
      }
    });
    
    // Symbol performance
    const symbolPerformance: Record<string, any> = {};
    
    trades.forEach(trade => {
      if (!symbolPerformance[trade.symbol]) {
        symbolPerformance[trade.symbol] = {
          totalTrades: 0,
          winningTrades: 0,
          pnl: 0,
          winRate: 0,
        };
      }
      
      symbolPerformance[trade.symbol].totalTrades += 1;
      if (trade.pnl > 0) {
        symbolPerformance[trade.symbol].winningTrades += 1;
      }
      symbolPerformance[trade.symbol].pnl += trade.pnl;
    });
    
    // Calculate win rates for symbols
    Object.keys(symbolPerformance).forEach(symbol => {
      const stats = symbolPerformance[symbol];
      stats.winRate = stats.totalTrades > 0 
        ? (stats.winningTrades / stats.totalTrades) * 100 
        : 0;
    });
    
    // Time performance (by hour)
    const timePerformance: Record<string, any> = {};
    
    trades.forEach(trade => {
      const hour = new Date(trade.entryTime).getHours();
      const hourKey = `${hour}:00`;
      
      if (!timePerformance[hourKey]) {
        timePerformance[hourKey] = {
          totalTrades: 0,
          winningTrades: 0,
          pnl: 0,
        };
      }
      
      timePerformance[hourKey].totalTrades += 1;
      if (trade.pnl > 0) {
        timePerformance[hourKey].winningTrades += 1;
      }
      timePerformance[hourKey].pnl += trade.pnl;
    });
    
    setMetrics({
      totalTrades,
      winningTrades,
      losingTrades,
      totalPnl,
      pnlPercent,
      winRate,
      averagePnl,
      maxDrawdown,
      dailyDrawdown: 0, // Would need daily data to calculate
      averageDuration,
      profitFactor,
      expectancy,
      averageWin,
      averageLoss,
      largestWin,
      largestLoss,
      maxConsecutiveWins: maxWinStreak,
      maxConsecutiveLosses: maxLossStreak,
      symbolPerformance,
      timePerformance,
    });
  }, [trades, initialBalance]);

  return metrics;
};
