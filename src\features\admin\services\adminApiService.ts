/**
 * Admin API Service
 * @description Service for making API calls to the admin endpoints
 * @version 1.0.0
 * @status stable
 */

import { apiService as api, ApiError } from '@/common/services/api';
import { toast } from 'sonner';

/**
 * Enhanced API wrapper with better error handling for admin functions
 * @param apiCall - Function that makes the API call
 * @param fallbackData - Data to return if the API call fails
 * @param errorMessage - Message to show if the API call fails
 * @returns Promise with response data or fallback data
 */
async function withErrorHandling<T>(
  apiCall: () => Promise<T>,
  fallbackData: T,
  errorMessage: string = 'Operation failed'
): Promise<T> {
  try {
    return await apiCall();
  } catch (error) {
    console.error('Admin API error:', error);

    // Show toast with error message
    if (error instanceof ApiError) {
      toast.error(`${errorMessage}: ${error.message}`);
    } else {
      toast.error(errorMessage);
    }

    // Return fallback data
    return fallbackData;
  }
}

/**
 * User management API functions
 */
export const userAdminApi = {
  /**
   * Get all users
   * @param params - Query parameters for filtering and pagination
   * @returns Promise with user data
   */
  getAllUsers: async (params?: {
    status?: string;
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    page?: number;
    limit?: number;
  }) => {
    const queryParams = new URLSearchParams();

    if (params?.status) queryParams.append('status', params.status);
    if (params?.search) queryParams.append('search', params.search);
    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());

    const queryString = queryParams.toString();
    const url = `/users${queryString ? `?${queryString}` : ''}`;

    return api.get(url);
  },

  /**
   * Get user by ID
   * @param userId - User ID
   * @returns Promise with user data
   */
  getUserById: async (userId: string) => {
    return api.get(`/users/${userId}`);
  },

  /**
   * Update user status
   * @param userId - User ID
   * @param status - New status
   * @returns Promise with updated user data
   */
  updateUserStatus: async (userId: string, status: 'active' | 'banned') => {
    return api.put(`/users/${userId}`, { status });
  },

  /**
   * Add credits to user wallet
   * @param userId - User ID
   * @param amount - Amount to add
   * @param reason - Reason for adding credits
   * @returns Promise with transaction data
   */
  addCreditsToUser: async (userId: string, amount: number, reason: string) => {
    return api.post(`/admin/wallet/credits/${userId}`, { amount, reason });
  },

  /**
   * Deduct credits from user wallet
   * @param userId - User ID
   * @param amount - Amount to deduct
   * @param reason - Reason for deducting credits
   * @returns Promise with transaction data
   */
  deductCreditsFromUser: async (userId: string, amount: number, reason: string) => {
    return api.post(`/admin/wallet/deduct/${userId}`, { amount, reason });
  },

  /**
   * Get user wallet transactions
   * @param userId - User ID
   * @returns Promise with transaction data
   */
  getUserWalletTransactions: async (userId: string) => {
    return api.get(`/admin/wallet/transactions/${userId}`);
  },

  /**
   * Expire inactive credits
   * @returns Promise with expiration data
   */
  expireInactiveCredits: async () => {
    return api.post('/admin/wallet/expire-credits');
  },
};

/**
 * Challenge management API functions
 */
export const challengeAdminApi = {
  /**
   * Get all challenges
   * @param params - Query parameters for filtering and pagination
   * @returns Promise with challenge data
   */
  getAllChallenges: async (params?: {
    type?: string;
    status?: string;
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    page?: number;
    limit?: number;
  }) => {
    const queryParams = new URLSearchParams();

    if (params?.type) queryParams.append('type', params.type);
    if (params?.status) queryParams.append('status', params.status);
    if (params?.search) queryParams.append('search', params.search);
    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());

    const queryString = queryParams.toString();
    const url = `/challenges${queryString ? `?${queryString}` : ''}`;

    return withErrorHandling(
      () => api.get(url),
      [], // Return empty array as fallback
      'Failed to load challenges'
    );
  },

  /**
   * Get challenge by ID
   * @param challengeId - Challenge ID
   * @returns Promise with challenge data
   */
  getChallengeById: async (challengeId: number) => {
    return withErrorHandling(
      () => api.get(`/challenges/${challengeId}`),
      null, // Return null as fallback
      'Failed to load challenge details'
    );
  },

  /**
   * Create a new challenge
   * @param challengeData - Challenge data
   * @returns Promise with created challenge data
   */
  createChallenge: async (challengeData: any) => {
    // Log the challenge data being sent
    console.log('Challenge data being sent to API:', JSON.stringify(challengeData, null, 2));

    try {
      // Validate dates
      const startDate = new Date(challengeData.startDate);
      const endDate = new Date(challengeData.endDate);
      const now = new Date();

      // Basic validation before sending to API
      if (startDate <= now) {
        console.warn('Start date must be in the future');
        toast.error('Start date must be in the future for new challenges');
        return { success: false, data: null, error: 'Start date must be in the future' };
      }

      if (endDate <= startDate) {
        console.warn('End date must be after start date');
        toast.error('End date must be after start date');
        return { success: false, data: null, error: 'End date must be after start date' };
      }

      // Ensure all required fields are present
      const validatedData = {
        ...challengeData,
        // Ensure name is present
        name: challengeData.name || `${challengeData.type.charAt(0).toUpperCase() + challengeData.type.slice(1)} Challenge`,
        // Ensure entryFee is a number
        entryFee: typeof challengeData.entryFee === 'number' ? challengeData.entryFee : 0,
        // Ensure prizePool is a number
        prizePool: typeof challengeData.prizePool === 'number' ? challengeData.prizePool : 100,
        // Ensure status is set
        status: challengeData.status || 'upcoming',
      };

      // Ensure ruleset is present
      if (!validatedData.ruleset) {
        console.warn('Ruleset is required');
        toast.error('Ruleset is required for challenge creation');
        return { success: false, data: null, error: 'Ruleset is required' };
      }

      console.log('Sending validated challenge data to API:', JSON.stringify(validatedData, null, 2));

      return withErrorHandling(
        () => api.post('/challenges', validatedData),
        { success: false, data: null }, // Return failure object as fallback
        'Failed to create challenge'
      );
    } catch (error) {
      console.error('Error in challenge validation:', error);
      toast.error(`Challenge validation error: ${error.message || 'Unknown error'}`);
      return { success: false, data: null, error: error.message || 'Unknown error' };
    }
  },

  /**
   * Update a challenge
   * @param challengeId - Challenge ID
   * @param challengeData - Updated challenge data
   * @returns Promise with updated challenge data
   */
  updateChallenge: async (challengeId: number, challengeData: any) => {
    return withErrorHandling(
      () => api.put(`/challenges/${challengeId}`, challengeData),
      { success: false, data: null }, // Return failure object as fallback
      'Failed to update challenge'
    );
  },

  /**
   * Delete a challenge
   * @param challengeId - Challenge ID
   * @returns Promise with deletion status
   */
  deleteChallenge: async (challengeId: number) => {
    console.log('adminApiService: Deleting challenge with ID:', challengeId);

    try {
      const result = await api.delete(`/challenges/${challengeId}`);
      console.log('adminApiService: Delete challenge result:', result);
      return { success: true, data: result };
    } catch (error) {
      console.error('adminApiService: Error in deleteChallenge:', error);

      // Extract specific error message from API response
      let errorMessage = 'Failed to delete challenge';

      if (error && typeof error === 'object') {
        // Check for ApiError with details
        if ('details' in error && error.details && error.details.error) {
          errorMessage = error.details.error;
        }
        // Check for direct message
        else if ('message' in error && error.message) {
          errorMessage = error.message;
        }
        // Check for error property
        else if ('error' in error && error.error) {
          errorMessage = error.error;
        }
      }

      console.log('Extracted error message:', errorMessage);

      // Show specific error message to user
      toast.error(errorMessage);

      return { success: false, error: errorMessage };
    }
  },

  /**
   * Get default ruleset for a challenge type
   * @param type - Challenge type
   * @returns Promise with ruleset data
   */
  getDefaultRuleset: async (type: string) => {
    // Define default rulesets for different challenge types
    const defaultRulesets = {
      daily: {
        initialBalance: 10000,
        maxDrawdownPercent: 4,
        maxRiskPerTradePercent: 2,
        noHedging: true,
        noMartingale: true,
        minTradeDurationMinutes: 2,
        minTrades: 1
      },
      weekly: {
        initialBalance: 50000,
        maxDrawdownPercent: 8,
        maxDailyDrawdownPercent: 4,
        maxRiskPerTradePercent: 2,
        noHedging: true,
        noMartingale: true,
        minTradeDurationMinutes: 2,
        minTrades: 3,
        minSwingTradeDays: 2
      },
      monthly: {
        initialBalance: 100000,
        maxDrawdownPercent: 10,
        maxDailyDrawdownPercent: 4,
        maxRiskPerTradePercent: 2,
        noHedging: true,
        noMartingale: true,
        minTradeDurationMinutes: 2,
        minTrades: 6,
        minTradingDays: 6,
        minSwingTradeDays: 2
      }
    };

    // Get the default ruleset for the specified type
    const fallbackRuleset = defaultRulesets[type as keyof typeof defaultRulesets] || defaultRulesets.daily;

    return withErrorHandling(
      () => api.get(`/challenges/default-ruleset/${type}`),
      { data: fallbackRuleset }, // Return fallback ruleset
      'Failed to load ruleset'
    );
  },

  /**
   * Get all challenge entries
   * @param params - Query parameters for filtering and pagination
   * @returns Promise with challenge entry data
   */
  getAllChallengeEntries: async (params?: {
    challengeId?: number;
    userId?: string;
    status?: string;
    page?: number;
    limit?: number;
  }) => {
    const queryParams = new URLSearchParams();

    if (params?.challengeId) queryParams.append('challengeId', params.challengeId.toString());
    if (params?.userId) queryParams.append('userId', params.userId);
    if (params?.status) queryParams.append('status', params.status);
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());

    const queryString = queryParams.toString();
    const url = `/challenge-entries${queryString ? `?${queryString}` : ''}`;

    return api.get(url);
  },

  /**
   * Disqualify a challenge entry
   * @param entryId - Challenge entry ID
   * @param reason - Reason for disqualification
   * @returns Promise with disqualification status
   */
  disqualifyChallengeEntry: async (entryId: number, reason: string) => {
    return api.post(`/challenge-entries/${entryId}/disqualify`, { reason });
  },
};

/**
 * Transaction management API functions
 */
export const transactionAdminApi = {
  /**
   * Get all wallet balances
   * @returns Promise with wallet balance data
   */
  getAllWalletBalances: async () => {
    return api.get('/admin/wallet/balances');
  },

  /**
   * Get all wallet transactions
   * @param params - Query parameters for filtering and pagination
   * @returns Promise with transaction data
   */
  getAllTransactions: async (params?: {
    userId?: string;
    type?: string;
    startDate?: string;
    endDate?: string;
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    page?: number;
    limit?: number;
  }) => {
    const queryParams = new URLSearchParams();

    if (params?.userId) queryParams.append('userId', params.userId);
    if (params?.type) queryParams.append('type', params.type);
    if (params?.startDate) queryParams.append('startDate', params.startDate);
    if (params?.endDate) queryParams.append('endDate', params.endDate);
    if (params?.search) queryParams.append('search', params.search);
    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());

    const queryString = queryParams.toString();
    const url = `/admin/transactions${queryString ? `?${queryString}` : ''}`;

    return api.get(url);
  },

  /**
   * Get transaction statistics
   * @returns Promise with transaction statistics
   */
  getTransactionStats: async () => {
    return api.get('/admin/transactions/stats');
  },
};

/**
 * Prize distribution API functions
 */
export const prizeDistributionAdminApi = {
  /**
   * Get prize distributions
   * @param params - Query parameters for filtering
   * @returns Promise with prize distribution data
   */
  getPrizeDistributions: async (params?: {
    challengeId?: number;
    userId?: string;
    status?: string;
    minRank?: number;
    maxRank?: number;
  }) => {
    const queryParams = new URLSearchParams();

    if (params?.challengeId) queryParams.append('challengeId', params.challengeId.toString());
    if (params?.userId) queryParams.append('userId', params.userId);
    if (params?.status) queryParams.append('status', params.status);
    if (params?.minRank) queryParams.append('minRank', params.minRank.toString());
    if (params?.maxRank) queryParams.append('maxRank', params.maxRank.toString());

    const queryString = queryParams.toString();
    const url = `/prize-distributions${queryString ? `?${queryString}` : ''}`;

    return api.get(url);
  },

  /**
   * Get prize distributions for a challenge
   * @param challengeId - Challenge ID
   * @returns Promise with prize distribution data
   */
  getPrizeDistributionsForChallenge: async (challengeId: number) => {
    return api.get(`/prize-distributions/challenges/${challengeId}`);
  },

  /**
   * Approve a prize distribution
   * @param distributionId - Prize distribution ID
   * @returns Promise with approval status
   */
  approvePrizeDistribution: async (distributionId: number) => {
    return api.post(`/prize-distributions/${distributionId}/approve`);
  },

  /**
   * Mark a prize distribution as paid
   * @param distributionId - Prize distribution ID
   * @param paymentTxHash - Payment transaction hash
   * @param paymentAddress - Payment address
   * @returns Promise with payment status
   */
  markPrizeDistributionAsPaid: async (
    distributionId: number,
    paymentTxHash: string,
    paymentAddress?: string
  ) => {
    return api.post(`/prize-distributions/${distributionId}/paid`, {
      paymentTxHash,
      paymentAddress
    });
  },

  /**
   * Approve all prize distributions for a challenge
   * @param challengeId - Challenge ID
   * @returns Promise with approval status
   */
  approveAllPrizeDistributions: async (challengeId: number) => {
    return api.post(`/prize-distributions/challenges/${challengeId}/approve-all`);
  },
};

/**
 * Notification API functions
 */
export const notificationAdminApi = {
  /**
   * Create a notification
   * @param notificationData - Notification data
   * @returns Promise with created notification data
   */
  createNotification: async (notificationData: {
    userId?: string;
    title: string;
    message: string;
    type?: string;
    priority?: string;
  }) => {
    return api.post('/notifications', notificationData);
  },

  /**
   * Create a batch notification for multiple users
   * @param notificationData - Notification data
   * @returns Promise with created notification data
   */
  createBatchNotification: async (notificationData: {
    userIds: string[];
    title: string;
    message: string;
    type?: string;
    priority?: string;
  }) => {
    return api.post('/admin/notifications/batch', notificationData);
  },

  /**
   * Create a system-wide notification for all users
   * @param notificationData - Notification data
   * @returns Promise with created notification data
   */
  createSystemNotification: async (notificationData: {
    title: string;
    message: string;
    type?: string;
    priority?: string;
  }) => {
    return api.post('/admin/notifications/system', notificationData);
  },

  /**
   * Get all notifications
   * @param params - Query parameters for filtering and pagination
   * @returns Promise with notification data
   */
  getAllNotifications: async (params?: {
    userId?: string;
    seen?: boolean;
    priority?: string;
    startDate?: string;
    endDate?: string;
    page?: number;
    limit?: number;
  }) => {
    const queryParams = new URLSearchParams();

    if (params?.userId) queryParams.append('userId', params.userId);
    if (params?.seen !== undefined) queryParams.append('seen', params.seen.toString());
    if (params?.priority) queryParams.append('priority', params.priority);
    if (params?.startDate) queryParams.append('startDate', params.startDate);
    if (params?.endDate) queryParams.append('endDate', params.endDate);
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());

    const queryString = queryParams.toString();
    const url = `/admin/notifications${queryString ? `?${queryString}` : ''}`;

    return api.get(url);
  },

  /**
   * Delete a notification
   * @param id - Notification ID
   * @returns Promise with deletion status
   */
  deleteNotification: async (id: number) => {
    return api.delete(`/admin/notifications/${id}`);
  },
};

/**
 * System settings API functions
 */
export const settingsAdminApi = {
  /**
   * Get system settings
   * @returns Promise with settings data
   */
  getSystemSettings: async () => {
    return api.get('/admin/settings');
  },

  /**
   * Update system settings
   * @param settingsData - Updated settings data
   * @returns Promise with updated settings data
   */
  updateSystemSettings: async (settingsData: any) => {
    return api.put('/admin/settings', settingsData);
  },

  /**
   * Get challenge rulesets
   * @returns Promise with ruleset data
   */
  getChallengeRulesets: async () => {
    return api.get('/admin/settings/challenge-rulesets');
  },

  /**
   * Update challenge rulesets
   * @param rulesetData - Updated ruleset data
   * @returns Promise with updated ruleset data
   */
  updateChallengeRulesets: async (rulesetData: any) => {
    return api.put('/admin/settings/challenge-rulesets', rulesetData);
  },

  /**
   * Get wallet settings
   * @returns Promise with wallet settings data
   */
  getWalletSettings: async () => {
    return api.get('/admin/settings/wallet');
  },

  /**
   * Update wallet settings
   * @param walletSettings - Updated wallet settings data
   * @returns Promise with updated wallet settings data
   */
  updateWalletSettings: async (walletSettings: any) => {
    return api.put('/admin/settings/wallet', walletSettings);
  },
};

/**
 * Metrics API functions
 */
export const metricsAdminApi = {
  /**
   * Get system metrics
   * @param params - Query parameters for filtering
   * @returns Promise with metrics data
   */
  getSystemMetrics: async (params?: {
    startDate?: string;
    endDate?: string;
    interval?: 'hourly' | 'daily' | 'weekly' | 'monthly';
  }) => {
    const queryParams = new URLSearchParams();

    if (params?.startDate) queryParams.append('startDate', params.startDate);
    if (params?.endDate) queryParams.append('endDate', params.endDate);
    if (params?.interval) queryParams.append('interval', params.interval);

    const queryString = queryParams.toString();
    const url = `/metrics/system${queryString ? `?${queryString}` : ''}`;

    return api.get(url);
  },

  /**
   * Get user metrics
   * @param params - Query parameters for filtering
   * @returns Promise with metrics data
   */
  getUserMetrics: async (params?: {
    startDate?: string;
    endDate?: string;
    interval?: 'daily' | 'weekly' | 'monthly';
  }) => {
    const queryParams = new URLSearchParams();

    if (params?.startDate) queryParams.append('startDate', params.startDate);
    if (params?.endDate) queryParams.append('endDate', params.endDate);
    if (params?.interval) queryParams.append('interval', params.interval);

    const queryString = queryParams.toString();
    const url = `/metrics/users${queryString ? `?${queryString}` : ''}`;

    return api.get(url);
  },

  /**
   * Get challenge metrics
   * @param params - Query parameters for filtering
   * @returns Promise with metrics data
   */
  getChallengeMetrics: async (params?: {
    startDate?: string;
    endDate?: string;
    interval?: 'daily' | 'weekly' | 'monthly';
    type?: string;
  }) => {
    const queryParams = new URLSearchParams();

    if (params?.startDate) queryParams.append('startDate', params.startDate);
    if (params?.endDate) queryParams.append('endDate', params.endDate);
    if (params?.interval) queryParams.append('interval', params.interval);
    if (params?.type) queryParams.append('type', params.type);

    const queryString = queryParams.toString();
    const url = `/metrics/challenges${queryString ? `?${queryString}` : ''}`;

    return api.get(url);
  },
};

/**
 * Dashboard API functions
 */
export const dashboardAdminApi = {
  /**
   * Get dashboard statistics
   * @returns Promise with dashboard statistics
   */
  getDashboardStats: async () => {
    return api.get('/admin/dashboard/stats');
  },
};

// Export all admin API services
export const adminApiService = {
  user: userAdminApi,
  challenge: challengeAdminApi,
  transaction: transactionAdminApi,
  prizeDistribution: prizeDistributionAdminApi,
  notification: notificationAdminApi,
  settings: settingsAdminApi,
  metrics: metricsAdminApi,
  dashboard: dashboardAdminApi,
};
