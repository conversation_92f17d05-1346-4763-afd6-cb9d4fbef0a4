import React from 'react';
import { motion } from 'framer-motion';
import { Link, useLocation } from 'react-router-dom';
import {
  LayoutDashboard,
  LineChart,
  Trophy,
  BookOpen,
  Users,
  Menu
} from 'lucide-react';
import { TouchArea } from '@/common/components/ui';
import { useResponsive } from '@/common/context';

/**
 * Mobile navigation component for dashboard
 * 
 * Displays a fixed bottom navigation bar on mobile devices with
 * quick access to key dashboard features.
 * 
 * @component
 * @example
 * <MobileNavigation />
 */
const MobileNavigation: React.FC = () => {
  const location = useLocation();
  const { screenSize } = useResponsive();
  
  // Only show on mobile screens
  if (screenSize !== 'xs' && screenSize !== 'sm') {
    return null;
  }
  
  // Navigation items for mobile
  const navItems = [
    {
      name: 'Dashboard',
      path: '/dashboard',
      icon: <LayoutDashboard size={20} />
    },
    {
      name: 'Challenges',
      path: '/dashboard/challenges',
      icon: <Trophy size={20} />
    },
    {
      name: 'Analytics',
      path: '/dashboard/analytics',
      icon: <LineChart size={20} />
    },
    {
      name: 'Journal',
      path: '/dashboard/journal',
      icon: <BookOpen size={20} />
    },
    {
      name: 'Leaderboard',
      path: '/dashboard/leaderboard',
      icon: <Users size={20} />
    },
    {
      name: 'More',
      path: '#more',
      icon: <Menu size={20} />
    }
  ];
  
  return (
    <motion.div
      className="fixed bottom-0 left-0 right-0 bg-gradient-to-r from-[#0a1322]/95 via-[#0f1c2e]/95 to-[#0a1322]/95 backdrop-blur-md border-t border-blue-500/30 z-50 h-16 sm:h-14 flex items-center justify-around px-1"
      initial={{ y: 100 }}
      animate={{ y: 0 }}
      transition={{ type: 'spring', damping: 20, stiffness: 300 }}
    >
      {navItems.map((item) => {
        const isActive = 
          item.path === location.pathname || 
          (item.path !== '/dashboard' && item.path !== '#more' && location.pathname.startsWith(item.path));
        
        // For the "More" button, handle differently
        if (item.path === '#more') {
          return (
            <TouchArea key={item.path} scale press>
              <button
                onClick={() => {
                  // Toggle mobile menu in header
                  const event = new CustomEvent('toggle-mobile-menu');
                  window.dispatchEvent(event);
                }}
                className="flex flex-col items-center justify-center w-full h-full px-2"
              >
                <div className={`p-1.5 rounded-full ${isActive ? 'bg-blue-500/20' : ''}`}>
                  {item.icon}
                </div>
                <span className="text-xs mt-0.5 font-medium">{item.name}</span>
              </button>
            </TouchArea>
          );
        }
        
        return (
          <TouchArea key={item.path} scale press>
            <Link
              to={item.path}
              className="flex flex-col items-center justify-center w-full h-full px-2"
            >
              <div 
                className={`
                  p-1.5 rounded-full 
                  ${isActive ? 'bg-blue-500/20 text-blue-300' : 'text-gray-400'}
                `}
              >
                {item.icon}
              </div>
              <span 
                className={`
                  text-xs mt-0.5 font-medium
                  ${isActive ? 'text-blue-300' : 'text-gray-400'}
                `}
              >
                {item.name}
              </span>
            </Link>
          </TouchArea>
        );
      })}
    </motion.div>
  );
};

export default MobileNavigation;
