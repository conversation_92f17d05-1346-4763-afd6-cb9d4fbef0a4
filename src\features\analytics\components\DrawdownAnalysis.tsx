import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/common/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger, TabsContent } from '@/common/components/ui/tabs';
import { Progress } from '@/common/components/ui/progress';
import { useAnalyticsContext } from '../context/AnalyticsContext';
import {
  AlertTriangle,
  TrendingDown,
  ArrowDownRight,
  Shield,
  BarChart2,
  Calendar,
  Clock,
  Activity,
  LineChart,
  RefreshCw
} from 'lucide-react';
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ReferenceLine
} from 'recharts';
import InfoButton from './InfoButton';
import { Badge } from '@/common/components/ui/badge';
import { Button } from '@/common/components/ui/button';
import RealTimeIndicator from './RealTimeIndicator';

/**
 * Drawdown analysis component
 *
 * Tracks drawdown in real-time and provides historical context with
 * detailed visualization and analysis. Includes real-time update indicators.
 *
 * @component
 * @example
 * <DrawdownAnalysis />
 */
const DrawdownAnalysis: React.FC = () => {
  const {
    drawdown,
    metrics,
    trades,
    lastUpdate,
    updateCounts,
    refreshData
  } = useAnalyticsContext();
  const [activeTab, setActiveTab] = useState<string>("current");
  const [timeRange, setTimeRange] = useState<'1d' | '1w' | '1m' | 'all'>('1w');
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);

  // Handle refresh
  const handleRefresh = () => {
    setIsRefreshing(true);
    refreshData();

    // Reset refreshing state after animation
    setTimeout(() => {
      setIsRefreshing(false);
    }, 1000);
  };

  // Format time since last update
  const getTimeSinceLastUpdate = () => {
    const update = lastUpdate.drawdown;
    if (!update) return 'No updates yet';

    const now = new Date();
    const diffMs = now.getTime() - update.getTime();

    if (diffMs < 1000) return 'Just now';
    if (diffMs < 60000) return `${Math.floor(diffMs / 1000)}s ago`;
    if (diffMs < 3600000) return `${Math.floor(diffMs / 60000)}m ago`;

    return `${Math.floor(diffMs / 3600000)}h ago`;
  };

  // Use either real-time drawdown or metrics drawdown
  const currentDrawdown = drawdown?.currentDrawdown || metrics?.dailyDrawdown || 0;
  const maxDrawdown = drawdown?.maxDrawdown || metrics?.maxDrawdown || 0;
  const remainingMargin = drawdown?.remainingMargin || (10 - currentDrawdown) || 0;

  // Determine risk level
  const getRiskLevel = (drawdown: number) => {
    if (drawdown < 3) return { level: 'Low', color: 'text-green-400', bgColor: 'bg-green-400' };
    if (drawdown < 6) return { level: 'Moderate', color: 'text-yellow-400', bgColor: 'bg-yellow-400' };
    if (drawdown < 8) return { level: 'High', color: 'text-orange-400', bgColor: 'bg-orange-400' };
    return { level: 'Critical', color: 'text-red-400', bgColor: 'bg-red-400' };
  };

  const currentRisk = getRiskLevel(currentDrawdown);
  const maxRisk = getRiskLevel(maxDrawdown);

  // Generate sample drawdown history data
  // In a real implementation, this would come from the API
  const generateDrawdownHistory = () => {
    const now = new Date();
    const data = [];

    // Number of data points based on time range
    const dataPoints = timeRange === '1d' ? 24 :
                      timeRange === '1w' ? 7 :
                      timeRange === '1m' ? 30 : 60;

    // Time increment based on time range
    const timeIncrement = timeRange === '1d' ? 60 * 60 * 1000 : // 1 hour
                         timeRange === '1w' ? 24 * 60 * 60 * 1000 : // 1 day
                         timeRange === '1m' ? 24 * 60 * 60 * 1000 : // 1 day
                         24 * 60 * 60 * 1000; // 1 day

    // Generate data points
    for (let i = dataPoints - 1; i >= 0; i--) {
      const date = new Date(now.getTime() - (i * timeIncrement));

      // Base drawdown on max drawdown with some randomness
      // In a real implementation, this would be actual historical data
      let drawdownValue = maxDrawdown * 0.5 + (Math.random() * maxDrawdown * 0.5);

      // Make more recent points closer to current drawdown
      if (i < 5) {
        drawdownValue = currentDrawdown + (Math.random() * 1 - 0.5);
      }

      // Ensure drawdown is positive and not too high
      drawdownValue = Math.max(0, Math.min(10, drawdownValue));

      data.push({
        date: date.toISOString(),
        drawdown: parseFloat(drawdownValue.toFixed(2)),
        formattedDate: timeRange === '1d'
          ? date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
          : date.toLocaleDateString([], { month: 'short', day: 'numeric' })
      });
    }

    return data;
  };

  const drawdownHistory = generateDrawdownHistory();

  // Calculate recovery metrics
  const calculateRecoveryMetrics = () => {
    if (!trades || trades.length === 0) return { avgRecoveryTime: 0, recoveryRate: 0 };

    // Sort trades by date
    const sortedTrades = [...trades].sort((a, b) =>
      new Date(a.entryTime).getTime() - new Date(b.entryTime).getTime()
    );

    // Find drawdown periods (consecutive losing trades)
    let drawdownPeriods = 0;
    let totalRecoveryDays = 0;
    let inDrawdown = false;
    let drawdownStart: Date | null = null;
    let recoveryStart: Date | null = null;

    for (let i = 0; i < sortedTrades.length; i++) {
      const trade = sortedTrades[i];

      if (trade.pnl < 0 && !inDrawdown) {
        // Start of a drawdown period
        inDrawdown = true;
        drawdownStart = new Date(trade.entryTime);
      } else if (trade.pnl > 0 && inDrawdown) {
        // End of a drawdown period, start of recovery
        inDrawdown = false;
        recoveryStart = new Date(trade.entryTime);
      } else if (trade.pnl > 0 && recoveryStart) {
        // In recovery period, check if we've recovered the drawdown
        // In a real implementation, we would track actual equity values
        // For now, we'll use a simplified approach

        // Assume recovery after 2 consecutive winning trades
        if (i > 0 && sortedTrades[i-1].pnl > 0) {
          const recoveryEnd = new Date(trade.entryTime);
          if (recoveryStart && drawdownStart) {
            const recoveryDays = (recoveryEnd.getTime() - drawdownStart.getTime()) / (1000 * 60 * 60 * 24);
            totalRecoveryDays += recoveryDays;
            drawdownPeriods++;
          }
          recoveryStart = null;
          drawdownStart = null;
        }
      }
    }

    const avgRecoveryTime = drawdownPeriods > 0 ? totalRecoveryDays / drawdownPeriods : 0;
    const recoveryRate = trades.length > 0 ? drawdownPeriods / trades.length * 100 : 0;

    return { avgRecoveryTime, recoveryRate };
  };

  const { avgRecoveryTime, recoveryRate } = calculateRecoveryMetrics();

  return (
    <Card className="border-gray-800 bg-gray-900/60 backdrop-blur-sm overflow-hidden relative">
      {/* Real-time indicator */}
      <RealTimeIndicator dataType="drawdown" position="top-right" />

      <CardHeader className="pb-2">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <TrendingDown className="h-5 w-5 text-red-400" />
            <div className="flex items-center gap-1.5">
              Drawdown Analysis
              <InfoButton
                content="Drawdown measures the decline from a peak in your account balance to a trough. This analysis helps you monitor your risk management and stay within challenge rules."
                className="ml-1"
              />
            </div>
          </div>

          <div className="flex items-center gap-3">
            <div className="text-xs text-gray-400 flex items-center gap-1">
              <span>Updated:</span>
              <span className={lastUpdate.drawdown ? 'text-gray-300' : 'text-gray-500'}>
                {getTimeSinceLastUpdate()}
              </span>
              {updateCounts.drawdown > 0 && (
                <Badge variant="outline" className="ml-1 text-[10px] h-4 px-1 border-blue-700 text-blue-400">
                  {updateCounts.drawdown} updates
                </Badge>
              )}
            </div>

            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              <AnimatePresence mode="wait">
                {isRefreshing ? (
                  <motion.div
                    key="refreshing"
                    initial={{ opacity: 0, rotate: 0 }}
                    animate={{ opacity: 1, rotate: 360 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.5 }}
                  >
                    <RefreshCw className="h-4 w-4 text-blue-400" />
                  </motion.div>
                ) : (
                  <motion.div
                    key="refresh"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                  >
                    <RefreshCw className="h-4 w-4 text-gray-400 hover:text-white" />
                  </motion.div>
                )}
              </AnimatePresence>
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="current" className="w-full" onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="current" className="text-xs">Current Status</TabsTrigger>
            <TabsTrigger value="history" className="text-xs">Historical Data</TabsTrigger>
            <TabsTrigger value="recovery" className="text-xs">Recovery Analysis</TabsTrigger>
          </TabsList>

          <TabsContent value="current">
            <div className="space-y-4">
              {/* Current Drawdown */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                <div className="flex justify-between items-center mb-2">
                  <div className="text-sm text-gray-400 flex items-center">
                    Current Drawdown
                    <InfoButton
                      content="Your current drawdown percentage, calculated from the peak balance to the current balance."
                      className="ml-1"
                      position="right"
                    />
                  </div>
                  <div className="flex items-center gap-1">
                    <Badge className={`${currentRisk.color.replace('text-', 'bg-')}/20 ${currentRisk.color} border-0`}>
                      {currentRisk.level}
                    </Badge>
                    <div className={`text-sm font-medium ${currentRisk.color}`}>
                      {currentDrawdown.toFixed(2)}%
                    </div>
                  </div>
                </div>
                <Progress
                  value={currentDrawdown * 10}
                  max={100}
                  className="h-2 bg-gray-700"
                  indicatorClassName={currentRisk.bgColor}
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <div>0%</div>
                  <div>5%</div>
                  <div>10%</div>
                </div>
              </motion.div>

              {/* Maximum Drawdown */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                <div className="flex justify-between items-center mb-2">
                  <div className="text-sm text-gray-400 flex items-center">
                    Maximum Drawdown
                    <InfoButton
                      content="The largest percentage drop from peak to trough in your account balance. This is a key risk metric in trading challenges."
                      className="ml-1"
                      position="right"
                    />
                  </div>
                  <div className="flex items-center gap-1">
                    <Badge className={`${maxRisk.color.replace('text-', 'bg-')}/20 ${maxRisk.color} border-0`}>
                      {maxRisk.level}
                    </Badge>
                    <div className={`text-sm font-medium ${maxRisk.color}`}>
                      {maxDrawdown.toFixed(2)}%
                    </div>
                  </div>
                </div>
                <Progress
                  value={maxDrawdown * 10}
                  max={100}
                  className="h-2 bg-gray-700"
                  indicatorClassName={maxRisk.bgColor}
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <div>0%</div>
                  <div>5%</div>
                  <div>10%</div>
                </div>
              </motion.div>

              {/* Remaining Margin */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="bg-gray-800/50 rounded-lg p-3 border border-gray-700 mt-4"
              >
                <div className="flex items-center gap-2 mb-2">
                  <Shield className="h-4 w-4 text-blue-400" />
                  <div className="text-sm font-medium flex items-center">
                    Remaining Margin
                    <InfoButton
                      content="The buffer you have before reaching the maximum allowed drawdown (typically 10%). This is critical for staying within challenge rules."
                      className="ml-1"
                      position="right"
                    />
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <div className="text-2xl font-bold">
                    {remainingMargin.toFixed(2)}%
                  </div>
                  {remainingMargin < 2 && (
                    <div className="flex items-center text-xs text-red-400 bg-red-400/20 px-2 py-1 rounded">
                      <AlertTriangle className="h-3 w-3 mr-1" />
                      Critical
                    </div>
                  )}
                </div>
                <div className="text-xs text-gray-400 mt-1">
                  Maximum allowed drawdown: 10.00%
                </div>
              </motion.div>

              {/* Drawdown Insights */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="bg-gray-800/50 rounded-lg p-3 border border-gray-700"
              >
                <div className="flex items-center gap-2 mb-2">
                  <ArrowDownRight className="h-4 w-4 text-purple-400" />
                  <div className="text-sm font-medium flex items-center">
                    Drawdown Insights
                    <InfoButton
                      content="Personalized insights based on your drawdown patterns and history. These insights help you understand your risk management effectiveness."
                      className="ml-1"
                      position="right"
                    />
                  </div>
                </div>
                <ul className="text-xs text-gray-400 space-y-2">
                  <li className="flex items-start gap-2">
                    <div className="rounded-full h-1.5 w-1.5 bg-gray-500 mt-1.5"></div>
                    <span>
                      {currentDrawdown > maxDrawdown * 0.8
                        ? "You're approaching your maximum historical drawdown level."
                        : "Current drawdown is within your historical range."}
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <div className="rounded-full h-1.5 w-1.5 bg-gray-500 mt-1.5"></div>
                    <span>
                      {maxDrawdown > 7
                        ? "Your maximum drawdown is high. Consider reviewing risk management."
                        : "Your maximum drawdown is well-controlled."}
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <div className="rounded-full h-1.5 w-1.5 bg-gray-500 mt-1.5"></div>
                    <span>
                      {remainingMargin < 3
                        ? "Your remaining margin is low. Consider reducing position sizes temporarily."
                        : "You have sufficient margin to withstand normal market fluctuations."}
                    </span>
                  </li>
                </ul>
              </motion.div>
            </div>
          </TabsContent>

          <TabsContent value="history">
            <div className="space-y-4">
              {/* Time Range Selector */}
              <div className="flex justify-end mb-2">
                <div className="flex bg-gray-800 rounded-md overflow-hidden">
                  {(['1d', '1w', '1m', 'all'] as const).map((range) => (
                    <button
                      key={range}
                      className={`px-2 py-1 text-xs ${timeRange === range ? 'bg-blue-900/50 text-blue-400' : 'text-gray-400'}`}
                      onClick={() => setTimeRange(range)}
                    >
                      {range === '1d' ? '1D' : range === '1w' ? '1W' : range === '1m' ? '1M' : 'ALL'}
                    </button>
                  ))}
                </div>
              </div>

              {/* Drawdown History Chart */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-800/50 rounded-lg p-3 border border-gray-700 h-[250px]"
              >
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart
                    data={drawdownHistory}
                    margin={{ top: 10, right: 10, left: 0, bottom: 10 }}
                  >
                    <defs>
                      <linearGradient id="drawdownGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#f87171" stopOpacity={0.4} />
                        <stop offset="95%" stopColor="#f87171" stopOpacity={0} />
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" stroke="#2a4d7d" opacity={0.3} />
                    <XAxis
                      dataKey="formattedDate"
                      stroke="#ffffff"
                      opacity={0.7}
                      tick={{ fontSize: 10 }}
                      tickFormatter={(value, index) => {
                        // Show fewer ticks on small screens
                        return index % Math.ceil(drawdownHistory.length / 5) === 0 ? value : '';
                      }}
                    />
                    <YAxis
                      stroke="#ffffff"
                      opacity={0.7}
                      tick={{ fontSize: 10 }}
                      tickFormatter={(value) => `${value}%`}
                      domain={[0, 10]}
                    />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: '#0f1c2e',
                        border: '1px solid #2a4d7d',
                        borderRadius: '4px',
                        fontSize: '12px'
                      }}
                      formatter={(value: any) => [`${value}%`, 'Drawdown']}
                      labelFormatter={(label) => `Date: ${label}`}
                    />
                    <ReferenceLine y={5} stroke="#facc15" strokeDasharray="3 3" />
                    <ReferenceLine y={10} stroke="#f87171" strokeDasharray="3 3" />
                    <Area
                      type="monotone"
                      dataKey="drawdown"
                      stroke="#f87171"
                      fillOpacity={1}
                      fill="url(#drawdownGradient)"
                      strokeWidth={2}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </motion.div>

              {/* Historical Insights */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-800/50 rounded-lg p-3 border border-gray-700"
              >
                <div className="flex items-center gap-2 mb-2">
                  <Activity className="h-4 w-4 text-blue-400" />
                  <div className="text-sm font-medium">Historical Insights</div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-2">
                  <div className="bg-gray-700/30 rounded p-2">
                    <div className="text-xs text-gray-400">Average Drawdown</div>
                    <div className="text-lg font-medium">
                      {(drawdownHistory.reduce((sum, item) => sum + item.drawdown, 0) / drawdownHistory.length).toFixed(2)}%
                    </div>
                  </div>
                  <div className="bg-gray-700/30 rounded p-2">
                    <div className="text-xs text-gray-400">Drawdown Volatility</div>
                    <div className="text-lg font-medium">
                      {/* Calculate standard deviation of drawdown values */}
                      {(() => {
                        const avg = drawdownHistory.reduce((sum, item) => sum + item.drawdown, 0) / drawdownHistory.length;
                        const variance = drawdownHistory.reduce((sum, item) => sum + Math.pow(item.drawdown - avg, 2), 0) / drawdownHistory.length;
                        return Math.sqrt(variance).toFixed(2);
                      })()}%
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </TabsContent>

          <TabsContent value="recovery">
            <div className="space-y-4">
              {/* Recovery Metrics */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-gray-800/50 rounded-lg p-4 border border-gray-700"
                >
                  <div className="flex items-center gap-2 mb-2">
                    <Clock className="h-4 w-4 text-green-400" />
                    <div className="text-sm font-medium">Average Recovery Time</div>
                  </div>
                  <div className="text-2xl font-bold">
                    {avgRecoveryTime.toFixed(1)} days
                  </div>
                  <div className="text-xs text-gray-400 mt-1">
                    {avgRecoveryTime < 3
                      ? "Excellent recovery speed"
                      : avgRecoveryTime < 7
                        ? "Good recovery speed"
                        : "Recovery could be improved"}
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                  className="bg-gray-800/50 rounded-lg p-4 border border-gray-700"
                >
                  <div className="flex items-center gap-2 mb-2">
                    <LineChart className="h-4 w-4 text-blue-400" />
                    <div className="text-sm font-medium">Recovery Rate</div>
                  </div>
                  <div className="text-2xl font-bold">
                    {recoveryRate.toFixed(1)}%
                  </div>
                  <div className="text-xs text-gray-400 mt-1">
                    Percentage of trades that contribute to recovery from drawdowns
                  </div>
                </motion.div>
              </div>

              {/* Recovery Strategies */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="bg-gray-800/50 rounded-lg p-4 border border-gray-700"
              >
                <div className="flex items-center gap-2 mb-3">
                  <Shield className="h-4 w-4 text-purple-400" />
                  <div className="text-sm font-medium">Recovery Strategies</div>
                </div>
                <ul className="space-y-3">
                  <li className="flex items-start gap-3">
                    <div className="bg-blue-900/30 rounded-full h-5 w-5 flex items-center justify-center text-xs text-blue-400 flex-shrink-0 mt-0.5">1</div>
                    <div>
                      <div className="text-sm font-medium text-blue-400">Reduce Position Size</div>
                      <div className="text-xs text-gray-400 mt-0.5">
                        During drawdown periods, reduce your position size by 30-50% to limit further losses.
                      </div>
                    </div>
                  </li>
                  <li className="flex items-start gap-3">
                    <div className="bg-blue-900/30 rounded-full h-5 w-5 flex items-center justify-center text-xs text-blue-400 flex-shrink-0 mt-0.5">2</div>
                    <div>
                      <div className="text-sm font-medium text-blue-400">Focus on High-Probability Setups</div>
                      <div className="text-xs text-gray-400 mt-0.5">
                        Only take trades with clear setups and higher probability of success during recovery.
                      </div>
                    </div>
                  </li>
                  <li className="flex items-start gap-3">
                    <div className="bg-blue-900/30 rounded-full h-5 w-5 flex items-center justify-center text-xs text-blue-400 flex-shrink-0 mt-0.5">3</div>
                    <div>
                      <div className="text-sm font-medium text-blue-400">Improve Risk-Reward Ratio</div>
                      <div className="text-xs text-gray-400 mt-0.5">
                        Aim for trades with at least 1:2 risk-reward ratio to recover faster with fewer trades.
                      </div>
                    </div>
                  </li>
                </ul>
              </motion.div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default DrawdownAnalysis;
