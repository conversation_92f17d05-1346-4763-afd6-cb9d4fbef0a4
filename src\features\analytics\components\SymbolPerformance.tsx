import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/common/components/ui/card';
import { useAnalyticsContext } from '../context/AnalyticsContext';
import { BarChart, ChevronDown, ChevronUp, DollarSign, Percent } from 'lucide-react';
import { Button } from '@/common/components/ui/button';
import InfoButton from './InfoButton';

/**
 * Symbol performance component
 *
 * Analyzes trading performance by symbol.
 *
 * @component
 * @example
 * <SymbolPerformance />
 */
const SymbolPerformance: React.FC = () => {
  const { symbolStats } = useAnalyticsContext();
  const [sortBy, setSortBy] = useState<'winRate' | 'pnl' | 'totalTrades'>('winRate');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  // Handle sort change
  const handleSort = (column: 'winRate' | 'pnl' | 'totalTrades') => {
    if (sortBy === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortDirection('desc');
    }
  };

  // Sort symbols
  const sortedSymbols = Object.entries(symbolStats)
    .sort(([, a], [, b]) => {
      if (sortDirection === 'asc') {
        return a[sortBy] - b[sortBy];
      } else {
        return b[sortBy] - a[sortBy];
      }
    });

  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 10 },
    show: { opacity: 1, y: 0 }
  };

  return (
    <Card className="border-gray-800 bg-gray-900/60 backdrop-blur-sm overflow-hidden">
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2">
          <BarChart className="h-5 w-5 text-blue-400" />
          <div className="flex items-center gap-1.5">
            Symbol Performance
            <InfoButton
              content="Analyzes your trading performance by instrument. This helps identify which symbols you trade most profitably and where you might need improvement."
              className="ml-1"
            />
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Sort Controls */}
        <div className="flex gap-2 mb-4">
          <Button
              variant="outline"
              size="sm"
              className={`text-xs ${sortBy === 'winRate' ? 'bg-blue-900/30 border-blue-700' : 'bg-gray-800 border-gray-700'}`}
              onClick={() => handleSort('winRate')}
            >
              Win Rate
              {sortBy === 'winRate' && (
                sortDirection === 'asc' ? <ChevronUp className="h-3 w-3 ml-1" /> : <ChevronDown className="h-3 w-3 ml-1" />
              )}
            </Button>
            <Button
              variant="outline"
              size="sm"
              className={`text-xs ${sortBy === 'pnl' ? 'bg-blue-900/30 border-blue-700' : 'bg-gray-800 border-gray-700'}`}
              onClick={() => handleSort('pnl')}
            >
              P&L
              {sortBy === 'pnl' && (
                sortDirection === 'asc' ? <ChevronUp className="h-3 w-3 ml-1" /> : <ChevronDown className="h-3 w-3 ml-1" />
              )}
            </Button>
            <Button
              variant="outline"
              size="sm"
              className={`text-xs ${sortBy === 'totalTrades' ? 'bg-blue-900/30 border-blue-700' : 'bg-gray-800 border-gray-700'}`}
              onClick={() => handleSort('totalTrades')}
            >
              Volume
              {sortBy === 'totalTrades' && (
                sortDirection === 'asc' ? <ChevronUp className="h-3 w-3 ml-1" /> : <ChevronDown className="h-3 w-3 ml-1" />
              )}
            </Button>
        </div>

        {/* Symbol List */}
        {sortedSymbols.length === 0 ? (
          <div className="text-center py-6 text-gray-400">
            <p>No symbol data available.</p>
          </div>
        ) : (
          <motion.div
            variants={container}
            initial="hidden"
            animate="show"
            className="space-y-2"
          >
            {sortedSymbols.map(([symbol, stats]) => (
              <motion.div
                key={symbol}
                variants={item}
                className="bg-gray-800/50 rounded-lg p-3 border border-gray-700 hover:border-gray-600 transition-colors"
              >
                <div className="flex justify-between items-center">
                  <div className="font-medium">{symbol}</div>
                  <div className="text-xs text-gray-400">{stats.totalTrades} trades</div>
                </div>

                <div className="grid grid-cols-2 gap-4 mt-2">
                  <div className="flex items-center gap-2">
                    <Percent className="h-4 w-4 text-green-400" />
                    <div>
                      <div className="text-sm font-medium">{stats.winRate.toFixed(1)}%</div>
                      <div className="text-xs text-gray-500">Win Rate</div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <DollarSign className={`h-4 w-4 ${stats.pnl >= 0 ? 'text-green-400' : 'text-red-400'}`} />
                    <div>
                      <div className={`text-sm font-medium ${stats.pnl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                        {formatCurrency(stats.pnl)}
                      </div>
                      <div className="text-xs text-gray-500">P&L</div>
                    </div>
                  </div>
                </div>

                {/* Win Rate Progress Bar */}
                <div className="mt-2 h-1.5 bg-gray-700 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-green-500 rounded-full"
                    style={{ width: `${Math.min(stats.winRate, 100)}%` }}
                  ></div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        )}
      </CardContent>
    </Card>
  );
};

export default SymbolPerformance;
