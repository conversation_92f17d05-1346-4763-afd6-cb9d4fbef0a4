import React, { createContext, useContext, useState, useEffect, ReactNode, useMemo } from 'react';
import { useUser } from '@clerk/clerk-react';
import { useChallengeContext } from '@/features/dashboard/context/ChallengeContext';
import { useRealTimeData } from '../hooks/useRealTimeData';
import { socketCacheService } from '@/common/services/socketCacheService';

// Define types for our context
export interface TradeData {
  id: number;
  challengeEntryId: number;
  ctraderAccountId?: string; // Added for filtering by cTrader account
  tradeId: string;
  symbol: string;
  lotSize: number;
  entryTime: string;
  exitTime: string;
  pnl: number;
  duration: number;
  direction?: string;
  openPrice?: number;
  closePrice?: number;
  commission?: number;
  swap?: number;
  grossProfit?: number;
  stopLoss?: number;
  takeProfit?: number;
  comment?: string;
  label?: string;
  status?: string;
}

export interface UserMetrics {
  totalTrades: number;
  winningTrades: number;
  losingTrades: number;
  totalPnl: number;
  pnlPercent: number;
  winRate: number;
  averagePnl: number;
  maxDrawdown: number;
  dailyDrawdown: number;
  averageDuration: number;
  profitFactor: number;
  expectancy: number;
  averageWin: number;
  averageLoss: number;
  largestWin: number;
  largestLoss: number;
  maxConsecutiveWins: number;
  maxConsecutiveLosses: number;
  maxRiskPerTrade: number;
  averageRiskPerTrade: number;
  averageRiskRewardRatio: number;
  sharpeRatio: number;
  sortinoRatio: number;
  tradingDays: number;
  averageTradesPerDay: number;
}

export interface DrawdownUpdate {
  challengeEntryId: number;
  ctraderAccountId?: string; // Added for filtering by cTrader account
  currentDrawdown: number;
  maxDrawdown: number;
  remainingMargin: number;
  timestamp: string;
}

export interface TradePattern {
  name: string;
  description: string;
  occurrences: number;
  winRate: number;
  averagePnl: number;
  trades: number[];
}

export interface PsychologicalInsight {
  type: 'revenge' | 'overtrading' | 'fomo' | 'earlyExit' | 'discipline' | 'consistency';
  severity: 'low' | 'medium' | 'high';
  description: string;
  recommendation: string;
  relatedTrades: number[];
}

// Define filter types
export type AnalyticsFilter =
  | { type: 'all' }
  | { type: 'ctraderAccount', ctraderAccountId: string };

export interface AnalyticsContextType {
  trades: TradeData[];
  metrics: UserMetrics | null;
  drawdown: DrawdownUpdate | null;
  isLoading: boolean;
  error: string | null;
  selectedChallengeId: string | null;
  setSelectedChallengeId: (id: string | null) => void;
  selectedFilter: AnalyticsFilter;
  setSelectedFilter: (filter: AnalyticsFilter) => void;
  symbolStats: Record<string, {
    totalTrades: number;
    winningTrades: number;
    pnl: number;
    winRate: number;
  }>;
  timeStats: Record<string, {
    totalTrades: number;
    winningTrades: number;
    pnl: number;
  }>;
  patterns: TradePattern[];
  psychologicalInsights: PsychologicalInsight[];
  isConnected: boolean;
  lastUpdate: Record<string, Date | null>;
  updateCounts: Record<string, number>;
  refreshData: () => void;
}

// Create context with default values
const AnalyticsContext = createContext<AnalyticsContextType>({
  trades: [],
  metrics: null,
  drawdown: null,
  isLoading: true,
  error: null,
  selectedChallengeId: null,
  setSelectedChallengeId: () => {},
  selectedFilter: { type: 'all' },
  setSelectedFilter: () => {},
  symbolStats: {},
  timeStats: {},
  patterns: [],
  psychologicalInsights: [],
  isConnected: false,
  lastUpdate: { trades: null, metrics: null, drawdown: null },
  updateCounts: { trades: 0, metrics: 0, drawdown: 0 },
  refreshData: () => {},
});

// Provider component
export const AnalyticsProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { user } = useUser();
  const { selectedChallenge } = useChallengeContext();
  const [selectedChallengeId, setSelectedChallengeId] = useState<string | null>(
    selectedChallenge ? selectedChallenge.id : null
  );
  const [selectedFilter, setSelectedFilter] = useState<AnalyticsFilter>({ type: 'all' });
  const [symbolStats, setSymbolStats] = useState<Record<string, any>>({});
  const [timeStats, setTimeStats] = useState<Record<string, any>>({});
  const [patterns, setPatterns] = useState<TradePattern[]>([]);
  const [psychologicalInsights, setPsychologicalInsights] = useState<PsychologicalInsight[]>([]);

  // Initialize socket cache service
  useEffect(() => {
    if (!user) return;
    socketCacheService.init();
  }, [user]);

  // Determine the options for real-time data hook based on selected filter
  const realTimeDataOptions = useMemo(() => {
    if (selectedFilter.type === 'ctraderAccount') {
      // When filtering by cTrader account, we don't filter by challenge entry
      return {
        ctraderAccountId: selectedFilter.ctraderAccountId,
        useCache: true,
        cacheTTL: 5 * 60 * 1000, // 5 minutes cache TTL
        initWithCache: true
      };
    } else {
      // When showing all data or when a specific challenge is selected
      return {
        challengeEntryId: selectedChallengeId ? parseInt(selectedChallengeId) : undefined,
        useCache: true,
        cacheTTL: 5 * 60 * 1000, // 5 minutes cache TTL
        initWithCache: true
      };
    }
  }, [selectedFilter, selectedChallengeId]);

  // Use the real-time data hook
  const {
    trades,
    metrics,
    drawdown,
    isLoading,
    error,
    isConnected,
    lastUpdate,
    updateCounts,
    refreshData
  } = useRealTimeData(realTimeDataOptions);

  // Calculate symbol statistics whenever trades change
  useEffect(() => {
    if (trades.length === 0) return;

    const symbolData: Record<string, any> = {};

    trades.forEach(trade => {
      if (!symbolData[trade.symbol]) {
        symbolData[trade.symbol] = {
          totalTrades: 0,
          winningTrades: 0,
          pnl: 0,
          winRate: 0,
        };
      }

      symbolData[trade.symbol].totalTrades += 1;
      if (trade.pnl > 0) {
        symbolData[trade.symbol].winningTrades += 1;
      }
      symbolData[trade.symbol].pnl += trade.pnl;
    });

    // Calculate win rates
    Object.keys(symbolData).forEach(symbol => {
      const stats = symbolData[symbol];
      stats.winRate = stats.totalTrades > 0
        ? (stats.winningTrades / stats.totalTrades) * 100
        : 0;
    });

    setSymbolStats(symbolData);
  }, [trades]);

  // Calculate time-based statistics
  useEffect(() => {
    if (trades.length === 0) return;

    const timeData: Record<string, any> = {};

    // Group by hour of day
    trades.forEach(trade => {
      const hour = new Date(trade.entryTime).getHours();
      const hourKey = `${hour}:00`;

      if (!timeData[hourKey]) {
        timeData[hourKey] = {
          totalTrades: 0,
          winningTrades: 0,
          pnl: 0,
        };
      }

      timeData[hourKey].totalTrades += 1;
      if (trade.pnl > 0) {
        timeData[hourKey].winningTrades += 1;
      }
      timeData[hourKey].pnl += trade.pnl;
    });

    setTimeStats(timeData);
  }, [trades]);

  // Detect trading patterns
  useEffect(() => {
    if (trades.length < 5) return; // Need enough trades to detect patterns

    // Sort trades by time
    const sortedTrades = [...trades].sort((a, b) =>
      new Date(a.entryTime).getTime() - new Date(b.entryTime).getTime()
    );

    const detectedPatterns: TradePattern[] = [];

    // Pattern 1: Consecutive wins (3 or more)
    let winStreak = 0;
    let winStreakTrades: number[] = [];

    sortedTrades.forEach((trade, index) => {
      if (trade.pnl > 0) {
        winStreak++;
        winStreakTrades.push(trade.id);

        if (winStreak >= 3 && (index === sortedTrades.length - 1 || sortedTrades[index + 1].pnl <= 0)) {
          // End of a win streak
          detectedPatterns.push({
            name: `Win Streak (${winStreak})`,
            description: `A series of ${winStreak} consecutive winning trades`,
            occurrences: 1,
            winRate: 100,
            averagePnl: winStreakTrades.reduce((sum, id) => {
              const trade = trades.find(t => t.id === id);
              return sum + (trade?.pnl || 0);
            }, 0) / winStreak,
            trades: [...winStreakTrades]
          });

          winStreak = 0;
          winStreakTrades = [];
        }
      } else {
        winStreak = 0;
        winStreakTrades = [];
      }
    });

    // Pattern 2: Same symbol consecutive trades
    const symbolGroups: Record<string, number[]> = {};

    sortedTrades.forEach(trade => {
      if (!symbolGroups[trade.symbol]) {
        symbolGroups[trade.symbol] = [];
      }
      symbolGroups[trade.symbol].push(trade.id);
    });

    Object.entries(symbolGroups).forEach(([symbol, tradeIds]) => {
      if (tradeIds.length >= 3) {
        const patternTrades = tradeIds.map(id => trades.find(t => t.id === id)!);
        const winningTrades = patternTrades.filter(t => t.pnl > 0).length;

        detectedPatterns.push({
          name: `${symbol} Focus`,
          description: `A series of ${tradeIds.length} trades on ${symbol}`,
          occurrences: tradeIds.length,
          winRate: (winningTrades / tradeIds.length) * 100,
          averagePnl: patternTrades.reduce((sum, t) => sum + t.pnl, 0) / tradeIds.length,
          trades: tradeIds
        });
      }
    });

    // Pattern 3: Time-based patterns (trading at specific hours)
    const hourGroups: Record<string, number[]> = {};

    sortedTrades.forEach(trade => {
      const hour = new Date(trade.entryTime).getHours();
      const hourKey = `${hour}:00`;

      if (!hourGroups[hourKey]) {
        hourGroups[hourKey] = [];
      }
      hourGroups[hourKey].push(trade.id);
    });

    Object.entries(hourGroups).forEach(([hour, tradeIds]) => {
      if (tradeIds.length >= 3) {
        const patternTrades = tradeIds.map(id => trades.find(t => t.id === id)!);
        const winningTrades = patternTrades.filter(t => t.pnl > 0).length;

        detectedPatterns.push({
          name: `${hour} Trading`,
          description: `Regular trading at ${hour}`,
          occurrences: tradeIds.length,
          winRate: (winningTrades / tradeIds.length) * 100,
          averagePnl: patternTrades.reduce((sum, t) => sum + t.pnl, 0) / tradeIds.length,
          trades: tradeIds
        });
      }
    });

    setPatterns(detectedPatterns);
  }, [trades]);

  // Detect psychological insights
  useEffect(() => {
    if (trades.length < 5) return; // Need enough trades for meaningful insights

    const insights: PsychologicalInsight[] = [];

    // Sort trades by time
    const sortedTrades = [...trades].sort((a, b) =>
      new Date(a.entryTime).getTime() - new Date(b.entryTime).getTime()
    );

    // Insight 1: Revenge trading (losing trade followed by larger trade within short time)
    for (let i = 0; i < sortedTrades.length - 1; i++) {
      const currentTrade = sortedTrades[i];
      const nextTrade = sortedTrades[i + 1];

      if (currentTrade.pnl < 0) {
        const timeDiff = new Date(nextTrade.entryTime).getTime() - new Date(currentTrade.exitTime).getTime();
        const timeDiffMinutes = timeDiff / (1000 * 60);

        if (timeDiffMinutes < 15 && nextTrade.lotSize > currentTrade.lotSize) {
          insights.push({
            type: 'revenge',
            severity: 'medium',
            description: 'Possible revenge trading detected - losing trade followed by larger position within 15 minutes',
            recommendation: 'Consider implementing a cooling-off period after losing trades',
            relatedTrades: [currentTrade.id, nextTrade.id]
          });
        }
      }
    }

    // Insight 2: Overtrading (too many trades in short period)
    const tradesByDay: Record<string, number[]> = {};

    sortedTrades.forEach(trade => {
      const date = new Date(trade.entryTime).toISOString().split('T')[0];

      if (!tradesByDay[date]) {
        tradesByDay[date] = [];
      }
      tradesByDay[date].push(trade.id);
    });

    Object.entries(tradesByDay).forEach(([date, tradeIds]) => {
      if (tradeIds.length > 10) {
        insights.push({
          type: 'overtrading',
          severity: 'high',
          description: `Possible overtrading detected - ${tradeIds.length} trades on ${date}`,
          recommendation: 'Consider setting a daily trade limit to prevent overtrading',
          relatedTrades: tradeIds
        });
      }
    });

    // Insight 3: Early exits (trades closed too quickly with small profit)
    sortedTrades.forEach(trade => {
      if (trade.pnl > 0 && trade.duration < 300 && trade.pnl < trade.lotSize * 5) {
        insights.push({
          type: 'earlyExit',
          severity: 'low',
          description: 'Possible early exit - trade closed quickly with small profit',
          recommendation: 'Consider letting profitable trades run longer to maximize gains',
          relatedTrades: [trade.id]
        });
      }
    });

    setPsychologicalInsights(insights);
  }, [trades]);

  const value = {
    trades,
    metrics,
    drawdown,
    isLoading,
    error,
    selectedChallengeId,
    setSelectedChallengeId,
    selectedFilter,
    setSelectedFilter,
    symbolStats,
    timeStats,
    patterns,
    psychologicalInsights,
    isConnected,
    lastUpdate,
    updateCounts,
    refreshData
  };

  return (
    <AnalyticsContext.Provider value={value}>
      {children}
    </AnalyticsContext.Provider>
  );
};

// Custom hook to use the analytics context
export const useAnalyticsContext = () => useContext(AnalyticsContext);
