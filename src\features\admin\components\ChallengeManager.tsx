/**
 * Challenge Manager Component
 * @description Component for managing challenges in the admin panel
 * @version 1.0.0
 * @status stable
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Search,
  Filter,
  RefreshCw,
  Plus,
  Edit,
  Trash2,
  Eye,
  Calendar,
  Trophy,
  ArrowUpDown,
  MoreHorizontal,
  Clock,
  DollarSign,
  Users,
  AlertTriangle,
  AlertCircle
} from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/common/components/ui/card';
import { Button } from '@/common/components/ui/button';
import { Input } from '@/common/components/ui/input';
import { Badge } from '@/common/components/ui/badge';
import { Skeleton } from '@/common/components/ui/skeleton';
import { useToast } from '@/common/components/ui/use-toast';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/common/components/ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/common/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/common/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/common/components/ui/tabs';
import { Progress } from '@/common/components/ui/progress';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/common/components/ui/alert-dialog';
import { useChallenges } from '../hooks/useAdminData';
import ChallengeCreationDialog from './ChallengeCreationDialog';
import ChallengeDetailDialog from './ChallengeDetailDialog';
import ChallengeParticipantsDialog from './ChallengeParticipantsDialog';
import ChallengeEditDialog from './ChallengeEditDialog';

// Challenge type definition
interface Challenge {
  id: number;
  type: 'daily' | 'weekly' | 'monthly';
  startDate: string;
  endDate: string;
  prizePool: number;
  entryFee: number;
  status: 'upcoming' | 'active' | 'completed' | 'cancelled';
  participantCount: number;
  ruleset: {
    maxDrawdown: number;
    maxDailyDrawdown?: number;
    maxRiskPerTrade: number;
    minTrades: number;
    startingBalance: number;
  };
}

/**
 * Challenge manager component
 */
const ChallengeManager: React.FC = () => {
  const { toast } = useToast();
  const [loading, setLoading] = useState<boolean>(true);
  const [challenges, setChallenges] = useState<Challenge[]>([]);
  const [filteredChallenges, setFilteredChallenges] = useState<Challenge[]>([]);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('startDate');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [activeTab, setActiveTab] = useState<string>('all');
  const [selectedChallenge, setSelectedChallenge] = useState<Challenge | null>(null);
  const [detailDialogOpen, setDetailDialogOpen] = useState<boolean>(false);
  const [participantsDialogOpen, setParticipantsDialogOpen] = useState<boolean>(false);
  const [editDialogOpen, setEditDialogOpen] = useState<boolean>(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState<boolean>(false);
  const [deleteLoading, setDeleteLoading] = useState<boolean>(false);

  // Ref to track pending refresh timeouts to prevent stacking
  const refreshTimeoutRef = React.useRef<NodeJS.Timeout | null>(null);

  // Use the challenges hook
  const {
    challenges: apiChallenges,
    loading: apiLoading,
    error,
    fetchChallenges,
    createChallenge,
    updateChallenge,
    deleteChallenge,
    setParams
  } = useChallenges();

  // Handle view details
  const handleViewDetails = useCallback((challenge: Challenge) => {
    console.log('Opening details for challenge:', challenge.id);
    try {
      setSelectedChallenge(challenge);
      setDetailDialogOpen(true);
    } catch (error) {
      console.error('Error opening challenge details:', error);
      toast({
        title: "Error",
        description: "Failed to open challenge details",
        variant: "destructive",
      });
    }
  }, [toast]);

  // Handle view participants
  const handleViewParticipants = useCallback((challenge: Challenge) => {
    console.log('Opening participants for challenge:', challenge.id);
    try {
      setSelectedChallenge(challenge);
      setParticipantsDialogOpen(true);
    } catch (error) {
      console.error('Error opening participants dialog:', error);
      toast({
        title: "Error",
        description: "Failed to open participants dialog",
        variant: "destructive",
      });
    }
  }, [toast]);

  // Handle edit challenge
  const handleEditChallenge = useCallback((challenge: Challenge) => {
    console.log('Opening edit dialog for challenge:', challenge.id);
    try {
      setSelectedChallenge(challenge);
      setEditDialogOpen(true);
    } catch (error) {
      console.error('Error opening edit dialog:', error);
      toast({
        title: "Error",
        description: "Failed to open edit dialog",
        variant: "destructive",
      });
    }
  }, [toast]);

  // Handle delete challenge
  const handleDeleteChallenge = useCallback((challenge: Challenge) => {
    console.log('Opening delete dialog for challenge:', challenge.id);
    try {
      setSelectedChallenge(challenge);
      setDeleteDialogOpen(true);
    } catch (error) {
      console.error('Error opening delete dialog:', error);
      toast({
        title: "Error",
        description: "Failed to open delete dialog",
        variant: "destructive",
      });
    }
  }, [toast]);

  // Handle challenge created
  const handleChallengeCreated = useCallback((challenge: any) => {
    toast({
      title: "Challenge Created",
      description: `${challenge.name} has been created successfully.`,
    });

    // Clear any pending refresh timeout
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current);
    }

    // Refresh challenges after creation with delay to prevent multiple calls
    refreshTimeoutRef.current = setTimeout(() => {
      fetchChallenges();
      refreshTimeoutRef.current = null;
    }, 100);
  }, [toast, fetchChallenges]);

  // Handle challenge updated
  const handleChallengeUpdated = useCallback((updatedChallenge: Challenge) => {
    // Show success message
    toast({
      title: "Challenge Updated",
      description: `${updatedChallenge.name || 'Challenge'} has been updated successfully.`,
    });

    // Clear any pending refresh timeout
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current);
    }

    // Refresh challenges after a short delay to prevent multiple calls
    refreshTimeoutRef.current = setTimeout(() => {
      fetchChallenges();
      refreshTimeoutRef.current = null;
    }, 100);
  }, [toast, fetchChallenges]);

  // Handle challenge deletion
  const handleConfirmDelete = async () => {
    if (!selectedChallenge) {
      console.warn('No challenge selected for deletion');
      return;
    }

    try {
      setDeleteLoading(true);
      console.log('Starting challenge deletion for ID:', selectedChallenge.id);

      // Delete challenge
      const result = await deleteChallenge(selectedChallenge.id);

      if (result) {
        // Show success message
        toast({
          title: "Challenge Deleted",
          description: `Challenge #${selectedChallenge.id} has been deleted successfully.`,
        });

        // Close dialog first
        setDeleteDialogOpen(false);
        setSelectedChallenge(null);

        // Clear any pending refresh timeout
        if (refreshTimeoutRef.current) {
          clearTimeout(refreshTimeoutRef.current);
        }

        // Refresh challenges after a short delay
        refreshTimeoutRef.current = setTimeout(() => {
          fetchChallenges();
          refreshTimeoutRef.current = null;
        }, 100);
      } else {
        // Handle deletion failure - the error message is already shown by the API service
        console.log('Challenge deletion failed, error already shown to user');
      }
    } catch (error) {
      console.error('Error deleting challenge:', error);
      toast({
        title: "Error",
        description: "An unexpected error occurred while deleting the challenge. Please try again.",
        variant: "destructive",
      });
    } finally {
      setDeleteLoading(false);
    }
  };

  // Set challenges when API data is loaded
  useEffect(() => {
    if (apiChallenges && Array.isArray(apiChallenges)) {
      console.log('Setting challenges from API:', apiChallenges.length);
      setChallenges(apiChallenges);
    }
  }, [apiChallenges]);

  // Set loading state from API
  useEffect(() => {
    setLoading(apiLoading);
  }, [apiLoading]);

  // Cleanup effect to clear pending timeouts
  useEffect(() => {
    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, []);

  // Filter and sort challenges - memoized to prevent infinite loops
  useEffect(() => {
    console.log('Filtering challenges, current count:', challenges.length);

    if (!Array.isArray(challenges)) {
      console.warn('Challenges is not an array:', challenges);
      setFilteredChallenges([]);
      return;
    }

    let result = [...challenges];

    // Apply tab filter
    if (activeTab !== 'all') {
      result = result.filter(challenge => challenge.status === activeTab);
    }

    // Apply type filter
    if (typeFilter !== 'all') {
      result = result.filter(challenge => challenge.type === typeFilter);
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      result = result.filter(challenge => challenge.status === statusFilter);
    }

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      result = result.filter(challenge =>
        (challenge.type && challenge.type.toLowerCase().includes(term)) ||
        (challenge.id && challenge.id.toString().includes(term))
      );
    }

    // Apply sorting
    result.sort((a, b) => {
      let valueA, valueB;

      switch (sortBy) {
        case 'type':
          valueA = a.type || '';
          valueB = b.type || '';
          break;
        case 'prizePool':
          valueA = a.prizePool || 0;
          valueB = b.prizePool || 0;
          break;
        case 'entryFee':
          valueA = a.entryFee || 0;
          valueB = b.entryFee || 0;
          break;
        case 'participantCount':
          valueA = a.participantCount || 0;
          valueB = b.participantCount || 0;
          break;
        case 'startDate':
        default:
          valueA = a.startDate ? new Date(a.startDate).getTime() : 0;
          valueB = b.startDate ? new Date(b.startDate).getTime() : 0;
          break;
      }

      if (sortOrder === 'asc') {
        return valueA > valueB ? 1 : -1;
      } else {
        return valueA < valueB ? 1 : -1;
      }
    });

    console.log('Filtered challenges count:', result.length);
    setFilteredChallenges(result);
  }, [challenges, searchTerm, typeFilter, statusFilter, sortBy, sortOrder, activeTab]);

  // Toggle sort order
  const toggleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  // Calculate challenge progress
  const calculateProgress = (startDate: string, endDate: string) => {
    const start = new Date(startDate).getTime();
    const end = new Date(endDate).getTime();
    const now = new Date().getTime();

    if (now < start) return 0;
    if (now > end) return 100;

    const total = end - start;
    const elapsed = now - start;
    return Math.round((elapsed / total) * 100);
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Get status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'upcoming':
        return 'outline';
      case 'completed':
        return 'secondary';
      case 'cancelled':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  // Check if a challenge can be deleted
  const canDeleteChallenge = (challenge: Challenge) => {
    // Can only delete upcoming challenges with no participants
    return challenge.status === 'upcoming' && challenge.participantCount === 0;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Challenge Management</h1>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
            onClick={() => fetchChallenges()}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            {loading ? 'Loading...' : 'Refresh'}
          </Button>
          <ChallengeCreationDialog
            onChallengeCreated={handleChallengeCreated}
            trigger={
              <Button
                size="sm"
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Create Challenge
              </Button>
            }
          />
        </div>
      </div>

      {/* Challenge Detail Dialog */}
      <ChallengeDetailDialog
        challenge={selectedChallenge}
        open={detailDialogOpen}
        onOpenChange={setDetailDialogOpen}
      />

      {/* Challenge Participants Dialog */}
      <ChallengeParticipantsDialog
        challenge={selectedChallenge}
        open={participantsDialogOpen}
        onOpenChange={setParticipantsDialogOpen}
      />

      {/* Challenge Edit Dialog */}
      <ChallengeEditDialog
        challenge={selectedChallenge}
        open={editDialogOpen}
        onOpenChange={setEditDialogOpen}
        onChallengeUpdated={handleChallengeUpdated}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent className="bg-forex-darker border-forex-border text-white">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-white flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-red-400" />
              Delete Challenge
            </AlertDialogTitle>
            <AlertDialogDescription className="text-white/80">
              Are you sure you want to delete this challenge? This action cannot be undone.
            </AlertDialogDescription>
            {selectedChallenge && (
              <div className="mt-2 p-3 bg-forex-dark rounded-md">
                <div className="font-medium">Challenge #{selectedChallenge.id}</div>
                <div className="text-sm text-gray-400">Type: {selectedChallenge.type}</div>
                <div className="text-sm text-gray-400">
                  Dates: {formatDate(selectedChallenge.startDate)} - {formatDate(selectedChallenge.endDate)}
                </div>
              </div>
            )}
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="bg-transparent border-forex-border text-white hover:bg-forex-dark hover:text-white">
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                handleConfirmDelete();
              }}
              disabled={deleteLoading}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              {deleteLoading ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                'Delete Challenge'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Filters */}
      <Card className="bg-forex-darker border-forex-border">
        <CardHeader className="pb-3">
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-forex-muted" />
                <Input
                  placeholder="Search by challenge ID or type..."
                  className="pl-9"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Select
                value={typeFilter}
                onValueChange={setTypeFilter}
              >
                <SelectTrigger className="w-[150px]">
                  <div className="flex items-center gap-2">
                    <Trophy className="h-4 w-4" />
                    <SelectValue placeholder="Challenge type" />
                  </div>
                </SelectTrigger>
                <SelectContent className="bg-forex-darker border-forex-border">
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                </SelectContent>
              </Select>
              <Select
                value={statusFilter}
                onValueChange={setStatusFilter}
              >
                <SelectTrigger className="w-[150px]">
                  <div className="flex items-center gap-2">
                    <Filter className="h-4 w-4" />
                    <SelectValue placeholder="Status" />
                  </div>
                </SelectTrigger>
                <SelectContent className="bg-forex-darker border-forex-border">
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="upcoming">Upcoming</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Challenges Tabs */}
      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-4 mb-4">
          <TabsTrigger value="all">All Challenges</TabsTrigger>
          <TabsTrigger value="active">Active</TabsTrigger>
          <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
          <TabsTrigger value="completed">Completed</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="mt-0">
          {renderChallengesTable()}
        </TabsContent>

        <TabsContent value="active" className="mt-0">
          {renderChallengesTable()}
        </TabsContent>

        <TabsContent value="upcoming" className="mt-0">
          {renderChallengesTable()}
        </TabsContent>

        <TabsContent value="completed" className="mt-0">
          {renderChallengesTable()}
        </TabsContent>
      </Tabs>
    </div>
  );

  // Helper function to render challenges table
  function renderChallengesTable() {
    return (
      <Card className="bg-forex-darker border-forex-border">
        <CardHeader className="pb-3">
          <CardTitle>Challenges</CardTitle>
          <CardDescription>
            {filteredChallenges.length} {filteredChallenges.length === 1 ? 'challenge' : 'challenges'} found
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, index) => (
                <div key={index} className="flex items-center space-x-4">
                  <Skeleton className="h-12 w-12 rounded-full" />
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-[250px]" />
                    <Skeleton className="h-4 w-[200px]" />
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="rounded-md border border-forex-border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[80px]">ID</TableHead>
                    <TableHead>
                      <Button
                        variant="ghost"
                        onClick={() => toggleSort('type')}
                        className="flex items-center gap-1 p-0 hover:bg-transparent"
                      >
                        Type
                        <ArrowUpDown className="h-4 w-4" />
                      </Button>
                    </TableHead>
                    <TableHead>
                      <Button
                        variant="ghost"
                        onClick={() => toggleSort('startDate')}
                        className="flex items-center gap-1 p-0 hover:bg-transparent"
                      >
                        Dates
                        <ArrowUpDown className="h-4 w-4" />
                      </Button>
                    </TableHead>
                    <TableHead>
                      <Button
                        variant="ghost"
                        onClick={() => toggleSort('prizePool')}
                        className="flex items-center gap-1 p-0 hover:bg-transparent"
                      >
                        Prize Pool
                        <ArrowUpDown className="h-4 w-4" />
                      </Button>
                    </TableHead>
                    <TableHead>
                      <Button
                        variant="ghost"
                        onClick={() => toggleSort('entryFee')}
                        className="flex items-center gap-1 p-0 hover:bg-transparent"
                      >
                        Entry Fee
                        <ArrowUpDown className="h-4 w-4" />
                      </Button>
                    </TableHead>
                    <TableHead>
                      <Button
                        variant="ghost"
                        onClick={() => toggleSort('participantCount')}
                        className="flex items-center gap-1 p-0 hover:bg-transparent"
                      >
                        Participants
                        <ArrowUpDown className="h-4 w-4" />
                      </Button>
                    </TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Progress</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredChallenges.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={9} className="text-center py-8 text-forex-muted">
                        No challenges found
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredChallenges.map((challenge) => (
                      <TableRow key={challenge.id}>
                        <TableCell className="font-medium">{challenge.id}</TableCell>
                        <TableCell>
                          <Badge variant="outline" className="capitalize">
                            {challenge.type}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <span className="text-xs text-forex-muted">Start: {formatDate(challenge.startDate)}</span>
                            <span className="text-xs text-forex-muted">End: {formatDate(challenge.endDate)}</span>
                          </div>
                        </TableCell>
                        <TableCell>${challenge.prizePool}</TableCell>
                        <TableCell>${challenge.entryFee}</TableCell>
                        <TableCell>{challenge.participantCount}</TableCell>
                        <TableCell>
                          <Badge variant={getStatusBadgeVariant(challenge.status)}>
                            {challenge.status.charAt(0).toUpperCase() + challenge.status.slice(1)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {challenge.status === 'active' && (
                            <div className="w-full">
                              <Progress value={calculateProgress(challenge.startDate, challenge.endDate)} className="h-2" />
                              <span className="text-xs text-forex-muted mt-1 block">
                                {calculateProgress(challenge.startDate, challenge.endDate)}%
                              </span>
                            </div>
                          )}
                          {challenge.status === 'upcoming' && (
                            <span className="text-xs text-forex-muted">Not started</span>
                          )}
                          {challenge.status === 'completed' && (
                            <span className="text-xs text-forex-muted">Completed</span>
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent className="bg-forex-darker border-forex-border">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.preventDefault();
                                  handleViewDetails(challenge);
                                }}
                              >
                                <Eye className="h-4 w-4 mr-2" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.preventDefault();
                                  handleViewParticipants(challenge);
                                }}
                              >
                                <Users className="h-4 w-4 mr-2" />
                                View Participants
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.preventDefault();
                                  handleEditChallenge(challenge);
                                }}
                              >
                                <Edit className="h-4 w-4 mr-2" />
                                Edit Challenge
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.preventDefault();
                                  if (canDeleteChallenge(challenge)) {
                                    handleDeleteChallenge(challenge);
                                  } else {
                                    toast({
                                      title: "Cannot Delete Challenge",
                                      description: "Only upcoming challenges with no participants can be deleted.",
                                      variant: "destructive",
                                    });
                                  }
                                }}
                                className={`${
                                  canDeleteChallenge(challenge)
                                    ? "text-red-400 hover:text-red-300 hover:bg-red-500/10"
                                    : "text-gray-500 cursor-not-allowed"
                                }`}
                                disabled={!canDeleteChallenge(challenge)}
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Delete Challenge
                                {!canDeleteChallenge(challenge) && (
                                  <span className="ml-2 text-xs">(Not allowed)</span>
                                )}
                              </DropdownMenuItem>
                              {challenge.status === 'completed' && (
                                <DropdownMenuItem>
                                  <DollarSign className="h-4 w-4 mr-2" />
                                  Manage Payouts
                                </DropdownMenuItem>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }
};

export default ChallengeManager;
