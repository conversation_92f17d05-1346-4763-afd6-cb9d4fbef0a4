// Export components from dashboard feature
export { default as DashboardLayout } from './components/DashboardLayout';
// DashboardHeader removed during authentication cleanup
export { default as DashboardSidebar } from './components/DashboardSidebar';
export { default as MobileNavigation } from './components/MobileNavigation';
// PerformanceMetrics component removed during cleanup
export { default as EquityChart } from './components/EquityChart';
export { default as ActiveChallenges } from './components/ActiveChallenges';
export { default as RecentTrades } from './components/RecentTrades';
export { default as WalletStatus } from './components/WalletStatus';
export { default as ConnectedAccounts } from './components/ConnectedAccounts';
export { default as ChallengeHUD } from './components/ChallengeHUD';
export { default as AchievementMilestones } from './components/AchievementMilestones';
export { default as ChallengeLeaderboard } from './components/ChallengeLeaderboard';
export { default as MotivationPrompts } from './components/MotivationPrompts';
export { default as AchievementCelebration } from './components/AchievementCelebration';

// Journal components
export { default as JournalEntryList } from './components/journal/JournalEntryList';
export { default as JournalEntryEditor } from './components/journal/JournalEntryEditor';
export { default as ChallengeSelector } from './components/ChallengeSelector';
export { default as NotificationsPanel } from './components/NotificationsPanel';
export { default as DynamicBackground } from './components/DynamicBackground';
export { default as GradientText } from './components/GradientText';

// Export types and interfaces
export type { Achievement } from './components/AchievementCelebration';

// Export hooks
export { useAchievements } from './hooks/useAchievements';

// Export context
export { default as ChallengeContext, useChallengeContext } from './context/ChallengeContext';
export type { Challenge, ChallengeType, ChallengeStatus } from './context/ChallengeContext';

// Export pages
export { default as SettingsPage } from './pages/SettingsPage';
export { default as HelpPage } from './pages/HelpPage';
export { default as LeaderboardPage } from './pages/LeaderboardPage';
