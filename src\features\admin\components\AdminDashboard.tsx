/**
 * Admin Dashboard Component
 * @description Main dashboard component for the admin panel
 * @version 1.0.0
 * @status stable
 */

import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  Users,
  Trophy,
  Wallet,
  Bell,
  BarChart3,
  Activity,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  RefreshCw
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/common/components/ui/card';
import { Button } from '@/common/components/ui/button';
import { Skeleton } from '@/common/components/ui/skeleton';
import { useToast } from '@/common/components/ui/use-toast';
import { apiService } from '@/common/services/api';

/**
 * Admin dashboard component
 * @returns JSX element
 */
const AdminDashboard: React.FC = () => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const [stats, setStats] = useState({
    totalUsers: 0,
    activeChallenges: 0,
    pendingPayouts: 0,
    totalWalletCredits: 0,
    recentTrades: 0,
    activeConnections: 0,
    disqualifiedUsers: 0,
    systemStatus: 'operational',
    trends: {
      users: { change: '+0%', direction: 'up' },
      challenges: { change: '+0', direction: 'up' },
      payouts: { change: '+0', direction: 'up' },
      credits: { change: '+$0', direction: 'up' }
    }
  });

  // Function to fetch dashboard stats
  const fetchDashboardStats = async (isRefreshing = false) => {
    try {
      if (isRefreshing) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      console.log('Fetching admin dashboard stats...');

      // Fetch real data from the API
      try {
        const response = await apiService.getAdminDashboardStats();
        console.log('Admin dashboard API response:', response);

        if (response) {
          setStats({
            totalUsers: response.totalUsers || 0,
            activeChallenges: response.activeChallenges || 0,
            pendingPayouts: response.pendingPayouts || 0,
            totalWalletCredits: response.totalWalletCredits || 0,
            recentTrades: response.recentTrades || 0,
            activeConnections: response.activeConnections || 0,
            disqualifiedUsers: response.disqualifiedUsers || 0,
            systemStatus: response.systemStatus || 'operational',
            trends: response.trends || {
              users: { change: '+0%', direction: 'up' },
              challenges: { change: '+0', direction: 'up' },
              payouts: { change: '+0', direction: 'up' },
              credits: { change: '+$0', direction: 'up' }
            }
          });
        } else {
          console.warn('Empty response from API, setting default values');
          // Set default values when API returns empty response
          setStats({
            totalUsers: 0,
            activeChallenges: 0,
            pendingPayouts: 0,
            totalWalletCredits: 0,
            recentTrades: 0,
            activeConnections: 0,
            disqualifiedUsers: 0,
            systemStatus: 'operational',
            trends: {
              users: { change: '0%', direction: 'neutral' },
              challenges: { change: '0', direction: 'neutral' },
              payouts: { change: '0', direction: 'neutral' },
              credits: { change: '$0', direction: 'neutral' }
            }
          });
        }

        // Update last updated timestamp
        setLastUpdated(new Date());

        if (isRefreshing) {
          setRefreshing(false);
          toast({
            title: 'Refreshed',
            description: 'Dashboard statistics updated',
          });
        } else {
          setLoading(false);
        }

        return; // Exit early since we've handled everything
      } catch (error) {
        console.error('Error fetching dashboard stats:', error);
        // Set error state with default values
        setStats({
          totalUsers: 0,
          activeChallenges: 0,
          pendingPayouts: 0,
          totalWalletCredits: 0,
          recentTrades: 0,
          activeConnections: 0,
          disqualifiedUsers: 0,
          systemStatus: 'error',
          trends: {
            users: { change: 'N/A', direction: 'neutral' },
            challenges: { change: 'N/A', direction: 'neutral' },
            payouts: { change: 'N/A', direction: 'neutral' },
            credits: { change: 'N/A', direction: 'neutral' }
          }
        });

        // Show error toast
        toast({
          title: 'Error',
          description: 'Failed to load dashboard statistics',
          variant: 'destructive',
        });
      }

      // Make sure loading states are reset in case of errors
      if (isRefreshing) {
        setRefreshing(false);
      } else {
        setLoading(false);
      }
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);

      // More detailed error logging
      if (error instanceof Error) {
        console.error('Error details:', error.message);
        console.error('Error stack:', error.stack);
      }

      toast({
        title: 'Error',
        description: `Failed to load dashboard statistics: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: 'destructive',
      });

      if (isRefreshing) {
        setRefreshing(false);
      } else {
        setLoading(false);
      }
    }
  };

  // Handle manual refresh
  const handleRefresh = () => {
    fetchDashboardStats(true);
  };

  // Fetch dashboard stats on component mount
  useEffect(() => {
    fetchDashboardStats();

    // Set up auto-refresh every 60 seconds (reduced frequency to prevent performance issues)
    const refreshInterval = setInterval(() => {
      // Only refresh if the component is still mounted and visible
      if (document.visibilityState === 'visible') {
        fetchDashboardStats(false);
      }
    }, 60000);

    // Clean up interval on component unmount
    return () => clearInterval(refreshInterval);
  }, []); // Remove toast dependency to prevent re-creating intervals

  // Quick action cards
  const quickActions = [
    {
      title: 'User Management',
      description: 'Manage users, view profiles, and adjust wallet credits',
      icon: <Users className="h-6 w-6" />,
      path: '/admin/users',
      color: 'bg-blue-500'
    },
    {
      title: 'Challenge Management',
      description: 'Create, edit, and monitor trading challenges',
      icon: <Trophy className="h-6 w-6" />,
      path: '/admin/challenges',
      color: 'bg-green-500'
    },
    {
      title: 'Prize Distribution',
      description: 'Approve and process payouts to winners',
      icon: <DollarSign className="h-6 w-6" />,
      path: '/admin/prize-distributions',
      color: 'bg-purple-500'
    },
    {
      title: 'Wallet Management',
      description: 'View and manage wallet transactions',
      icon: <Wallet className="h-6 w-6" />,
      path: '/admin/transactions',
      color: 'bg-amber-500'
    },
    {
      title: 'Notifications',
      description: 'Send and manage system notifications',
      icon: <Bell className="h-6 w-6" />,
      path: '/admin/notifications',
      color: 'bg-red-500'
    },
    {
      title: 'Metrics & Analytics',
      description: 'View platform performance metrics',
      icon: <BarChart3 className="h-6 w-6" />,
      path: '/admin/metrics',
      color: 'bg-indigo-500'
    },
  ];

  // Stat cards
  const statCards = [
    {
      title: 'Total Users',
      value: stats.totalUsers,
      icon: <Users className="h-5 w-5" />,
      change: stats.trends?.users?.change || '+0%',
      trend: stats.trends?.users?.direction || 'up'
    },
    {
      title: 'Active Challenges',
      value: stats.activeChallenges,
      icon: <Trophy className="h-5 w-5" />,
      change: stats.trends?.challenges?.change || '+0',
      trend: stats.trends?.challenges?.direction || 'up'
    },
    {
      title: 'Pending Payouts',
      value: stats.pendingPayouts,
      icon: <DollarSign className="h-5 w-5" />,
      change: stats.trends?.payouts?.change || '+0',
      trend: stats.trends?.payouts?.direction || 'up'
    },
    {
      title: 'Total Wallet Credits',
      value: `$${stats.totalWalletCredits.toLocaleString()}`,
      icon: <Wallet className="h-5 w-5" />,
      change: stats.trends?.credits?.change || '+$0',
      trend: stats.trends?.credits?.direction || 'up'
    },
  ];

  // System status cards
  const systemStatusCards = [
    {
      title: 'WebSocket Connections',
      value: stats.activeConnections,
      status: 'operational',
      icon: <Activity className="h-5 w-5" />
    },
    {
      title: 'Recent Trades',
      value: stats.recentTrades,
      status: 'operational',
      icon: <BarChart3 className="h-5 w-5" />
    },
    {
      title: 'Disqualified Users',
      value: stats.disqualifiedUsers,
      status: 'warning',
      icon: <AlertTriangle className="h-5 w-5" />
    },
    {
      title: 'System Status',
      value: stats.systemStatus === 'operational' ? 'Operational' : 'Issues Detected',
      status: stats.systemStatus === 'operational' ? 'operational' : 'error',
      icon: stats.systemStatus === 'operational' ? <CheckCircle className="h-5 w-5" /> : <AlertTriangle className="h-5 w-5" />
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Admin Dashboard</h1>
        <Button
          variant="outline"
          size="sm"
          className="flex items-center gap-2"
          onClick={handleRefresh}
          disabled={refreshing}
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          {refreshing ? 'Refreshing...' : 'Refresh Data'}
        </Button>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {statCards.map((stat, index) => (
          <Card key={index} className="bg-forex-darker border-forex-border">
            <CardHeader className="pb-2">
              <div className="flex justify-between items-center">
                <CardTitle className="text-sm font-medium text-forex-muted">{stat.title}</CardTitle>
                <div className="p-2 rounded-full bg-forex-dark/50">{stat.icon}</div>
              </div>
            </CardHeader>
            <CardContent>
              {loading ? (
                <Skeleton className="h-8 w-24" />
              ) : (
                <div className="text-2xl font-bold">{stat.value}</div>
              )}
              <div className={`flex items-center mt-1 text-sm ${stat.trend === 'up' ? 'text-green-500' : 'text-red-500'}`}>
                {stat.trend === 'up' ? <TrendingUp className="h-3 w-3 mr-1" /> : <TrendingDown className="h-3 w-3 mr-1" />}
                {stat.change}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quick Actions */}
      <h2 className="text-xl font-semibold mt-8 mb-4">Quick Actions</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {quickActions.map((action, index) => (
          <Link to={action.path} key={index}>
            <Card className="bg-forex-darker border-forex-border hover:bg-forex-hover transition-colors cursor-pointer h-full">
              <CardHeader>
                <div className={`p-3 rounded-lg ${action.color} w-fit`}>
                  {action.icon}
                </div>
                <CardTitle className="mt-4">{action.title}</CardTitle>
                <CardDescription>{action.description}</CardDescription>
              </CardHeader>
              <CardFooter>
                <Button variant="ghost" className="w-full justify-start">
                  Go to {action.title}
                </Button>
              </CardFooter>
            </Card>
          </Link>
        ))}
      </div>

      {/* System Status */}
      <h2 className="text-xl font-semibold mt-8 mb-4">System Status</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {systemStatusCards.map((status, index) => (
          <Card key={index} className="bg-forex-darker border-forex-border">
            <CardHeader className="pb-2">
              <div className="flex justify-between items-center">
                <CardTitle className="text-sm font-medium text-forex-muted">{status.title}</CardTitle>
                <div className={`p-2 rounded-full ${
                  status.status === 'operational' ? 'bg-green-500/20 text-green-500' :
                  status.status === 'warning' ? 'bg-amber-500/20 text-amber-500' :
                  'bg-red-500/20 text-red-500'
                }`}>
                  {status.icon}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {loading ? (
                <Skeleton className="h-8 w-24" />
              ) : (
                <div className="text-2xl font-bold">{status.value}</div>
              )}
              <div className="flex items-center mt-1 text-sm text-forex-muted">
                <Clock className="h-3 w-3 mr-1" />
                Updated {lastUpdated.toLocaleTimeString()}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default AdminDashboard;
