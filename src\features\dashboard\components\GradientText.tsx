import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface GradientTextProps {
  value: string | number;
  trend?: 'positive' | 'negative' | 'neutral' | 'warning' | 'premium';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  animate?: boolean;
  glow?: boolean;
  shimmer?: boolean;
  className?: string;
  countUp?: boolean;
  bold?: boolean;
}

/**
 * Enhanced gradient text component for displaying metrics with advanced visual styling
 *
 * Renders text with gradient effects, animations, and visual enhancements based on
 * the trend (positive/negative/neutral/warning/premium)
 *
 * Features:
 * - Gradient color effects based on trend
 * - Optional text glow effect
 * - Optional shimmer animation
 * - Count-up animation for numerical values
 * - Responsive sizing options
 * - Customizable animations
 *
 * @component
 * @example
 * <GradientText
 *   value="2.5%"
 *   trend="positive"
 *   size="lg"
 *   animate={true}
 *   glow={true}
 *   shimmer={true}
 *   countUp={true}
 * />
 */
const GradientText: React.FC<GradientTextProps> = ({
  value,
  trend = 'neutral',
  size = 'md',
  animate = true,
  glow = false,
  shimmer = false,
  className = '',
  countUp = false,
  bold = true
}) => {
  const [displayValue, setDisplayValue] = useState<string | number>(countUp ? '0' : value);
  const [hasAnimated, setHasAnimated] = useState(false);

  // Determine gradient colors based on trend
  const getGradientColors = () => {
    switch (trend) {
      case 'positive':
        return 'from-green-400 to-emerald-500';
      case 'negative':
        return 'from-red-400 to-rose-500';
      case 'warning':
        return 'from-amber-400 to-yellow-500';
      case 'premium':
        return 'from-indigo-400 via-blue-500 to-cyan-400';
      case 'neutral':
      default:
        return 'from-blue-400 to-indigo-500';
    }
  };

  // Determine text glow based on trend
  const getTextGlow = () => {
    if (!glow) return '';

    switch (trend) {
      case 'positive':
        return 'text-glow-green';
      case 'negative':
        return 'text-glow-red';
      case 'warning':
        return 'text-shadow';
      case 'premium':
        return 'text-glow-blue';
      case 'neutral':
      default:
        return 'text-glow-blue';
    }
  };

  // Determine text size
  const getTextSize = () => {
    switch (size) {
      case 'xs':
        return 'text-base';
      case 'sm':
        return 'text-lg';
      case 'md':
        return 'text-2xl';
      case 'lg':
        return 'text-3xl';
      case 'xl':
        return 'text-4xl';
      case '2xl':
        return 'text-5xl';
      default:
        return 'text-2xl';
    }
  };

  // Animation variants
  const textVariants = {
    initial: {
      opacity: 0,
      y: 10,
      scale: 0.95
    },
    animate: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.5,
        ease: "easeOut"
      }
    }
  };

  // Enhanced pulse animation based on trend
  const getPulseVariants = () => {
    switch (trend) {
      case 'positive':
        return {
          scale: [1, 1.03, 1],
          textShadow: ['0 0 5px rgba(34, 197, 94, 0.5)', '0 0 15px rgba(34, 197, 94, 0.7)', '0 0 5px rgba(34, 197, 94, 0.5)']
        };
      case 'negative':
        return {
          scale: [1, 1.03, 1],
          textShadow: ['0 0 5px rgba(239, 68, 68, 0.5)', '0 0 15px rgba(239, 68, 68, 0.7)', '0 0 5px rgba(239, 68, 68, 0.5)']
        };
      case 'premium':
        return {
          scale: [1, 1.03, 1],
          textShadow: ['0 0 5px rgba(59, 130, 246, 0.5)', '0 0 15px rgba(59, 130, 246, 0.7)', '0 0 5px rgba(59, 130, 246, 0.5)']
        };
      default:
        return {
          scale: [1, 1.02, 1]
        };
    }
  };

  const pulseVariants = {
    pulse: {
      ...getPulseVariants(),
      transition: {
        repeat: Infinity,
        repeatType: "reverse" as const,
        duration: 2,
        ease: "easeInOut"
      }
    }
  };

  // Shimmer animation
  const shimmerVariants = {
    shimmer: {
      backgroundPosition: ['200% 0', '-200% 0', '200% 0'],
      transition: {
        repeat: Infinity,
        duration: 3,
        ease: "linear"
      }
    }
  };

  // Count-up animation for numerical values
  useEffect(() => {
    if (countUp && animate && !hasAnimated) {
      // Extract numeric value if it's a string with non-numeric characters
      let numericValue: number;
      let prefix = '';
      let suffix = '';

      if (typeof value === 'string') {
        // Extract prefix (like '$')
        const prefixMatch = value.match(/^[^\d-.]*/);
        prefix = prefixMatch ? prefixMatch[0] : '';

        // Extract suffix (like '%')
        const suffixMatch = value.match(/[^\d-.]*$/);
        suffix = suffixMatch ? suffixMatch[0] : '';

        // Extract numeric part
        const numericMatch = value.match(/[-\d.]+/);
        numericValue = numericMatch ? parseFloat(numericMatch[0]) : 0;
      } else {
        numericValue = Number(value);
      }

      // Determine if we need decimal places
      const isDecimal = String(value).includes('.');
      const decimalPlaces = isDecimal ? String(value).split('.')[1]?.length || 0 : 0;

      // Animate from 0 to the target value
      let startValue = 0;
      const steps = 20;
      const increment = numericValue / steps;
      const duration = 1000; // 1 second animation
      const stepDuration = duration / steps;

      const timer = setInterval(() => {
        startValue += increment;

        if (startValue >= numericValue) {
          startValue = numericValue;
          clearInterval(timer);
          setHasAnimated(true);
        }

        // Format the value with the same decimal places as the original
        const formattedValue = isDecimal
          ? startValue.toFixed(decimalPlaces)
          : Math.round(startValue).toString();

        setDisplayValue(`${prefix}${formattedValue}${suffix}`);
      }, stepDuration);

      return () => clearInterval(timer);
    } else {
      setDisplayValue(value);
    }
  }, [value, countUp, animate, hasAnimated]);

  return (
    <motion.div
      className={`${bold ? 'font-bold' : 'font-medium'} ${getTextSize()} ${getTextGlow()} ${className}
        ${shimmer ? 'bg-[length:200%_100%]' : 'bg-clip-text text-transparent'}
        ${shimmer
          ? 'bg-gradient-to-r from-transparent via-white/10 to-transparent'
          : `bg-gradient-to-r ${getGradientColors()}`}
      `}
      style={{
        backgroundClip: shimmer ? 'unset' : 'text',
        WebkitBackgroundClip: shimmer ? 'unset' : 'text',
        WebkitTextFillColor: shimmer ? 'unset' : 'transparent',
      }}
      initial={animate ? "initial" : undefined}
      animate={animate ? ["animate", shimmer ? "shimmer" : "pulse"] : undefined}
      variants={{
        ...textVariants,
        ...pulseVariants,
        ...(shimmer ? shimmerVariants : {})
      }}
    >
      {/* Actual text with gradient */}
      <span className={shimmer ? `bg-gradient-to-r ${getGradientColors()} bg-clip-text text-transparent` : ''}>
        {displayValue}
      </span>

      {/* Optional animated underline for emphasis */}
      {trend === 'premium' && (
        <motion.div
          className="h-0.5 bg-gradient-to-r from-blue-400 to-indigo-500 mt-1 rounded-full"
          initial={{ width: 0 }}
          animate={{ width: '100%' }}
          transition={{ delay: 0.3, duration: 0.5 }}
        />
      )}
    </motion.div>
  );
};

export default GradientText;
