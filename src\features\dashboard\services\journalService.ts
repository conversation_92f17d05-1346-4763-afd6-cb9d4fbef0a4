/**
 * Journal Service
 * @description Service for journal-related operations using API with localStorage fallback
 * @version 2.1.0
 */

import { api, apiService } from '@/common/services/api';

/**
 * Journal Entry Interface
 */
export interface JournalEntry {
  id: string | number;
  title: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  tags?: string[];
  mood?: 'positive' | 'neutral' | 'negative';
  tradeIds?: string[];
  attachments?: {
    type: 'image' | 'chart' | 'file';
    url: string;
    name: string;
  }[];
  isFavorite?: boolean;
  dayProfitability?: 'profitable' | 'unprofitable' | 'break-even';
  pnlAmount?: number;
}

// Local storage key for journal entries
const JOURNAL_ENTRIES_STORAGE_KEY = 'tcx_journal_entries';

/**
 * Get all journal entries from localStorage
 * @returns Array of journal entries
 */
const getEntriesFromStorage = (): JournalEntry[] => {
  try {
    const storedEntries = localStorage.getItem(JOURNAL_ENTRIES_STORAGE_KEY);
    if (storedEntries) {
      return JSON.parse(storedEntries);
    }
  } catch (error) {
    console.error('Error reading journal entries from localStorage:', error);
  }
  return [];
};

/**
 * Save journal entries to localStorage
 * @param entries Array of journal entries to save
 */
const saveEntriesToStorage = (entries: JournalEntry[]): void => {
  try {
    localStorage.setItem(JOURNAL_ENTRIES_STORAGE_KEY, JSON.stringify(entries));
    console.log('Saved entries to localStorage:', entries.length);
  } catch (error) {
    console.error('Error saving journal entries to localStorage:', error);
  }
};

/**
 * Interface for creating a new journal entry
 */
export interface CreateJournalEntryDto {
  title: string;
  content: string;
  tags?: string[];
  mood?: 'positive' | 'neutral' | 'negative';
  tradeIds?: string[];
  attachments?: {
    type: 'image' | 'chart' | 'file';
    url: string;
    name: string;
  }[];
  dayProfitability?: 'profitable' | 'unprofitable' | 'break-even';
  pnlAmount?: number;
}

/**
 * Interface for updating an existing journal entry
 */
export interface UpdateJournalEntryDto extends CreateJournalEntryDto {
  id: number;
}

/**
 * Get all journal entries for the current user
 * @returns Promise with journal entries
 */
export const getJournalEntries = async (): Promise<JournalEntry[]> => {
  try {
    console.log('Fetching journal entries...');

    // First, check localStorage for any saved entries
    const storedEntries = getEntriesFromStorage();
    if (storedEntries.length > 0) {
      console.log('Found entries in localStorage:', storedEntries.length);
    }

    // Try to get entries from the API
    try {
      const response = await api.getJournalEntries();
      console.log('Journal API response:', response);

      // The response should be the array of journal entries directly
      if (Array.isArray(response)) {
        console.log('Returning array response directly');
        // Save to localStorage for future use
        saveEntriesToStorage(response);
        return response;
      }

      // If response has a data property that's an array, use that
      if (response && Array.isArray(response.data)) {
        console.log('Returning response.data array');
        // Save to localStorage for future use
        saveEntriesToStorage(response.data);
        return response.data;
      }
    } catch (apiError) {
      console.error('API error, falling back to localStorage:', apiError);
    }

    // If we have stored entries, return those
    if (storedEntries.length > 0) {
      console.log('Returning entries from localStorage');
      return storedEntries;
    }

    // If we get here, no entries were found
    console.warn('No entries found in API or localStorage');
    return [];
  } catch (error) {
    console.error('Error in getJournalEntries:', error);

    // Try to get entries from localStorage as a fallback
    const storedEntries = getEntriesFromStorage();
    if (storedEntries.length > 0) {
      console.log('Error occurred, returning entries from localStorage');
      return storedEntries;
    }

    // Return empty array instead of throwing for empty entries or not found
    // This will trigger the empty state UI instead of the error state
    if (error instanceof Error &&
        (error.message.includes('empty') ||
         error.message.includes('404') ||
         error.message.includes('not found'))) {
      return [];
    }
    throw error;
  }
};

/**
 * Get a single journal entry by ID
 * @param id Journal entry ID
 * @returns Promise with journal entry
 */
export const getJournalEntryById = async (id: number): Promise<JournalEntry> => {
  try {
    const response = await api.getJournalEntryById(id);
    return response;
  } catch (error) {
    console.error(`Error getting journal entry ${id}:`, error);
    throw error;
  }
};

/**
 * Create a new journal entry
 * @param entry Journal entry data
 * @returns Promise with created journal entry
 */
export const createJournalEntry = async (entry: CreateJournalEntryDto): Promise<JournalEntry> => {
  try {
    console.log('Creating journal entry:', entry);

    // Create a new entry object with temporary ID
    const newEntry: JournalEntry = {
      id: Date.now().toString(), // Temporary ID, will be replaced by API response
      title: entry.title,
      content: entry.content,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      tags: entry.tags || [],
      mood: entry.mood,
      tradeIds: entry.tradeIds || [],
      attachments: entry.attachments,
      dayProfitability: entry.dayProfitability,
      pnlAmount: entry.pnlAmount,
      isFavorite: false
    };

    // Try to save to the API
    const response = await api.createJournalEntry(entry);
    console.log('Create journal entry API response:', response);

    // If we have a proper response with an ID, use that ID
    if (response && response.id) {
      newEntry.id = response.id;
    }

    // Save to localStorage as a backup
    const currentEntries = getEntriesFromStorage();
    const updatedEntries = [newEntry, ...currentEntries];
    saveEntriesToStorage(updatedEntries);
    console.log('Saved new entry to localStorage as backup, total entries:', updatedEntries.length);

    return newEntry;
  } catch (error) {
    console.error('Error creating journal entry:', error);

    // Create a fallback entry for localStorage
    const fallbackEntry: JournalEntry = {
      id: Date.now().toString(),
      title: entry.title,
      content: entry.content,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      tags: entry.tags || [],
      mood: entry.mood,
      tradeIds: entry.tradeIds || [],
      attachments: entry.attachments,
      dayProfitability: entry.dayProfitability,
      pnlAmount: entry.pnlAmount,
      isFavorite: false
    };

    // Save to localStorage as a fallback
    const currentEntries = getEntriesFromStorage();
    const updatedEntries = [fallbackEntry, ...currentEntries];
    saveEntriesToStorage(updatedEntries);
    console.log('API error - saved entry to localStorage as fallback');

    // Return the fallback entry
    return fallbackEntry;
  }
};

/**
 * Update an existing journal entry
 * @param id Journal entry ID
 * @param entry Updated journal entry data
 * @returns Promise with updated journal entry
 */
export const updateJournalEntry = async (id: number, entry: Omit<UpdateJournalEntryDto, 'id'>): Promise<JournalEntry> => {
  try {
    console.log(`Updating journal entry with ID: ${id}`, entry);

    // Update in the API
    const response = await api.updateJournalEntry(id, entry);
    console.log('Update journal entry API response:', response);
    const apiResponse = response;

    // Also update in localStorage as a backup
    const currentEntries = getEntriesFromStorage();
    const entryIndex = currentEntries.findIndex(e =>
      e.id === id || e.id.toString() === id.toString()
    );

    if (entryIndex !== -1) {
      // Create updated entry
      const updatedEntry: JournalEntry = {
        ...currentEntries[entryIndex],
        ...entry,
        id: currentEntries[entryIndex].id, // Preserve the original ID
        updatedAt: new Date().toISOString()
      };

      // Update the entry in the array
      const updatedEntries = [...currentEntries];
      updatedEntries[entryIndex] = updatedEntry;

      // Save to localStorage
      saveEntriesToStorage(updatedEntries);
      console.log(`Updated entry ${id} in localStorage as backup`);
    }

    // Return the API response
    return apiResponse;
  } catch (error) {
    console.error(`Error updating journal entry ${id}:`, error);

    // Try to update in localStorage as a fallback
    const currentEntries = getEntriesFromStorage();
    const entryIndex = currentEntries.findIndex(e =>
      e.id === id || e.id.toString() === id.toString()
    );

    if (entryIndex !== -1) {
      // Create updated entry
      const updatedEntry: JournalEntry = {
        ...currentEntries[entryIndex],
        ...entry,
        id: currentEntries[entryIndex].id, // Preserve the original ID
        updatedAt: new Date().toISOString()
      };

      // Update the entry in the array
      const updatedEntries = [...currentEntries];
      updatedEntries[entryIndex] = updatedEntry;

      // Save to localStorage
      saveEntriesToStorage(updatedEntries);
      console.log(`API error - updated entry ${id} in localStorage as fallback`);

      // Return the updated entry from localStorage
      return updatedEntry;
    }

    // If we couldn't find the entry in localStorage, rethrow the error
    throw error;
  }
};

/**
 * Delete a journal entry
 * @param id Journal entry ID
 * @returns Promise with success message
 */
export const deleteJournalEntry = async (id: number): Promise<{ message: string }> => {
  try {
    console.log(`Deleting journal entry with ID: ${id}`);

    // Delete from the API
    const response = await api.deleteJournalEntry(id);
    console.log('Delete journal entry API response:', response);

    // Also delete from localStorage
    const currentEntries = getEntriesFromStorage();
    const updatedEntries = currentEntries.filter(entry =>
      entry.id !== id && entry.id.toString() !== id.toString()
    );
    saveEntriesToStorage(updatedEntries);
    console.log(`Deleted entry ${id} from localStorage, remaining entries:`, updatedEntries.length);

    return { message: 'Journal entry deleted successfully' };
  } catch (error) {
    console.error(`Error deleting journal entry ${id}:`, error);

    // Try to delete from localStorage as a fallback
    const currentEntries = getEntriesFromStorage();
    const updatedEntries = currentEntries.filter(entry =>
      entry.id !== id && entry.id.toString() !== id.toString()
    );

    // Only save to localStorage if we actually removed an entry
    if (currentEntries.length !== updatedEntries.length) {
      saveEntriesToStorage(updatedEntries);
      console.log(`API error - deleted entry ${id} from localStorage as fallback`);
      return { message: 'Journal entry deleted from local storage (API error)' };
    }

    throw error;
  }
};

/**
 * Toggle favorite status of a journal entry
 * @param id Journal entry ID
 * @returns Promise with updated journal entry
 */
export const toggleFavoriteJournalEntry = async (id: number): Promise<JournalEntry> => {
  try {
    console.log(`Toggling favorite status for journal entry with ID: ${id}`);

    // Toggle favorite status in the API
    const response = await api.toggleFavoriteJournalEntry(id);
    console.log('Toggle favorite API response:', response);
    const apiResponse = response;

    // Also update in localStorage as a backup
    const currentEntries = getEntriesFromStorage();
    const entryIndex = currentEntries.findIndex(entry =>
      entry.id === id || entry.id.toString() === id.toString()
    );

    if (entryIndex !== -1) {
      // Toggle the favorite status based on API response if available, otherwise toggle the current value
      const isFavorite = apiResponse?.isFavorite !== undefined
        ? apiResponse.isFavorite
        : !currentEntries[entryIndex].isFavorite;

      const updatedEntry = {
        ...currentEntries[entryIndex],
        isFavorite,
        updatedAt: new Date().toISOString()
      };

      // Update the entry in the array
      const updatedEntries = [...currentEntries];
      updatedEntries[entryIndex] = updatedEntry;

      // Save to localStorage
      saveEntriesToStorage(updatedEntries);
      console.log(`Updated favorite status for entry ${id} in localStorage as backup`);
    }

    // Return the API response
    return apiResponse;
  } catch (error) {
    console.error(`Error toggling favorite status for journal entry ${id}:`, error);

    // Try to update in localStorage as a fallback
    const currentEntries = getEntriesFromStorage();
    const entryIndex = currentEntries.findIndex(entry =>
      entry.id === id || entry.id.toString() === id.toString()
    );

    if (entryIndex !== -1) {
      // Toggle the favorite status
      const updatedEntry = {
        ...currentEntries[entryIndex],
        isFavorite: !currentEntries[entryIndex].isFavorite,
        updatedAt: new Date().toISOString()
      };

      // Update the entry in the array
      const updatedEntries = [...currentEntries];
      updatedEntries[entryIndex] = updatedEntry;

      // Save to localStorage
      saveEntriesToStorage(updatedEntries);
      console.log(`API error - updated favorite status for entry ${id} in localStorage as fallback`);

      // Return the updated entry from localStorage
      return updatedEntry;
    }

    // If we couldn't find the entry in localStorage, rethrow the error
    throw error;
  }
};
