import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Trophy, Users, ChevronDown, ArrowUp, ArrowDown, ChevronRight } from 'lucide-react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  Button,
  Badge,
  Avatar,
  AvatarImage,
  AvatarFallback
} from '@/common/components/ui';
import { useChallengeContext } from '../context/ChallengeContext';

// Competitor interface
interface Competitor {
  id: string;
  name: string;
  avatar?: string;
  pnlPercent: number;
  rankChange: number;
  country: string;
  isCurrentUser: boolean;
}

/**
 * Challenge Leaderboard component
 *
 * Displays a cinematic leaderboard with live movement indicators for
 * challenge participants.
 *
 * @component
 * @status experimental
 * @version 1.0.0
 * @example
 * <ChallengeLeaderboard />
 */
const ChallengeLeaderboard: React.FC = () => {
  const { selectedChallenge } = useChallengeContext();
  const [expanded, setExpanded] = useState(false);
  const [competitors, setCompetitors] = useState<Competitor[]>([]);
  const [isAnimating, setIsAnimating] = useState(false);

  // We'll fetch real data from the API instead of using mock data

  // Initialize competitors - fetch from API when selectedChallenge changes
  useEffect(() => {
    if (selectedChallenge?.id) {
      // In a real implementation, you would fetch the leaderboard data from the API
      // For example:
      // apiService.getLeaderboard(selectedChallenge.id)
      //   .then(data => {
      //     setCompetitors(data.map(item => ({
      //       id: item.userId,
      //       name: item.username,
      //       avatar: item.avatar,
      //       pnlPercent: item.score,
      //       rankChange: item.change,
      //       country: item.country || 'US',
      //       isCurrentUser: item.isCurrentUser
      //     })));
      //   })
      //   .catch(error => {
      //     console.error('Error fetching leaderboard:', error);
      //     setCompetitors([]);
      //   });

      // For now, we'll use an empty array until the API is implemented
      setCompetitors([]);
    }
  }, [selectedChallenge?.id]);

  // In a real implementation, you would use WebSockets to get real-time updates
  // For now, we'll disable the simulation of rank changes
  useEffect(() => {
    // This would be replaced with WebSocket connection code
    return () => {
      // Cleanup WebSocket connection
    };
  }, []);

  // Format name with country
  const formatNameWithCountry = (competitor: Competitor) => {
    const countryEmoji = getCountryFlag(competitor.country);
    return (
      <div className="flex items-center">
        <span className="mr-1.5">{countryEmoji}</span>
        <span className="truncate">{competitor.name}</span>
      </div>
    );
  };

  // Get country flag emoji
  const getCountryFlag = (countryCode: string) => {
    const codePoints = countryCode
      .toUpperCase()
      .split('')
      .map(char => 127397 + char.charCodeAt(0));
    return String.fromCodePoint(...codePoints);
  };

  // Get rank change indicator
  const getRankChangeIndicator = (rankChange: number) => {
    if (rankChange > 0) {
      return (
        <div className="flex items-center text-green-400">
          <ArrowUp className="h-3 w-3 mr-0.5" />
          <span>{rankChange}</span>
        </div>
      );
    } else if (rankChange < 0) {
      return (
        <div className="flex items-center text-red-400">
          <ArrowDown className="h-3 w-3 mr-0.5" />
          <span>{Math.abs(rankChange)}</span>
        </div>
      );
    } else {
      return (
        <div className="flex items-center text-gray-400">
          <span className="text-xs">-</span>
        </div>
      );
    }
  };

  // If no challenge selected, show empty state
  if (!selectedChallenge) {
    return (
      <Card className="border-blue-500/20 bg-[#0f1c2e]/60 backdrop-blur-sm">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg font-medium">Leaderboard</CardTitle>
          <CardDescription className="text-xs text-gray-400">
            Select a challenge to view the leaderboard
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-2">
          <div className="flex flex-col items-center justify-center py-6">
            <Trophy className="h-12 w-12 text-blue-500/50 mb-3" />
            <p className="text-gray-400 text-sm text-center max-w-xs">
              Select a specific challenge to see the leaderboard and your ranking position.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Sort competitors by pnlPercent
  const sortedCompetitors = [...competitors].sort((a, b) => b.pnlPercent - a.pnlPercent);

  // Determine display count based on expanded state
  const displayCount = expanded ? sortedCompetitors.length : Math.min(5, sortedCompetitors.length);
  const displayedCompetitors = sortedCompetitors.slice(0, displayCount);

  // Find current user's position
  const currentUserPosition = sortedCompetitors.findIndex(c => c.isCurrentUser) + 1;

  return (
    <Card className="border-blue-500/20 bg-[#0f1c2e]/60 backdrop-blur-sm">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg font-medium">Challenge Leaderboard</CardTitle>
            <CardDescription className="text-xs text-gray-400">
              {selectedChallenge.name} rankings
            </CardDescription>
          </div>
          <Badge className="bg-blue-500/20 text-blue-300 border border-blue-500/30 flex items-center gap-1">
            <Users className="h-3 w-3" />
            <span>{competitors.length}</span>
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="pt-2">
        {/* Leaderboard table */}
        <div className="space-y-1">
          {/* Table header */}
          <div className="grid grid-cols-12 px-3 py-2 text-xs text-gray-400 border-b border-blue-500/20">
            <div className="col-span-1 font-medium">#</div>
            <div className="col-span-6 font-medium">Trader</div>
            <div className="col-span-3 font-medium text-right">P&L</div>
            <div className="col-span-2 font-medium text-right">Change</div>
          </div>

          {/* Competitors list */}
          <div className="space-y-1 py-1">
            <AnimatePresence>
              {displayedCompetitors.map((competitor, index) => (
                <motion.div
                  key={competitor.id}
                  className={`grid grid-cols-12 px-3 py-2 rounded-md ${
                    competitor.isCurrentUser
                      ? 'bg-blue-500/20 border border-blue-500/40'
                      : 'hover:bg-blue-500/10'
                  }`}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{
                    opacity: 1,
                    x: 0,
                    background: isAnimating && competitor.rankChange !== 0
                      ? competitor.rankChange > 0
                        ? ['rgba(22, 163, 74, 0.1)', 'rgba(22, 163, 74, 0)', 'rgba(22, 163, 74, 0)']
                        : ['rgba(220, 38, 38, 0.1)', 'rgba(220, 38, 38, 0)', 'rgba(220, 38, 38, 0)']
                      : undefined
                  }}
                  exit={{ opacity: 0, x: 10 }}
                  transition={{
                    duration: 0.3,
                    delay: index * 0.05,
                    background: { duration: 1.5, times: [0, 0.5, 1] }
                  }}
                >
                  {/* Rank */}
                  <div className="col-span-1 flex items-center">
                    {index === 0 ? (
                      <Trophy className="h-4 w-4 text-amber-400" />
                    ) : (
                      <span className={`text-sm font-medium ${
                        index === 1 ? 'text-gray-300' : index === 2 ? 'text-amber-700' : 'text-gray-400'
                      }`}>{index + 1}</span>
                    )}
                  </div>

                  {/* Trader */}
                  <div className="col-span-6 flex items-center">
                    <Avatar className="h-6 w-6 mr-2">
                      {competitor.avatar ? (
                        <AvatarImage src={competitor.avatar} alt={competitor.name} />
                      ) : (
                        <AvatarFallback className="bg-blue-500/20 text-blue-300 text-xs">
                          {competitor.name.substring(0, 2).toUpperCase()}
                        </AvatarFallback>
                      )}
                    </Avatar>
                    <span className={`text-sm truncate ${competitor.isCurrentUser ? 'font-medium text-white' : 'text-gray-300'}`}>
                      {formatNameWithCountry(competitor)}
                    </span>
                    {competitor.isCurrentUser && (
                      <Badge className="ml-2 bg-blue-500/20 text-blue-300 border border-blue-500/30 text-xs">
                        You
                      </Badge>
                    )}
                  </div>

                  {/* P&L */}
                  <div className="col-span-3 flex items-center justify-end">
                    <span className="text-sm font-medium text-green-400">
                      +{competitor.pnlPercent.toFixed(2)}%
                    </span>
                  </div>

                  {/* Rank change */}
                  <div className="col-span-2 flex items-center justify-end text-xs">
                    {getRankChangeIndicator(competitor.rankChange)}
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </div>

        {/* User's position indicator (if not visible in the short list) */}
        {!expanded && currentUserPosition > displayCount && (
          <div className="mt-2 px-3 py-2 bg-blue-500/10 rounded-md border border-blue-500/20">
            <div className="grid grid-cols-12">
              <div className="col-span-1 flex items-center">
                <span className="text-sm font-medium text-white">{currentUserPosition}</span>
              </div>

              <div className="col-span-6 flex items-center">
                <Avatar className="h-6 w-6 mr-2">
                  <AvatarFallback className="bg-blue-500/20 text-blue-300 text-xs">
                    {sortedCompetitors[currentUserPosition - 1].name.substring(0, 2).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <span className="text-sm font-medium text-white truncate">
                  {formatNameWithCountry(sortedCompetitors[currentUserPosition - 1])}
                </span>
                <Badge className="ml-2 bg-blue-500/20 text-blue-300 border border-blue-500/30 text-xs">
                  You
                </Badge>
              </div>

              <div className="col-span-3 flex items-center justify-end">
                <span className="text-sm font-medium text-green-400">
                  +{sortedCompetitors[currentUserPosition - 1].pnlPercent.toFixed(2)}%
                </span>
              </div>

              <div className="col-span-2 flex items-center justify-end text-xs">
                {getRankChangeIndicator(sortedCompetitors[currentUserPosition - 1].rankChange)}
              </div>
            </div>
          </div>
        )}

        {/* Show more/less button */}
        {competitors.length > 5 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setExpanded(!expanded)}
            className="w-full mt-3 text-blue-300 hover:text-blue-200 hover:bg-blue-500/10"
          >
            {expanded ? 'Show Less' : `Show All ${competitors.length} Competitors`}
            <ChevronDown className={`ml-2 h-4 w-4 transition-transform ${expanded ? 'rotate-180' : ''}`} />
          </Button>
        )}

        {/* View full leaderboard button */}
        <Button
          variant="outline"
          size="sm"
          className="w-full mt-3 text-blue-300 border-blue-500/30 hover:bg-blue-500/10 hover:text-blue-200"
        >
          View Full Leaderboard
          <ChevronRight className="ml-2 h-4 w-4" />
        </Button>
      </CardContent>
    </Card>
  );
};

export default ChallengeLeaderboard;