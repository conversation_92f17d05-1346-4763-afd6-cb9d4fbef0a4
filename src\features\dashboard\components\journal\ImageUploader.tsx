import React, { useState, useCallback } from 'react';
import { Upload, X, Image as ImageIcon, AlertCircle } from 'lucide-react';
import { Button } from '@/common/components/ui';
import { uploadImageToCloudinary, validateImageFile } from '@/lib/cloudinary';

interface ImageUploaderProps {
  onImageUploaded: (imageUrl: string) => void;
  maxImages?: number;
  currentImagesCount?: number;
}

/**
 * Image uploader component for journal entries
 */
const ImageUploader: React.FC<ImageUploaderProps> = ({
  onImageUploaded,
  maxImages = 5,
  currentImagesCount = 0,
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dragActive, setDragActive] = useState(false);

  const handleUpload = useCallback(async (file: File) => {
    if (currentImagesCount >= maxImages) {
      setError(`Maximum of ${maxImages} images allowed per entry`);
      return;
    }

    if (!validateImageFile(file)) {
      setError('Invalid file. Please upload JPG, PNG, or GIF images under 2MB');
      return;
    }

    setError(null);
    setIsUploading(true);

    try {
      const imageUrl = await uploadImageToCloudinary(file);
      if (imageUrl) {
        onImageUploaded(imageUrl);
      } else {
        setError('Failed to upload image. Please try again.');
      }
    } catch (err) {
      setError('An error occurred during upload');
      console.error(err);
    } finally {
      setIsUploading(false);
    }
  }, [currentImagesCount, maxImages, onImageUploaded]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleUpload(e.target.files[0]);
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleUpload(e.dataTransfer.files[0]);
    }
  };

  // Don't show the uploader if max images reached
  if (currentImagesCount >= maxImages) {
    return null;
  }

  return (
    <div className="mb-4">
      <div
        className={`border-2 border-dashed rounded-lg p-4 text-center ${
          dragActive ? 'border-blue-400 bg-blue-50/10' : 'border-[#3a3a3a]'
        } transition-colors duration-200`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <div className="flex flex-col items-center justify-center py-4">
          <ImageIcon className="h-10 w-10 text-gray-400 mb-2" />
          <p className="text-sm text-gray-300 mb-2">
            Drag & drop an image or click to browse
          </p>
          <p className="text-xs text-gray-400 mb-4">
            JPG, PNG or GIF • Max 2MB • {maxImages - currentImagesCount} remaining
          </p>
          <div>
            <input
              id="file-upload"
              type="file"
              className="hidden"
              accept="image/jpeg,image/png,image/gif"
              onChange={handleFileChange}
              disabled={isUploading}
            />
            <label htmlFor="file-upload">
              <Button
                type="button"
                variant="outline"
                size="sm"
                className="bg-[#2a2a2a] hover:bg-[#333333] text-gray-200 border-[#3a3a3a] cursor-pointer"
                disabled={isUploading}
                onClick={() => document.getElementById('file-upload')?.click()}
              >
                {isUploading ? (
                  <span className="flex items-center">
                    <span className="animate-spin mr-2">⟳</span> Uploading...
                  </span>
                ) : (
                  <span className="flex items-center">
                    <Upload className="h-4 w-4 mr-2" /> Select Image
                  </span>
                )}
              </Button>
            </label>
          </div>
        </div>
      </div>

      {error && (
        <div className="mt-2 text-red-400 text-sm flex items-center">
          <AlertCircle className="h-4 w-4 mr-1" />
          {error}
        </div>
      )}
    </div>
  );
};

export default ImageUploader;
