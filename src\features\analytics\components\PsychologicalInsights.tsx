import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/common/components/ui/card';
import { useAnalyticsContext } from '../context/AnalyticsContext';
import { Brain, AlertTriangle, Info, CheckCircle } from 'lucide-react';
import { Badge } from '@/common/components/ui/badge';
import InfoButton from './InfoButton';

/**
 * Psychological insights component
 *
 * Analyzes trading psychology and provides insights.
 *
 * @component
 * @example
 * <PsychologicalInsights />
 */
const PsychologicalInsights: React.FC = () => {
  const { psychologicalInsights } = useAnalyticsContext();

  // Get severity icon and color
  const getSeverityDetails = (severity: 'low' | 'medium' | 'high') => {
    switch (severity) {
      case 'low':
        return {
          icon: <Info className="h-4 w-4" />,
          color: 'bg-blue-600 text-blue-100',
          label: 'Low'
        };
      case 'medium':
        return {
          icon: <AlertTriangle className="h-4 w-4" />,
          color: 'bg-yellow-600 text-yellow-100',
          label: 'Medium'
        };
      case 'high':
        return {
          icon: <AlertTriangle className="h-4 w-4" />,
          color: 'bg-red-600 text-red-100',
          label: 'High'
        };
      default:
        return {
          icon: <Info className="h-4 w-4" />,
          color: 'bg-blue-600 text-blue-100',
          label: 'Info'
        };
    }
  };

  // Get insight type label
  const getInsightTypeLabel = (type: string) => {
    switch (type) {
      case 'revenge':
        return 'Revenge Trading';
      case 'overtrading':
        return 'Overtrading';
      case 'fomo':
        return 'FOMO';
      case 'earlyExit':
        return 'Early Exit';
      case 'discipline':
        return 'Discipline';
      case 'consistency':
        return 'Consistency';
      default:
        return type.charAt(0).toUpperCase() + type.slice(1);
    }
  };

  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  };

  return (
    <Card className="border-gray-800 bg-gray-900/60 backdrop-blur-sm overflow-hidden">
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2">
          <Brain className="h-5 w-5 text-indigo-400" />
          <div className="flex items-center gap-1.5">
            Psychological Insights
            <InfoButton
              content="Analyzes your trading behavior to identify potential psychological patterns that may be affecting your performance, such as revenge trading or overtrading."
              className="ml-1"
            />
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {psychologicalInsights.length === 0 ? (
          <div className="text-center py-6 text-gray-400">
            <CheckCircle className="h-12 w-12 mx-auto mb-3 text-green-500" />
            <p>No psychological issues detected.</p>
            <p className="text-sm mt-2">Keep up the good work!</p>
          </div>
        ) : (
          <motion.div
            variants={container}
            initial="hidden"
            animate="show"
            className="space-y-3"
          >
            {psychologicalInsights.map((insight, index) => {
              const severityDetails = getSeverityDetails(insight.severity);

              return (
                <motion.div
                  key={index}
                  variants={item}
                  className="bg-gray-800/50 rounded-lg p-3 border border-gray-700"
                >
                  <div className="flex justify-between items-start mb-2">
                    <div className="font-medium text-white">
                      {getInsightTypeLabel(insight.type)}
                    </div>
                    <Badge className={severityDetails.color}>
                      <span className="flex items-center gap-1">
                        {severityDetails.icon}
                        {severityDetails.label}
                      </span>
                    </Badge>
                  </div>

                  <p className="text-sm text-gray-400 mb-3">{insight.description}</p>

                  <div className="bg-indigo-900/20 border border-indigo-900/30 rounded p-2 text-xs text-indigo-300">
                    <span className="font-medium">Recommendation:</span> {insight.recommendation}
                  </div>

                  {insight.relatedTrades.length > 0 && (
                    <div className="mt-2 text-xs text-gray-500">
                      Related trades: {insight.relatedTrades.length}
                    </div>
                  )}
                </motion.div>
              );
            })}
          </motion.div>
        )}
      </CardContent>
    </Card>
  );
};

export default PsychologicalInsights;
