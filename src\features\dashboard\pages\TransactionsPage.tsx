/**
 * Dashboard Transactions Page
 *
 * Displays comprehensive transaction history including platform activity,
 * wallet credits, challenge entries, and withdrawal history.
 *
 * @status stable
 * @version 1.0.0
 */

import React, { useState, useEffect } from 'react';
import { DashboardLayout } from '@/features/dashboard';
import { WalletCreditHistory } from '@/features/wallet';
import { Toaster } from '@/common/components/ui/sonner';
import { motion } from 'framer-motion';
import { useLocation } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { apiService } from '@/common/services/api';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  Badge,
  Button,
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem
} from '@/common/components/ui';
import {
  CreditCard,
  ArrowDownUp,
  Award,
  DollarSign,
  ArrowUpRight,
  ArrowDownLeft,
  Filter,
  Search,
  Calendar,
  X,
  Plus,
  BanknoteIcon,
  Clock,
  AlertCircle
} from 'lucide-react';

/**
 * Platform Transaction component displaying general platform activity
 */
const PlatformTransactions: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState("");

  // Fetch transactions from API
  const { data: apiTransactions, isLoading, error } = useQuery({
    queryKey: ['transactions'],
    queryFn: async () => {
      try {
        const data = await apiService.getTransactions();
        console.log('Transactions API response:', data);
        return Array.isArray(data) ? data : [];
      } catch (err) {
        console.error('Error fetching transactions:', err);
        throw err;
      }
    },
    retry: 1, // Only retry once to avoid too many failed requests
  });

  // Filter transactions based on search query
  const getFilteredTransactions = () => {
    if (!apiTransactions || !Array.isArray(apiTransactions)) return [];

    try {
      if (!searchQuery) return apiTransactions.slice(0, 10); // Limit to 10 transactions if no search

      return apiTransactions.filter(tx => {
        if (!tx || typeof tx !== 'object') return false;

        try {
          const reason = tx.reason || '';
          const type = tx.type || '';
          const searchLower = searchQuery.toLowerCase();

          return (
            (typeof reason === 'string' && reason.toLowerCase().includes(searchLower)) ||
            (typeof type === 'string' && type.toLowerCase().includes(searchLower))
          );
        } catch (err) {
          console.error('Error filtering transaction by search:', err, tx);
          return false;
        }
      });
    } catch (err) {
      console.error('Error in getFilteredTransactions:', err);
      return [];
    }
  };

  const filteredTransactions = getFilteredTransactions();

  // Format currency
  const formatCurrency = (amount: number) => {
    return `${amount >= 0 ? '+' : '-'}$${Math.abs(amount).toFixed(2)}`;
  };

  // Get transaction icon based on type
  const getTransactionIcon = (tx: any) => {
    try {
      if (tx && typeof tx.amount === 'number' && tx.amount > 0) {
        return <ArrowDownLeft className="h-4 w-4 text-green-400" />;
      } else {
        return <ArrowUpRight className="h-4 w-4 text-amber-400" />;
      }
    } catch (err) {
      console.error('Error getting transaction icon:', err, tx);
      return <AlertCircle className="h-4 w-4 text-red-400" />;
    }
  };

  // Get transaction badge based on type
  const getTransactionBadge = (tx: any) => {
    try {
      let badgeText = 'System';
      let badgeClass = "bg-blue-500/20 text-blue-300 border-blue-500/30";

      if (!tx || typeof tx !== 'object') {
        return <Badge className={badgeClass}>{badgeText}</Badge>;
      }

      const reason = tx.reason || '';

      if (typeof reason === 'string') {
        const reasonLower = reason.toLowerCase();

        if (reasonLower.includes('purchase') || reasonLower.includes('entry fee')) {
          badgeText = 'Purchase';
          badgeClass = "bg-amber-500/20 text-amber-300 border-amber-500/30";
        } else if (reasonLower.includes('prize') || reasonLower.includes('reward')) {
          badgeText = 'Reward';
          badgeClass = "bg-green-500/20 text-green-300 border-green-500/30";
        } else if (reasonLower.includes('deposit')) {
          badgeText = 'Deposit';
          badgeClass = "bg-purple-500/20 text-purple-300 border-purple-500/30";
        } else if (reasonLower.includes('withdrawal')) {
          badgeText = 'Withdrawal';
          badgeClass = "bg-red-500/20 text-red-300 border-red-500/30";
        }
      }

      return <Badge className={badgeClass}>{badgeText}</Badge>;
    } catch (err) {
      console.error('Error getting transaction badge:', err, tx);
      return <Badge className="bg-gray-500/20 text-gray-300 border-gray-500/30">Unknown</Badge>;
    }
  };

  return (
    <Card className="border-blue-500/20 bg-[#0f1c2e]/60 backdrop-blur-sm">
      <CardHeader className="pb-2">
        <div className="flex flex-col md:flex-row justify-between md:items-center gap-3">
          <div>
            <CardTitle className="text-lg font-medium">Platform Activity</CardTitle>
            <CardDescription className="text-xs text-gray-400">
              All platform transactions and activity
            </CardDescription>
          </div>

          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search transactions..."
              className="bg-blue-900/20 border border-blue-500/30 rounded-md pl-9 pr-4 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500/50 text-white w-full"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            {searchQuery && (
              <button
                className="absolute right-2.5 top-2.5 text-gray-400 hover:text-gray-300"
                onClick={() => setSearchQuery('')}
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {/* Loading state */}
        {isLoading && (
          <div className="space-y-4 py-2">
            <motion.div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4 h-20 animate-pulse" />
            <motion.div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4 h-20 animate-pulse" />
            <motion.div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4 h-20 animate-pulse" />
          </div>
        )}

        {/* Error state */}
        {error && (
          <div className="text-center py-8">
            <AlertCircle className="h-12 w-12 text-red-500/70 mx-auto mb-3" />
            <h3 className="text-lg font-medium text-white mb-2">Error Loading Transactions</h3>
            <p className="text-gray-400 max-w-md mx-auto">
              We couldn't load your transaction history. Please try again later.
            </p>
          </div>
        )}

        {/* Empty state */}
        {!isLoading && !error && filteredTransactions.length === 0 && (
          <div className="text-center py-8">
            <CreditCard className="h-12 w-12 text-blue-500/30 mx-auto mb-3" />
            <h3 className="text-lg font-medium text-white mb-2">No Transactions Found</h3>
            <p className="text-gray-400 max-w-md mx-auto">
              {searchQuery
                ? "No transactions match your search criteria. Try a different search term."
                : "You don't have any transactions yet. Start by adding funds or entering a challenge."}
            </p>
          </div>
        )}

        {/* Transaction list */}
        {!isLoading && !error && filteredTransactions.length > 0 && (
          <div className="space-y-3">
            {filteredTransactions.map((tx, index) => (
              <motion.div
                key={tx.id}
                className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4 hover:bg-blue-900/30 transition-colors duration-200"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2, delay: index * 0.05 }}
              >
                <div className="flex items-start gap-3">
                  <div className="rounded-full p-2 bg-blue-500/10 mt-1">
                    {getTransactionIcon(tx)}
                  </div>

                  <div className="flex-1">
                    <div className="flex flex-wrap items-center gap-2 mb-1">
                      <span className="font-medium text-white">{tx.reason}</span>
                      {getTransactionBadge(tx)}
                    </div>

                    <div className="flex flex-wrap items-center text-xs text-gray-400 gap-x-4 gap-y-1">
                      <div className="flex items-center">
                        <Calendar className="h-3.5 w-3.5 mr-1.5" />
                        <span>{new Date(tx.date).toLocaleDateString()}</span>
                      </div>

                      <div className="flex items-center capitalize">
                        <DollarSign className="h-3.5 w-3.5 mr-1.5" />
                        <span>
                          Amount: {formatCurrency(tx.amount)}
                        </span>
                      </div>
                    </div>
                  </div>

                  {tx.amount !== 0 && (
                    <div className={`text-lg font-bold font-mono ${
                      tx.amount > 0 ? 'text-green-400' : 'text-amber-400'
                    }`}>
                      {formatCurrency(tx.amount)}
                    </div>
                  )}
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

/**
 * Challenge Entries component displaying challenge-related transactions
 */
const ChallengeEntries: React.FC = () => {
  const [challengeType, setChallengeType] = useState("all");

  // Fetch transactions from API
  const { data: apiTransactions, isLoading, error } = useQuery({
    queryKey: ['transactions', 'challenges'],
    queryFn: async () => {
      try {
        const data = await apiService.getTransactions();
        console.log('Challenge transactions API response:', data);
        return Array.isArray(data) ? data : [];
      } catch (err) {
        console.error('Error fetching challenge transactions:', err);
        throw err;
      }
    },
    retry: 1, // Only retry once to avoid too many failed requests
  });

  // Filter challenge-related transactions
  const getChallengeTransactions = () => {
    if (!apiTransactions || !Array.isArray(apiTransactions)) return [];

    try {
      return apiTransactions.filter(tx => {
        if (!tx || typeof tx !== 'object') return false;

        try {
          // Filter transactions related to challenges
          const reason = tx.reason || '';
          const isChallengeTransaction =
            (typeof reason === 'string' && (
              reason.toLowerCase().includes('challenge') ||
              reason.toLowerCase().includes('prize') ||
              reason.toLowerCase().includes('entry fee')
            ));

          // Apply challenge type filter if not "all"
          if (challengeType !== "all") {
            return isChallengeTransaction &&
              typeof reason === 'string' &&
              reason.toLowerCase().includes(challengeType);
          }

          return isChallengeTransaction;
        } catch (err) {
          console.error('Error processing transaction in filter:', err, tx);
          return false;
        }
      });
    } catch (err) {
      console.error('Error filtering challenge transactions:', err);
      return [];
    }
  };

  const challengeTransactions = getChallengeTransactions();

  // Format currency
  const formatCurrency = (amount: number) => {
    return `${amount >= 0 ? '+' : '-'}$${Math.abs(amount).toFixed(2)}`;
  };

  return (
    <Card className="border-blue-500/20 bg-[#0f1c2e]/60 backdrop-blur-sm">
      <CardHeader className="pb-2">
        <div className="flex flex-col md:flex-row justify-between md:items-center gap-3">
          <div>
            <CardTitle className="text-lg font-medium">Challenge Entries</CardTitle>
            <CardDescription className="text-xs text-gray-400">
              Your challenge entry fees and prize winnings
            </CardDescription>
          </div>

          <div className="flex items-center gap-2">
            <div className="flex items-center">
              <Filter className="h-4 w-4 text-gray-400 mr-2" />
              <Select
                value={challengeType}
                onValueChange={setChallengeType}
              >
                <SelectTrigger className="w-[160px] text-sm h-9 bg-blue-900/20 border-blue-500/30">
                  <SelectValue placeholder="All Challenges" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Challenges</SelectItem>
                  <SelectItem value="daily">Daily Challenges</SelectItem>
                  <SelectItem value="weekly">Weekly Challenges</SelectItem>
                  <SelectItem value="monthly">Monthly Challenges</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {/* Loading state */}
        {isLoading && (
          <div className="space-y-4 py-2">
            <motion.div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4 h-20 animate-pulse" />
            <motion.div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4 h-20 animate-pulse" />
            <motion.div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4 h-20 animate-pulse" />
          </div>
        )}

        {/* Error state */}
        {error && (
          <div className="text-center py-8">
            <Award className="h-12 w-12 text-red-500/70 mx-auto mb-3" />
            <h3 className="text-lg font-medium text-white mb-2">Error Loading Challenge Entries</h3>
            <p className="text-gray-400 max-w-md mx-auto">
              We couldn't load your challenge entry transactions. Please try again later.
            </p>
          </div>
        )}

        {/* Empty state */}
        {!isLoading && !error && challengeTransactions.length === 0 && (
          <div className="text-center py-8">
            <Award className="h-12 w-12 text-blue-500/30 mx-auto mb-3" />
            <h3 className="text-lg font-medium text-white mb-2">No Challenge Entries Found</h3>
            <p className="text-gray-400 max-w-md mx-auto">
              {challengeType !== "all"
                ? `You haven't participated in any ${challengeType} challenges yet.`
                : "You haven't participated in any challenges yet. Enter a challenge to get started!"}
            </p>
          </div>
        )}

        {/* Challenge entries list */}
        {!isLoading && !error && challengeTransactions.length > 0 && (
          <div className="space-y-3">
            {challengeTransactions.map((tx, index) => (
              <motion.div
                key={tx.id}
                className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4 hover:bg-blue-900/30 transition-colors duration-200"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2, delay: index * 0.05 }}
              >
                <div className="flex items-start gap-3">
                  <div className="rounded-full p-2 bg-blue-500/10 mt-1">
                    <Award className="h-4 w-4 text-amber-400" />
                  </div>

                  <div className="flex-1">
                    <div className="flex flex-wrap items-center gap-2 mb-1">
                      <span className="font-medium text-white">{tx.reason}</span>
                      <Badge className={
                        tx.amount > 0 ?
                        "bg-green-500/20 text-green-300 border-green-500/30" :
                        "bg-blue-500/20 text-blue-300 border-blue-500/30"
                      }>
                        {tx.amount > 0 ? 'Prize' : 'Entry Fee'}
                      </Badge>
                    </div>

                    <div className="flex flex-wrap items-center text-xs text-gray-400 gap-x-4 gap-y-1">
                      <div className="flex items-center">
                        <Calendar className="h-3.5 w-3.5 mr-1.5" />
                        <span>{new Date(tx.date).toLocaleDateString()}</span>
                      </div>

                      {tx.amount > 0 && tx.reason.toLowerCase().includes('place') && (
                        <div className="flex items-center capitalize">
                          <Award className="h-3.5 w-3.5 mr-1.5" />
                          <span>{tx.reason.match(/\d+[a-z]{2} Place/i)?.[0] || 'Winner'}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className={`text-lg font-bold font-mono ${
                    tx.amount > 0 ? 'text-green-400' : 'text-amber-400'
                  }`}>
                    {formatCurrency(tx.amount)}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

/**
 * Transfers component displaying deposits and withdrawals
 */
const TransfersHistory: React.FC = () => {
  const [transferType, setTransferType] = useState("all");

  // Fetch transactions from API
  const { data: apiTransactions, isLoading, error } = useQuery({
    queryKey: ['transactions', 'transfers'],
    queryFn: async () => {
      try {
        const data = await apiService.getTransactions();
        console.log('Transfer transactions API response:', data);
        return Array.isArray(data) ? data : [];
      } catch (err) {
        console.error('Error fetching transfer transactions:', err);
        throw err;
      }
    },
    retry: 1, // Only retry once to avoid too many failed requests
  });

  // Map API transactions to transfer format
  const mapTransactionsToTransfers = () => {
    if (!apiTransactions || !Array.isArray(apiTransactions)) return [];

    try {
      return apiTransactions
        .filter(tx => {
          if (!tx || typeof tx !== 'object') return false;

          try {
            // Filter transactions related to deposits or withdrawals
            const reason = tx.reason || '';
            const type = tx.type || '';

            const isTransferTransaction =
              (typeof reason === 'string' && (
                reason.toLowerCase().includes('deposit') ||
                reason.toLowerCase().includes('withdrawal')
              )) ||
              (typeof type === 'string' && (
                type.toLowerCase().includes('deposit') ||
                type.toLowerCase().includes('withdrawal')
              ));

            return isTransferTransaction;
          } catch (err) {
            console.error('Error processing transaction in filter:', err, tx);
            return false;
          }
        })
        .map(tx => {
          try {
            const reason = tx.reason || '';
            const type = tx.type || '';

            // Determine transfer type
            let transferType = 'deposit';
            if ((typeof reason === 'string' && reason.toLowerCase().includes('withdrawal')) ||
                (typeof type === 'string' && type.toLowerCase().includes('withdrawal')) ||
                (typeof tx.amount === 'number' && tx.amount < 0)) {
              transferType = 'withdrawal';
            }

            // Determine payment method
            let method = 'Other';
            if (typeof reason === 'string') {
              if (reason.toLowerCase().includes('credit card')) {
                method = 'Credit Card';
              } else if (reason.toLowerCase().includes('bank')) {
                method = 'Bank Transfer';
              } else if (reason.toLowerCase().includes('paypal')) {
                method = 'PayPal';
              } else if (reason.toLowerCase().includes('crypto')) {
                method = 'Crypto';
              }
            }

            // Create transfer object
            return {
              id: String(tx.id || Math.random()),
              type: transferType,
              amount: Math.abs(tx.amount || 0),
              method,
              status: tx.status || 'completed',
              date: tx.date ? new Date(tx.date) : new Date(),
              reference: `TXN-${transferType === 'deposit' ? 'D' : 'W'}${tx.id || Math.random().toString(36).substring(2, 8)}`
            };
          } catch (err) {
            console.error('Error mapping transaction to transfer:', err, tx);
            return {
              id: String(Math.random()),
              type: 'deposit',
              amount: 0,
              method: 'Other',
              status: 'error',
              date: new Date(),
              reference: `TXN-ERROR-${Math.random().toString(36).substring(2, 8)}`
            };
          }
        });
    } catch (err) {
      console.error('Error mapping transactions to transfers:', err);
      return [];
    }
  };

  const transfers = mapTransactionsToTransfers();

  // Filter transfers based on selected type
  const filteredTransfers = transferType === 'all'
    ? transfers
    : transfers.filter(t => t.type === transferType);

  return (
    <Card className="border-blue-500/20 bg-[#0f1c2e]/60 backdrop-blur-sm">
      <CardHeader className="pb-2">
        <div className="flex flex-col md:flex-row justify-between md:items-center gap-3">
          <div>
            <CardTitle className="text-lg font-medium">Transfers</CardTitle>
            <CardDescription className="text-xs text-gray-400">
              Your deposits and withdrawals
            </CardDescription>
          </div>

          <div className="flex items-center gap-2">
            <div className="flex items-center">
              <Filter className="h-4 w-4 text-gray-400 mr-2" />
              <Select
                value={transferType}
                onValueChange={setTransferType}
              >
                <SelectTrigger className="w-[160px] text-sm h-9 bg-blue-900/20 border-blue-500/30">
                  <SelectValue placeholder="All Transfers" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Transfers</SelectItem>
                  <SelectItem value="deposit">Deposits</SelectItem>
                  <SelectItem value="withdrawal">Withdrawals</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button variant="outline" size="sm" className="text-blue-300 border-blue-500/30 hover:bg-blue-500/10">
              <Plus className="h-4 w-4 mr-1" />
              New Deposit
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {/* Loading state */}
        {isLoading && (
          <div className="space-y-4 py-2">
            <motion.div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4 h-20 animate-pulse" />
            <motion.div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4 h-20 animate-pulse" />
            <motion.div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4 h-20 animate-pulse" />
          </div>
        )}

        {/* Error state */}
        {error && (
          <div className="text-center py-8">
            <DollarSign className="h-12 w-12 text-red-500/70 mx-auto mb-3" />
            <h3 className="text-lg font-medium text-white mb-2">Error Loading Transfers</h3>
            <p className="text-gray-400 max-w-md mx-auto">
              We couldn't load your transfer history. Please try again later.
            </p>
          </div>
        )}

        {/* Empty state */}
        {!isLoading && !error && filteredTransfers.length === 0 && (
          <div className="text-center py-8">
            <DollarSign className="h-12 w-12 text-blue-500/30 mx-auto mb-3" />
            <h3 className="text-lg font-medium text-white mb-2">No Transfers Found</h3>
            <p className="text-gray-400 max-w-md mx-auto">
              {transferType === 'deposit'
                ? "You haven't made any deposits yet. Add funds to participate in more challenges."
                : transferType === 'withdrawal'
                ? "You haven't made any withdrawals yet. Win challenges to earn withdrawable funds."
                : "You haven't made any transfers yet. Start by adding funds to your account."}
            </p>
          </div>
        )}

        {/* Transfers list */}
        {!isLoading && !error && filteredTransfers.length > 0 && (
          <div className="space-y-3">
            {filteredTransfers.map((transfer, index) => (
              <motion.div
                key={transfer.id}
                className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4 hover:bg-blue-900/30 transition-colors duration-200"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2, delay: index * 0.05 }}
              >
                <div className="flex items-start gap-3">
                  <div className="rounded-full p-2 bg-blue-500/10 mt-1">
                    {transfer.type === 'deposit' ?
                      <ArrowDownLeft className="h-4 w-4 text-green-400" /> :
                      <ArrowUpRight className="h-4 w-4 text-amber-400" />}
                  </div>

                  <div className="flex-1">
                    <div className="flex flex-wrap items-center gap-2 mb-1">
                      <span className="font-medium text-white">
                        {transfer.type === 'deposit' ? 'Deposit' : 'Withdrawal'} via {transfer.method}
                      </span>
                      <Badge className={
                        transfer.status === 'completed' ?
                        "bg-green-500/20 text-green-300 border-green-500/30" :
                        "bg-amber-500/20 text-amber-300 border-amber-500/30"
                      }>
                        {transfer.status === 'completed' ? 'Completed' : 'Pending'}
                      </Badge>
                    </div>

                    <div className="flex flex-wrap items-center text-xs text-gray-400 gap-x-4 gap-y-1">
                      <div className="flex items-center">
                        <Calendar className="h-3.5 w-3.5 mr-1.5" />
                        <span>{transfer.date.toLocaleDateString()}</span>
                      </div>

                      <div className="flex items-center capitalize">
                        <BanknoteIcon className="h-3.5 w-3.5 mr-1.5" />
                        <span>Method: {transfer.method}</span>
                      </div>

                      <div className="flex items-center">
                        <Clock className="h-3.5 w-3.5 mr-1.5" />
                        <span>Ref: {transfer.reference}</span>
                      </div>
                    </div>
                  </div>

                  <div className={`text-lg font-bold font-mono ${
                    transfer.type === 'deposit' ? 'text-green-400' : 'text-amber-400'
                  }`}>
                    {transfer.type === 'deposit' ? '+' : '-'}${transfer.amount.toFixed(2)}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

/**
 * Dashboard Transactions Page component
 * Shows all transaction types with tab navigation
 *
 * @returns TransactionsPage component
 */
const TransactionsPage: React.FC = () => {
  const location = useLocation();
  const [activeTab, setActiveTab] = useState("wallet");

  // Set active tab based on URL path
  useEffect(() => {
    const path = location.pathname;
    if (path.includes('/transactions/wallet')) {
      setActiveTab('wallet');
    } else if (path.includes('/transactions/challenges')) {
      setActiveTab('challenges');
    } else if (path.includes('/transactions/transfers')) {
      setActiveTab('transfers');
    }
  }, [location.pathname]);

  return (
    <DashboardLayout>
      <motion.div
        className="space-y-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-3xl font-bold tracking-tight">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-blue-600">
              Transactions
            </span>
          </h1>
        </div>

        <Tabs defaultValue="wallet" value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-3 mb-6">
            <TabsTrigger value="wallet" className="flex items-center gap-2">
              <CreditCard className="h-4 w-4" />
              <span className="hidden sm:inline">Wallet Credits</span>
            </TabsTrigger>
            <TabsTrigger value="challenges" className="flex items-center gap-2">
              <Award className="h-4 w-4" />
              <span className="hidden sm:inline">Challenge Entries</span>
            </TabsTrigger>
            <TabsTrigger value="transfers" className="flex items-center gap-2">
              <ArrowDownUp className="h-4 w-4" />
              <span className="hidden sm:inline">Transfers</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="wallet">
            <WalletCreditHistory />
          </TabsContent>

          <TabsContent value="challenges">
            <ChallengeEntries />
          </TabsContent>

          <TabsContent value="transfers">
            <TransfersHistory />
          </TabsContent>
        </Tabs>
      </motion.div>

      <Toaster position="top-right" />
    </DashboardLayout>
  );
};

export default TransactionsPage;