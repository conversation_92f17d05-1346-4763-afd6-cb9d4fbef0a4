import React, { useState, useEffect } from 'react';
import { DashboardHeader } from '@/features/dashboard';
import DynamicBackground from '@/features/dashboard/components/DynamicBackground';
import { useChallengeContext } from '@/features/dashboard/context/ChallengeContext';
import { useAnalyticsContext } from '../context/AnalyticsContext';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/common/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/common/components/ui/card';
import { Badge } from '@/common/components/ui/badge';
import { Activity, AlertTriangle, RefreshCw, Wifi, WifiOff, Database } from 'lucide-react';
import { socketService } from '@/common/services/socketService';
import { socketCacheService } from '@/common/services/socketCacheService';
import { motion, AnimatePresence } from 'framer-motion';
import { Toolt<PERSON>, TooltipContent, Too<PERSON><PERSON>Provider, TooltipTrigger } from '@/common/components/ui/tooltip';
import AnalyticsFilter from './AnalyticsFilter';

interface AnalyticsLayoutProps {
  children: React.ReactNode;
}

/**
 * Analytics layout component
 *
 * Provides the layout structure for the analytics page with challenge selection
 * and connection status indicators.
 *
 * @component
 * @example
 * <AnalyticsLayout>
 *   <AnalyticsContent />
 * </AnalyticsLayout>
 */
const AnalyticsLayout: React.FC<AnalyticsLayoutProps> = ({ children }) => {
  const { challenges } = useChallengeContext();
  const {
    selectedChallengeId,
    setSelectedChallengeId,
    isLoading,
    isConnected,
    refreshData,
    lastUpdate,
    updateCounts
  } = useAnalyticsContext();
  const [isReconnecting, setIsReconnecting] = useState<boolean>(false);
  const [cacheSize, setCacheSize] = useState<number>(0);
  const [cacheKeys, setCacheKeys] = useState<string[]>([]);

  // Get cache info
  useEffect(() => {
    const interval = setInterval(() => {
      setCacheSize(socketCacheService.size());
      setCacheKeys(socketCacheService.keys());
    }, 5000);

    // Initial update
    setCacheSize(socketCacheService.size());
    setCacheKeys(socketCacheService.keys());

    return () => clearInterval(interval);
  }, []);

  const handleChallengeChange = (value: string) => {
    setSelectedChallengeId(value);
  };

  const handleReconnect = () => {
    setIsReconnecting(true);
    socketService.reconnect();

    // Reset reconnecting state after animation
    setTimeout(() => {
      setIsReconnecting(false);
    }, 1000);
  };

  // Get total update count
  const getTotalUpdateCount = () => {
    return Object.values(updateCounts).reduce((sum, count) => sum + count, 0);
  };

  return (
    <DynamicBackground>
      <div className="container mx-auto px-4 py-6">
        <DashboardHeader title="Advanced Analytics" />

        <div className="mb-6 flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div className="flex-1">
            <h2 className="text-2xl font-bold text-white mb-2">Trading Analytics</h2>
            <p className="text-gray-300">
              Real-time analytics powered by WebSocket data from your trading activity.
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-4">
            <Select
              value={selectedChallengeId || ''}
              onValueChange={handleChallengeChange}
              disabled={isLoading}
            >
              <SelectTrigger className="w-[200px] bg-gray-800 border-gray-700">
                <SelectValue placeholder="Select Challenge" />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 border-gray-700">
                <SelectItem value="all">All Challenges</SelectItem>
                {challenges.map((challenge) => (
                  <SelectItem key={challenge.id} value={challenge.id}>
                    {challenge.title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Card className="bg-gray-800 border-gray-700 w-[250px]">
                    <CardContent className="p-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <AnimatePresence mode="wait">
                            {isConnected ? (
                              <motion.div
                                key="connected"
                                initial={{ opacity: 0, scale: 0.8 }}
                                animate={{ opacity: 1, scale: 1 }}
                                exit={{ opacity: 0, scale: 0.8 }}
                              >
                                <Badge className="bg-green-500/20 text-green-400 border border-green-600">
                                  <Wifi className="h-3 w-3 mr-1" />
                                  Connected
                                </Badge>
                              </motion.div>
                            ) : (
                              <motion.div
                                key="disconnected"
                                initial={{ opacity: 0, scale: 0.8 }}
                                animate={{ opacity: 1, scale: 1 }}
                                exit={{ opacity: 0, scale: 0.8 }}
                              >
                                <Badge className="bg-red-500/20 text-red-400 border border-red-600">
                                  <WifiOff className="h-3 w-3 mr-1" />
                                  Disconnected
                                </Badge>
                              </motion.div>
                            )}
                          </AnimatePresence>
                        </div>

                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="bg-blue-900/20 text-blue-400 border-blue-700 text-xs">
                            <Database className="h-3 w-3 mr-1" />
                            {cacheSize}
                          </Badge>

                          <motion.button
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={handleReconnect}
                            className="text-blue-400 hover:text-blue-300 transition-colors"
                            disabled={isReconnecting}
                          >
                            <AnimatePresence mode="wait">
                              {isReconnecting ? (
                                <motion.div
                                  key="reconnecting"
                                  initial={{ opacity: 0, rotate: 0 }}
                                  animate={{ opacity: 1, rotate: 360 }}
                                  exit={{ opacity: 0 }}
                                  transition={{ duration: 0.5 }}
                                >
                                  <RefreshCw className="h-4 w-4 text-blue-400" />
                                </motion.div>
                              ) : (
                                <motion.div
                                  key="reconnect"
                                  initial={{ opacity: 0 }}
                                  animate={{ opacity: 1 }}
                                  exit={{ opacity: 0 }}
                                >
                                  <RefreshCw className="h-4 w-4" />
                                </motion.div>
                              )}
                            </AnimatePresence>
                          </motion.button>
                        </div>
                      </div>

                      <div className="mt-2 text-xs text-gray-400 flex items-center justify-between">
                        <div>Updates: {getTotalUpdateCount()}</div>
                        <div>Cache: {cacheSize} items</div>
                      </div>
                    </CardContent>
                  </Card>
                </TooltipTrigger>
                <TooltipContent side="bottom" className="bg-gray-900 border-gray-700 text-xs max-w-[300px]">
                  <div className="space-y-2">
                    <div className="font-medium">WebSocket Status</div>
                    <div className="flex flex-col gap-1">
                      <div className="flex justify-between">
                        <span>Connection:</span>
                        <span className={isConnected ? 'text-green-400' : 'text-red-400'}>
                          {isConnected ? 'Connected' : 'Disconnected'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Total Updates:</span>
                        <span>{getTotalUpdateCount()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Cache Items:</span>
                        <span>{cacheSize}</span>
                      </div>
                    </div>
                    {cacheKeys.length > 0 && (
                      <div>
                        <div className="font-medium mt-2">Cached Data:</div>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {cacheKeys.map(key => (
                            <Badge key={key} variant="outline" className="text-[10px]">{key}</Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
          </div>
        ) : (
          <div className="space-y-6">
            <AnalyticsFilter />
            {children}
          </div>
        )}
      </div>
    </DynamicBackground>
  );
};

export default AnalyticsLayout;
