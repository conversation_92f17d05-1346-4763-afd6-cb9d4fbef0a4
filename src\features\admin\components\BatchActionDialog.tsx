/**
 * Batch Action Dialog Component
 * @description Dialog for performing batch actions on users
 * @version 1.0.0
 * @status stable
 */

import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/common/components/ui/dialog';
import { useSimpleDialogCleanup } from '@/common/hooks/useDialogCleanup';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/common/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/common/components/ui/select';
import { Button } from '@/common/components/ui/button';
import { Input } from '@/common/components/ui/input';
import { Label } from '@/common/components/ui/label';
import { RefreshCw, Users, Ban, CheckCircle, Wallet } from 'lucide-react';

interface BatchActionDialogProps {
  selectedCount: number;
  onStatusChange: (status: 'active' | 'banned') => Promise<void>;
  onCreditAdjustment: (amount: number, reason: string, operation: 'add' | 'deduct') => Promise<void>;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

/**
 * Batch action dialog component
 */
const BatchActionDialog: React.FC<BatchActionDialogProps> = ({
  selectedCount,
  onStatusChange,
  onCreditAdjustment,
  open,
  onOpenChange,
}) => {
  const [activeTab, setActiveTab] = useState<string>('status');
  const [loading, setLoading] = useState<boolean>(false);
  const [status, setStatus] = useState<'active' | 'banned'>('active');
  const [amount, setAmount] = useState<number>(0);
  const [reason, setReason] = useState<string>('');
  const [operation, setOperation] = useState<'add' | 'deduct'>('add');

  // Use cleanup hook to prevent UI freezing
  const handleOpenChange = useSimpleDialogCleanup(onOpenChange);

  // Handle status change
  const handleStatusChange = async () => {
    try {
      setLoading(true);
      await onStatusChange(status);
    } finally {
      setLoading(false);
    }
  };

  // Handle credit adjustment
  const handleCreditAdjustment = async () => {
    try {
      setLoading(true);
      await onCreditAdjustment(amount, reason, operation);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[500px] bg-forex-darker border-forex-border">
        <DialogHeader>
          <DialogTitle>Batch Actions</DialogTitle>
          <DialogDescription>
            Perform actions on {selectedCount} selected users
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-2">
            <TabsTrigger value="status" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Status Change
            </TabsTrigger>
            <TabsTrigger value="credits" className="flex items-center gap-2">
              <Wallet className="h-4 w-4" />
              Credit Adjustment
            </TabsTrigger>
          </TabsList>

          <TabsContent value="status" className="py-4">
            <div className="space-y-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="status" className="text-right">
                  New Status
                </Label>
                <Select
                  value={status}
                  onValueChange={(value) => setStatus(value as 'active' | 'banned')}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent className="bg-forex-darker border-forex-border">
                    <SelectItem value="active" className="flex items-center gap-2">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>Activate Users</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="banned" className="flex items-center gap-2">
                      <div className="flex items-center gap-2">
                        <Ban className="h-4 w-4 text-red-500" />
                        <span>Ban Users</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="credits" className="py-4">
            <div className="space-y-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="operation" className="text-right">
                  Operation
                </Label>
                <Select
                  value={operation}
                  onValueChange={(value) => setOperation(value as 'add' | 'deduct')}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select operation" />
                  </SelectTrigger>
                  <SelectContent className="bg-forex-darker border-forex-border">
                    <SelectItem value="add">Add Credits</SelectItem>
                    <SelectItem value="deduct">Deduct Credits</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="amount" className="text-right">
                  Amount
                </Label>
                <Input
                  id="amount"
                  type="number"
                  min="0"
                  step="0.01"
                  value={amount || ''}
                  onChange={(e) => setAmount(parseFloat(e.target.value) || 0)}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="reason" className="text-right">
                  Reason
                </Label>
                <Input
                  id="reason"
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                  className="col-span-3"
                />
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => handleOpenChange(false)}>
            Cancel
          </Button>
          <Button
            onClick={activeTab === 'status' ? handleStatusChange : handleCreditAdjustment}
            disabled={loading}
          >
            {loading ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              'Apply to Selected Users'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default BatchActionDialog;
