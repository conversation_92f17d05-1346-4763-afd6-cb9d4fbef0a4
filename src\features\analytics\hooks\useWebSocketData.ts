import { useState, useEffect } from 'react';
import { socketService } from '@/common/services/socketService';
import { SocketEvent } from '@/types/socketEvents';
import { TradeData, UserMetrics, DrawdownUpdate } from '../context/AnalyticsContext';

/**
 * Hook for handling WebSocket data for analytics
 * 
 * @param challengeEntryId - Optional challenge entry ID to filter data
 * @returns WebSocket data and connection status
 */
export const useWebSocketData = (challengeEntryId?: number) => {
  const [trades, setTrades] = useState<TradeData[]>([]);
  const [metrics, setMetrics] = useState<UserMetrics | null>(null);
  const [drawdown, setDrawdown] = useState<DrawdownUpdate | null>(null);
  const [isConnected, setIsConnected] = useState<boolean>(socketService.isConnected());
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Handle connection status changes
    const handleConnect = () => setIsConnected(true);
    const handleDisconnect = () => setIsConnected(false);
    
    // Handle trade events
    const handleTradeCreated = (data: TradeData) => {
      // Filter by challenge entry ID if provided
      if (challengeEntryId && data.challengeEntryId !== challengeEntryId) return;
      
      setTrades(prev => [data, ...prev]);
    };
    
    // Handle metrics updates
    const handleMetricsUpdated = (data: UserMetrics) => {
      setMetrics(data);
    };
    
    // Handle drawdown updates
    const handleDrawdownUpdate = (data: DrawdownUpdate) => {
      // Filter by challenge entry ID if provided
      if (challengeEntryId && data.challengeEntryId !== challengeEntryId) return;
      
      setDrawdown(data);
    };

    // Register event handlers
    socketService.on(SocketEvent.CONNECT, handleConnect);
    socketService.on(SocketEvent.DISCONNECT, handleDisconnect);
    socketService.on(SocketEvent.TRADE_CREATED, handleTradeCreated);
    socketService.on(SocketEvent.USER_METRICS_UPDATED, handleMetricsUpdated);
    socketService.on(SocketEvent.REAL_TIME_DRAWDOWN_UPDATE, handleDrawdownUpdate);

    // Set initial connection status
    setIsConnected(socketService.isConnected());
    setIsLoading(false);

    // Cleanup on unmount
    return () => {
      socketService.off(SocketEvent.CONNECT);
      socketService.off(SocketEvent.DISCONNECT);
      socketService.off(SocketEvent.TRADE_CREATED);
      socketService.off(SocketEvent.USER_METRICS_UPDATED);
      socketService.off(SocketEvent.REAL_TIME_DRAWDOWN_UPDATE);
    };
  }, [challengeEntryId]);

  // Reconnect function
  const reconnect = () => {
    try {
      socketService.reconnect();
    } catch (err) {
      setError('Failed to reconnect to WebSocket server');
    }
  };

  return {
    trades,
    metrics,
    drawdown,
    isConnected,
    isLoading,
    error,
    reconnect
  };
};
