import { useState, useEffect } from 'react';
import { Award, Target, Zap, Trophy, Clock } from 'lucide-react';
import { Achievement } from '../components/AchievementCelebration';

// Custom hook for managing achievements
export function useAchievements() {
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [completedAchievement, setCompletedAchievement] = useState<Achievement | null>(null);
  // Flag to disable auto-triggering for production
  const enableAutoTrigger = true;

  // Initialize achievements
  useEffect(() => {
    // Mock achievements data
    const mockAchievements: Achievement[] = [
      {
        id: '1',
        name: 'First Blood',
        description: 'Complete your first profitable trade',
        icon: Target,
        rarity: 'common'
      },
      {
        id: '2',
        name: 'Winning Streak',
        description: 'Complete 5 profitable trades in a row',
        icon: Zap,
        rarity: 'uncommon'
      },
      {
        id: '3',
        name: 'Diamond Hands',
        description: 'Hold a profitable position for more than 24 hours',
        icon: Clock,
        rarity: 'rare'
      },
      {
        id: '4',
        name: 'Risk Manager',
        description: 'Complete 10 trades with less than 1% risk per trade',
        icon: Award,
        rarity: 'epic'
      },
      {
        id: '5',
        name: 'Challenge Master',
        description: 'Win 3 trading challenges',
        icon: Trophy,
        rarity: 'legendary'
      }
    ];

    setAchievements(mockAchievements);
  }, []);

  // Complete an achievement by ID
  const completeAchievement = (achievementId: string) => {
    const achievement = achievements.find(a => a.id === achievementId);
    if (achievement) {
      setCompletedAchievement(achievement);
    }
  };

  // Clear the completed achievement
  const clearCompletedAchievement = () => {
    console.log('Clearing achievement');
    setCompletedAchievement(null);
  };

  // For demo purposes, trigger a random achievement after a delay
  // but only once per session and with a longer delay
  useEffect(() => {
    if (!enableAutoTrigger) return;

    // Check if we've already shown an achievement in this session
    const hasShownAchievement = sessionStorage.getItem('achievementShown');

    if (hasShownAchievement) {
      return; // Don't show another achievement in this session
    }

    // Use a much longer delay (30 seconds) to avoid frequent popups
    const timer = setTimeout(() => {
      if (achievements.length > 0 && !completedAchievement) {
        const randomIndex = Math.floor(Math.random() * achievements.length);
        console.log('Auto-triggering achievement:', achievements[randomIndex].name);
        setCompletedAchievement(achievements[randomIndex]);

        // Mark that we've shown an achievement in this session
        sessionStorage.setItem('achievementShown', 'true');
      }
    }, 30000); // 30 second delay

    return () => clearTimeout(timer);
  }, [achievements, completedAchievement]);

  return {
    achievements,
    completedAchievement,
    completeAchievement,
    clearCompletedAchievement
  };
}