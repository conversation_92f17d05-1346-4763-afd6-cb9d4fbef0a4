/**
 * Challenge Edit Dialog Component
 * @description Dialog for editing challenge details in the admin panel
 */

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/common/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/common/components/ui/form';
import { Button } from '@/common/components/ui/button';
import { Input } from '@/common/components/ui/input';
import { Separator } from '@/common/components/ui/separator';
import { ScrollArea } from '@/common/components/ui/scroll-area';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/common/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/common/components/ui/popover';
import { Calendar } from '@/common/components/ui/calendar';
import { Switch } from '@/common/components/ui/switch';
import { CalendarIcon, Edit, Loader2 } from 'lucide-react';
import { format, addDays } from 'date-fns';
import { useToast } from '@/common/components/ui/use-toast';
import { useSimpleDialogCleanup } from '@/common/hooks/useDialogCleanup';
import { adminApiService } from '../services/adminApiService';
import { Challenge, ChallengeStatus, ChallengeType } from '@/features/challenges/types';

// Form schema
const formSchema = z.object({
  name: z.string().min(3, 'Name must be at least 3 characters'),
  type: z.enum(['daily', 'weekly', 'monthly']),
  startDate: z.date(),
  endDate: z.date(),
  entryFee: z.coerce.number().min(0, 'Entry fee must be at least 0'),
  prizePool: z.coerce.number().min(0, 'Prize pool must be at least 0'),
  status: z.enum(['upcoming', 'active', 'completed', 'cancelled']),
});

interface ChallengeEditDialogProps {
  challenge: Challenge | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onChallengeUpdated: (challenge: Challenge) => void;
}

/**
 * Challenge edit dialog component
 */
const ChallengeEditDialog: React.FC<ChallengeEditDialogProps> = ({
  challenge,
  open,
  onOpenChange,
  onChallengeUpdated,
}) => {
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  // Use cleanup hook to prevent UI freezing
  const handleOpenChange = useSimpleDialogCleanup(onOpenChange);

  // Create form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      type: 'daily',
      startDate: new Date(),
      endDate: addDays(new Date(), 1),
      entryFee: 25,
      prizePool: 100,
      status: 'upcoming',
    },
  });

  // Update form values when challenge changes
  useEffect(() => {
    if (challenge) {
      form.reset({
        name: challenge.name || '',
        type: challenge.type as any,
        startDate: new Date(challenge.startDate),
        endDate: new Date(challenge.endDate),
        entryFee: challenge.entryFee,
        prizePool: challenge.prizePool,
        status: challenge.status as any,
      });
    }
  }, [challenge, form]);

  // Handle form submission
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    if (!challenge) return;

    try {
      setLoading(true);

      // Prepare challenge data
      const challengeData = {
        ...values,
        ruleset: challenge.ruleset, // Keep existing ruleset
      };

      // Update challenge
      const response = await adminApiService.challenge.updateChallenge(challenge.id, challengeData);

      if (response.success === false) {
        toast({
          title: 'Error',
          description: 'Failed to update challenge. Please try again.',
          variant: 'destructive',
        });
        return;
      }

      // Show success message
      toast({
        title: 'Challenge Updated',
        description: `${values.name} has been updated successfully.`,
      });

      // Call callback
      onChallengeUpdated(response);

      // Close dialog
      handleOpenChange(false);
    } catch (error) {
      console.error('Error updating challenge:', error);
      toast({
        title: 'Error',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[600px] bg-forex-darker border-forex-border text-white">
        <DialogHeader>
          <DialogTitle className="text-white flex items-center gap-2">
            <Edit className="h-5 w-5 text-blue-400" />
            Edit Challenge
          </DialogTitle>
          <DialogDescription className="text-white/80">
            Update challenge details and settings
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="max-h-[600px] pr-4">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-white">Challenge Name</FormLabel>
                    <FormControl>
                      <Input {...field} className="text-white bg-forex-dark border-forex-border" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">Challenge Type</FormLabel>
                      <Select
                        disabled={challenge?.status !== 'upcoming'}
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className="text-white bg-forex-dark border-forex-border">
                            <SelectValue placeholder="Select type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent className="bg-forex-dark border-forex-border">
                          <SelectItem value="daily">Daily</SelectItem>
                          <SelectItem value="weekly">Weekly</SelectItem>
                          <SelectItem value="monthly">Monthly</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">Status</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className="text-white bg-forex-dark border-forex-border">
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent className="bg-forex-dark border-forex-border">
                          <SelectItem value="upcoming">Upcoming</SelectItem>
                          <SelectItem value="active">Active</SelectItem>
                          <SelectItem value="completed">Completed</SelectItem>
                          <SelectItem value="cancelled">Cancelled</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="startDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel className="text-white">Start Date</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={"outline"}
                              className="text-white bg-forex-dark border-forex-border"
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0 bg-forex-dark border-forex-border">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => date < new Date()}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="endDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel className="text-white">End Date</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={"outline"}
                              className="text-white bg-forex-dark border-forex-border"
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0 bg-forex-dark border-forex-border">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => date <= form.getValues().startDate}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="entryFee"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">Entry Fee ($)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          step="0.01"
                          {...field}
                          className="text-white bg-forex-dark border-forex-border"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="prizePool"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">Prize Pool ($)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          step="0.01"
                          {...field}
                          className="text-white bg-forex-dark border-forex-border"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </form>
          </Form>
        </ScrollArea>

        <DialogFooter className="flex justify-end gap-2">
          <Button variant="outline" onClick={() => handleOpenChange(false)}>
            Cancel
          </Button>
          <Button
            onClick={form.handleSubmit(onSubmit)}
            disabled={loading}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ChallengeEditDialog;
