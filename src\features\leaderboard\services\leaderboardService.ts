/**
 * Leaderboard Service
 * @description Service for interacting with the leaderboard API
 * @version 1.0.0
 * @status stable
 */

import { apiService } from '@/common/services/api';
import { DetailedLeaderboardEntry } from '@/types/socketTypes';

/**
 * Leaderboard statistics
 */
export interface LeaderboardStats {
  challengeId: number;
  totalParticipants: number;
  averageScore: number;
  medianScore: number;
  topScore: number;
  bottomScore: number;
  scoreDistribution: {
    range: string;
    count: number;
  }[];
}

/**
 * Leaderboard snapshot
 */
export interface LeaderboardSnapshot {
  id: number;
  challengeId: number;
  snapshotTime: string;
  snapshotType: 'realtime' | 'daily' | 'final';
  entries: DetailedLeaderboardEntry[];
}

/**
 * User ranking history
 */
export interface UserRankingHistory {
  userId: string;
  username: string;
  challengeId: number;
  rankings: {
    snapshotTime: string;
    rank: number;
    score: number;
  }[];
}

/**
 * Leaderboard service for interacting with the leaderboard API
 */
export const leaderboardService = {
  /**
   * Get global leaderboard across all active challenges
   * @param limit - Maximum number of entries to return
   * @returns Promise with leaderboard entries
   */
  async getGlobalLeaderboard(limit: number = 50): Promise<DetailedLeaderboardEntry[]> {
    const response = await apiService.get(`/leaderboards/global?limit=${limit}`);
    return response.data;
  },
  /**
   * Get current leaderboard for a challenge
   * @param challengeId - Challenge ID
   * @returns Promise with leaderboard entries
   */
  async getCurrentLeaderboard(challengeId: number): Promise<DetailedLeaderboardEntry[]> {
    const response = await apiService.get(`/leaderboards/${challengeId}`);
    return response.data;
  },

  /**
   * Get leaderboard snapshots for a challenge
   * @param challengeId - Challenge ID
   * @param options - Filter options
   * @returns Promise with leaderboard snapshots
   */
  async getLeaderboardSnapshots(
    challengeId: number,
    options?: {
      startDate?: Date;
      endDate?: Date;
      limit?: number;
      offset?: number;
      snapshotType?: 'realtime' | 'daily' | 'final';
    }
  ): Promise<LeaderboardSnapshot[]> {
    const queryParams = new URLSearchParams();

    if (options?.startDate) {
      queryParams.append('startDate', options.startDate.toISOString());
    }

    if (options?.endDate) {
      queryParams.append('endDate', options.endDate.toISOString());
    }

    if (options?.limit) {
      queryParams.append('limit', options.limit.toString());
    }

    if (options?.offset) {
      queryParams.append('offset', options.offset.toString());
    }

    if (options?.snapshotType) {
      queryParams.append('snapshotType', options.snapshotType);
    }

    const queryString = queryParams.toString();
    const url = `/leaderboards/${challengeId}/snapshots${queryString ? `?${queryString}` : ''}`;

    const response = await apiService.get(url);
    return response.data;
  },

  /**
   * Get leaderboard statistics for a challenge
   * @param challengeId - Challenge ID
   * @returns Promise with leaderboard statistics
   */
  async getLeaderboardStatistics(challengeId: number): Promise<LeaderboardStats> {
    const response = await apiService.get(`/leaderboards/${challengeId}/statistics`);
    return response.data;
  },

  /**
   * Get user ranking history for a challenge
   * @param userId - User ID
   * @param challengeId - Challenge ID
   * @returns Promise with user ranking history
   */
  async getUserRankingHistory(userId: string, challengeId: number): Promise<UserRankingHistory> {
    const response = await apiService.get(`/leaderboards/users/${userId}/challenges/${challengeId}/history`);
    return response.data;
  },
};

export default leaderboardService;
