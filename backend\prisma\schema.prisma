generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Core MVP Models - Essential for Trading Competition Platform

model User {
  id                   String                @id
  walletAddress        String                @unique
  displayName          String?
  role                 String                @default("user")
  verifiedHost         Boolean               @default(false)
  cumulativeROI        Decimal               @default(0) @db.Decimal(10, 4)
  top3Finishes         Int                   @default(0)
  badges               Json?
  signupDate           DateTime              @default(now())
  lastLoginAt          DateTime?

  // Relations
  challengeEntries     ChallengeEntry[]
  prizeDistributions   PrizeDistribution[]
  hostApplications     HostApplication[]

  @@map("users")
}

model HostApplication {
  id        Int      @id @default(autoincrement())
  wallet    String
  socials   Json?
  notes     String?
  status    String   @default("pending") // pending, approved, rejected
  createdAt DateTime @default(now())
  user      User     @relation(fields: [wallet], references: [walletAddress])

  @@map("host_applications")
}

model Challenge {
  id                   Int                   @id @default(autoincrement())
  hostWallet           String
  name                 String
  description          String?
  bannerUrl            String?
  hostMessage          String?
  startAt              DateTime
  endAt                DateTime
  allowedSymbols       String[]
  startingBalance      Decimal               @db.Decimal(10, 2)
  entryFeeUSDC         Decimal               @db.Decimal(10, 2)
  platformFeeBps       Int                   @default(1000) // 10%
  hostRakeBps          Int                   @default(0) // 0%, 10%, or 20%
  prizePreset          String                // HOST_TAKES_ALL, WINNER_TAKES_ALL, TOP3, TOP5
  escrowAddress        String
  visibility           String                @default("public")
  status               String                @default("draft") // draft, live, ended, finalized, cancelled, under_review
  minEntrants          Int                   @default(5)
  maxEntrants          Int?
  disputeWindowEndsAt  DateTime?
  finalizedAt          DateTime?
  cancelledAt          DateTime?
  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @default(now()) @updatedAt

  // Relations
  challengeEntries     ChallengeEntry[]
  prizeDistributions   PrizeDistribution[]
  disputes             Dispute[]

  @@map("challenges")
}

model ChallengeEntry {
  id                      Int                      @id @default(autoincrement())
  challengeId             Int
  wallet                  String
  joinedAt                DateTime                 @default(now())
  entryTxHash             String
  isEligible              Boolean                  @default(true)
  startEquity             Decimal                  @db.Decimal(10, 2)
  refundTxHash            String?
  deviceFingerprintHash   String?

  // Relations
  challenge               Challenge                @relation(fields: [challengeId], references: [id])
  user                    User                     @relation(fields: [wallet], references: [walletAddress])
  orders                  Order[]
  positions               Position[]
  equitySnapshots         EquitySnapshot[]
  disputes                Dispute[]

  @@map("challenge_entries")
}

model Order {
  id               Int            @id @default(autoincrement())
  participantId    Int
  type             String         // market, limit
  side             String         // buy, sell
  symbol           String
  qty              Decimal        @db.Decimal(10, 6)
  limitPrice       Decimal?       @db.Decimal(10, 5)
  status           String         @default("open") // open, filled, cancelled
  createdAt        DateTime       @default(now())

  // Relations
  fills            Fill[]
  challengeEntry   ChallengeEntry @relation(fields: [participantId], references: [id])

  @@map("orders")
}

model Fill {
  id        Int      @id @default(autoincrement())
  orderId   Int
  price     Decimal  @db.Decimal(10, 5)
  qty       Decimal  @db.Decimal(10, 6)
  fee       Decimal  @db.Decimal(10, 2)
  timestamp DateTime @default(now())
  tickId    String

  // Relations
  order     Order    @relation(fields: [orderId], references: [id])

  @@map("fills")
}

model Position {
  participantId Int
  symbol        String
  qty           Decimal  @db.Decimal(10, 6)
  avgPrice      Decimal  @db.Decimal(10, 5)
  unrealizedPnl Decimal  @db.Decimal(10, 2)

  // Relations
  challengeEntry ChallengeEntry @relation(fields: [participantId], references: [id])

  @@unique([participantId, symbol])
  @@map("positions")
}

model EquitySnapshot {
  id               Int            @id @default(autoincrement())
  participantId    Int
  timestamp        DateTime       @default(now())
  equity           Decimal        @db.Decimal(10, 2)

  // Relations
  challengeEntry   ChallengeEntry @relation(fields: [participantId], references: [id])

  @@map("equity_snapshots")
}

model PrizeDistribution {
  id                Int       @id @default(autoincrement())
  challengeId       Int
  userId            String
  rank              Int
  amount            Decimal   @db.Decimal(10, 2)
  status            String    @default("pending")
  paymentAddress    String?
  paymentTxHash     String?
  paymentTimestamp  DateTime?
  approvalTimestamp DateTime?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // Relations
  challenge         Challenge @relation(fields: [challengeId], references: [id])
  user              User      @relation(fields: [userId], references: [id])

  @@index([challengeId])
  @@index([userId])
  @@index([status])
  @@map("prize_distributions")
}

model Dispute {
  id               Int            @id @default(autoincrement())
  challengeId      Int
  participantId    Int
  claim            String
  evidenceUrl      String?
  status           String         @default("open") // open, resolved, rejected
  createdAt        DateTime       @default(now())
  resolvedAt       DateTime?
  resolutionNotes  String?

  // Relations
  challenge        Challenge      @relation(fields: [challengeId], references: [id])
  challengeEntry   ChallengeEntry @relation(fields: [participantId], references: [id])

  @@map("disputes")
}
