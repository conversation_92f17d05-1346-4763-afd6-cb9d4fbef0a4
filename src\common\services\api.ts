/**
 * API Service
 * @description Service for making API requests to the backend with wallet authentication
 * @version 2.0.0
 * @status stable
 */

import { useAuthStore } from '@/features/auth/stores/authStore';

// Set default API URL if env variable is not defined
// @ts-ignore - Vite-specific environment variable
const API_URL = import.meta.env?.VITE_API_URL || 'http://localhost:5003';

// Check if API_URL already ends with /api to avoid duplication
const BASE_URL = API_URL.endsWith('/api') ? API_URL : `${API_URL}/api`;

/**
 * Get the auth token from the auth store
 * @returns The session token or null
 */
const getAuthToken = (): string | null => {
  try {
    const { sessionToken } = useAuthStore.getState();
    return sessionToken;
  } catch (error) {
    console.error('Error getting auth token:', error);
    return null;
  }
};

/**
 * Custom API error class for better error handling
 */
export class ApiError extends Error {
  status: number;
  code?: string;
  details?: any;

  constructor(message: string, status: number, code?: string, details?: any) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.code = code;
    this.details = details;
  }
}

/**
 * Fetch wrapper with error handling and authentication
 * @param url - API endpoint
 * @param options - Fetch options
 * @returns Promise with response data
 */
async function fetchWithErrorHandling(url: string, options: RequestInit = {}) {
  try {
    // Get the auth token
    const token = getAuthToken();

    // Add the auth token to the headers if it exists
    const headers = {
      'Content-Type': 'application/json',
      ...(options.headers || {}),
    } as Record<string, string>;

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    // Log API calls for debugging in development mode
    // @ts-ignore - Vite-specific environment variable
    const isDev = import.meta.env?.MODE === 'development';

    if (isDev && !url.includes('/users/sync')) {
      console.log(`Making API request to: ${url}`);
    }

    try {
      // Properly construct the API URL to avoid duplication of /api
      let apiUrl;

      // Remove leading slash from url if it exists to avoid double slashes
      const cleanUrl = url.startsWith('/') ? url.substring(1) : url;

      // If the URL already contains http://, it's an absolute URL
      if (cleanUrl.startsWith('http://') || cleanUrl.startsWith('https://')) {
        apiUrl = cleanUrl;
      } else {
        // Construct URL using BASE_URL
        // If cleanUrl already starts with 'api/', remove it to avoid duplication
        const apiPath = cleanUrl.startsWith('api/') ? cleanUrl.substring(4) : cleanUrl;
        apiUrl = `${BASE_URL}/${apiPath}`;

        // Log the constructed URL for debugging
        console.log(`Constructed API URL: ${apiUrl}`);
      }

      // Remove any double slashes in the URL (except after http:)
      apiUrl = apiUrl.replace(/([^:])\/\//g, '$1/');

      // Ensure we don't have /api/api/ in the URL
      apiUrl = apiUrl.replace(/\/api\/api\//g, '/api/');

      console.log(`Making API request to: ${apiUrl}`);

      // Add more detailed logging in development mode
      if (isDev) {
        console.log(`Request headers:`, headers);
      }

      const response = await fetch(apiUrl, {
        ...options,
        headers: headers as HeadersInit,
      });

      // Handle non-OK responses
      if (!response.ok) {
        let errorData: { message?: string; code?: string; [key: string]: any } = {};

        // Try to parse error response as JSON
        try {
          errorData = await response.json();
          // Log the detailed error response
          console.error(`API error response (${response.status}):`, errorData);
        } catch (e) {
          // If parsing fails, use a simple error object
          console.error(`Failed to parse error response: ${e}`);
          errorData = { message: `API error: ${response.status} ${response.statusText}` };
        }

        // Add more detailed logging and user-friendly messages for different error types
        if (response.status === 401) {
          console.error('Authentication error - token may be invalid or expired');
          console.log('URL:', apiUrl);
          console.log('Auth header:', headers['Authorization'] ? 'Present' : 'Missing');

          // Check if token is present
          const token = getAuthToken();
          if (!token) {
            console.error('No auth token available - user may not be logged in');
            errorData.message = 'Authentication failed: You may need to log in again';
          }
        }
        // Add more detailed logging for validation errors
        else if (response.status === 400) {
          console.error('Validation error - request data may be invalid');
          console.log('URL:', apiUrl);
          console.log('Request data:', options.body ? JSON.parse(options.body as string) : 'No body');

          // Provide more user-friendly error messages for common validation errors
          if (errorData.error) {
            if (errorData.error.includes('already entered')) {
              errorData.message = 'You have already entered this challenge. Please try a different challenge or check your active challenges.';
            } else if (errorData.error.includes('insufficient credits')) {
              errorData.message = 'You don\'t have enough wallet credits to enter this challenge. Please add more credits or use a different payment method.';
            } else if (errorData.error.includes('challenge not found')) {
              errorData.message = 'The selected challenge could not be found. It may have been removed or expired.';
            } else if (errorData.error.includes('challenge not active')) {
              errorData.message = 'This challenge is not currently active. Please select an active challenge.';
            } else if (errorData.error.includes('account already connected')) {
              errorData.message = 'This cTrader account is already connected to another challenge. Please select a different account.';
            } else if (errorData.error.includes('invalid account')) {
              errorData.message = 'The selected cTrader account is invalid. Please make sure it\'s a demo account and try again.';
            }
          }
        }
        // Add handling for not found errors
        else if (response.status === 404) {
          console.error('Resource not found');
          console.log('URL:', apiUrl);

          // Provide more user-friendly error messages for common 404 errors
          if (url.includes('/challenge-entries/')) {
            errorData.message = 'The requested challenge entry could not be found. It may have been removed or expired.';
          } else if (url.includes('/challenges/')) {
            errorData.message = 'The requested challenge could not be found. It may have been removed or expired.';
          } else if (url.includes('/metrics/')) {
            errorData.message = 'The requested metrics could not be found. This feature may not be available yet.';
          } else {
            errorData.message = 'The requested resource could not be found.';
          }
        }
        // Add handling for server errors
        else if (response.status >= 500) {
          console.error('Server error');
          console.log('URL:', apiUrl);
          errorData.message = 'The server encountered an error. Please try again later or contact support if the issue persists.';
        }

        // Throw a structured API error
        throw new ApiError(
          errorData.message || `API error: ${response.status}`,
          response.status,
          errorData.code,
          errorData
        );
      }

      // Parse successful response
      const data = await response.json();
      return data;
    } catch (fetchError) {
      // Handle network errors (like server not running)
      if (fetchError instanceof TypeError && fetchError.message.includes('Failed to fetch')) {
        console.error('Network error - server may be down:', fetchError);

        // Create a network error
        const networkError = new ApiError(
          'Network error: Unable to connect to the server. Please check your internet connection.',
          0,
          'NETWORK_ERROR'
        );

        // For journal endpoints, return empty array to show empty state instead of error
        if (url.includes('/api/journal')) {
          console.warn('Returning empty array for journal endpoint due to network error');
          return [];
        }

        throw networkError;
      }

      // Re-throw API errors
      throw fetchError;
    }
  } catch (error) {
    // Log all errors in development mode
    // @ts-ignore - Vite-specific environment variable
    const isDev = import.meta.env?.MODE === 'development';
    if (isDev) {
      console.error(`API request failed for ${url}:`, error);

      // For journal endpoints, return empty data to show empty state instead of error
      if (url.includes('/api/journal')) {
        console.log('Error in journal API call, returning empty data');
        if (url === '/api/journal' && options?.method === 'POST') {
          return { success: false, message: 'Failed to create journal entry' };
        } else if (url === '/api/journal') {
          return [];
        }
        return [];
      }
    }

    // Throw the error for the caller to handle
    throw error;
  }
}

/**
 * API service for making requests to the backend
 */
export const apiService = {
  /**
   * Generic GET request
   * @param url - API endpoint URL
   * @returns Promise with response data
   */
  get: (url: string) => fetchWithErrorHandling(url),

  /**
   * Generic POST request
   * @param url - API endpoint URL
   * @param data - Request body data
   * @returns Promise with response data
   */
  post: (url: string, data?: any) => fetchWithErrorHandling(url, {
    method: 'POST',
    body: JSON.stringify(data),
  }),

  /**
   * Generic PUT request
   * @param url - API endpoint URL
   * @param data - Request body data
   * @returns Promise with response data
   */
  put: (url: string, data?: any) => fetchWithErrorHandling(url, {
    method: 'PUT',
    body: JSON.stringify(data),
  }),

  /**
   * Generic DELETE request
   * @param url - API endpoint URL
   * @returns Promise with response data
   */
  delete: (url: string) => {
    console.log('API Service: Making DELETE request to:', url);
    return fetchWithErrorHandling(url, {
      method: 'DELETE',
    }).then(response => {
      console.log('API Service: DELETE response:', response);
      return response;
    }).catch(error => {
      console.error('API Service: DELETE error:', error);
      throw error;
    });
  },

  /**
   * Get health status of the API
   * @returns Promise with health status
   */
  getHealth: () => fetchWithErrorHandling('/health'),

  /**
   * Get all users (admin only)
   * @returns Promise with users data
   */
  getUsers: () => fetchWithErrorHandling('/users'),

  /**
   * Get user by ID
   * @param id - User ID
   * @returns Promise with user data
   */
  getUserById: (id: string) => fetchWithErrorHandling(`/users/${id}`),

  /**
   * Sync user data from Clerk to our database
   * @param userData - User data from Clerk
   * @returns Promise with synced user data
   */
  syncUserFromClerk: (userData: {
    id: string;
    email: string;
    username?: string;
    discordUsername?: string;
  }) => fetchWithErrorHandling('/users/sync', {
    method: 'POST',
    body: JSON.stringify(userData),
  }),

  /**
   * Update user's crypto address
   * @param cryptoAddress - Crypto wallet address
   * @returns Promise with updated user data
   */
  updateCryptoAddress: (cryptoAddress: string) => fetchWithErrorHandling('/users/crypto-address', {
    method: 'PUT',
    body: JSON.stringify({ cryptoAddress }),
  }),

  /**
   * Update user's username
   * @param username - New username
   * @returns Promise with updated user data
   */
  updateUsername: (username: string) => fetchWithErrorHandling('/users/username', {
    method: 'PUT',
    body: JSON.stringify({ username }),
  }),

  /**
   * Update user's Discord username
   * @param discordUsername - Discord username
   * @returns Promise with updated user data
   */
  updateDiscordUsername: (discordUsername: string) => fetchWithErrorHandling('/users/discord-username', {
    method: 'PUT',
    body: JSON.stringify({ discordUsername }),
  }),

  /**
   * Get user transactions
   * @returns Promise with transaction data
   */
  getTransactions: () => fetchWithErrorHandling('/transactions'),

  /**
   * Create a new transaction
   * @param transactionData - Transaction data
   * @returns Promise with transaction data
   */
  createTransaction: (transactionData: {
    amount: number;
    type: string;
    reason: string;
  }) => fetchWithErrorHandling('/transactions', {
    method: 'POST',
    body: JSON.stringify(transactionData),
  }),

  /**
   * Get user notifications
   * @returns Promise with notifications data
   */
  getUserNotifications: () => fetchWithErrorHandling('/notifications'),

  /**
   * Get cTrader authorization URL
   * @returns Promise with authorization URL
   */
  getCTraderAuthUrl: () => fetchWithErrorHandling('/ctrader/auth-url'),

  /**
   * Exchange cTrader authorization code for access token
   * @param data - Code and challenge entry ID
   * @returns Promise with success status
   */
  exchangeCTraderCode: (data: {
    code: string;
    challengeEntryId: number;
  }) => fetchWithErrorHandling('/ctrader/exchange-code', {
    method: 'POST',
    body: JSON.stringify(data),
  }),

  /**
   * Connect to cTrader WebSocket
   * @param data - Challenge entry ID and account ID
   * @returns Promise with success status
   */
  connectCTrader: (data: {
    challengeEntryId: number;
    accountId: string;
  }) => fetchWithErrorHandling('/ctrader/connect', {
    method: 'POST',
    body: JSON.stringify(data),
  }),

  /**
   * Disconnect from cTrader WebSocket
   * @param challengeEntryId - Challenge entry ID
   * @returns Promise with success status
   */
  disconnectCTrader: (challengeEntryId: number) => fetchWithErrorHandling(`/ctrader/disconnect/${challengeEntryId}`, {
    method: 'POST',
  }),

  /**
   * Refresh cTrader token
   * @param challengeEntryId - Challenge entry ID
   * @returns Promise with success status
   */
  refreshCTraderToken: (challengeEntryId: number) => fetchWithErrorHandling(`/ctrader/refresh-token/${challengeEntryId}`, {
    method: 'POST',
  }),

  /**
   * Get cTrader accounts
   * @param challengeEntryId - Challenge entry ID
   * @returns Promise with accounts
   */
  getCTraderAccounts: (challengeEntryId: number) => fetchWithErrorHandling(`/ctrader/accounts/${challengeEntryId}`),

  /**
   * Get challenge entry by ID
   * @param id - Challenge entry ID
   * @returns Promise with challenge entry
   */
  getChallengeEntryById: (id: number) => fetchWithErrorHandling(`/challenge-entries/${id}`),

  /**
   * Get trade metrics for a challenge entry
   * @param challengeEntryId - Challenge entry ID
   * @returns Promise with trade metrics
   */
  getTradeMetrics: (challengeEntryId: number) => fetchWithErrorHandling(`/trades/metrics/${challengeEntryId}`),

  /**
   * Get trades for a challenge entry
   * @param challengeEntryId - Challenge entry ID
   * @returns Promise with trades
   */
  getTradesByChallengeEntry: (challengeEntryId: number) => fetchWithErrorHandling(`/challenge-entries/${challengeEntryId}/trades`),

  /**
   * Get all metrics
   * @returns Promise with all metrics
   */
  getMetrics: () => fetchWithErrorHandling('/metrics/all'),

  /**
   * Get system metrics
   * @returns Promise with system metrics
   */
  getSystemMetrics: () => fetchWithErrorHandling('/metrics/system'),

  /**
   * Get connection metrics for a challenge entry
   * @param challengeEntryId - Challenge entry ID
   * @returns Promise with connection metrics
   */
  getConnectionMetrics: (challengeEntryId: number) => fetchWithErrorHandling(`/metrics/connections/${challengeEntryId}`),

  /**
   * Get token refresh metrics for a challenge entry
   * @param challengeEntryId - Challenge entry ID
   * @returns Promise with token refresh metrics
   */
  getTokenRefreshMetrics: (challengeEntryId: number) => fetchWithErrorHandling(`/metrics/token-refresh/${challengeEntryId}`),

  /**
   * Reset metrics for a challenge entry
   * @param challengeEntryId - Challenge entry ID
   * @returns Promise with success message
   */
  resetMetrics: (challengeEntryId: number) => fetchWithErrorHandling(`/metrics/reset/${challengeEntryId}`, {
    method: 'POST',
  }),

  /**
   * Reset system metrics
   * @returns Promise with success message
   */
  resetSystemMetrics: () => fetchWithErrorHandling('/metrics/reset-system', {
    method: 'POST',
  }),

  /**
   * Get unread notification count
   * @returns Promise with unread count
   */
  getUnreadNotificationCount: () => fetchWithErrorHandling('/notifications/count'),

  /**
   * Mark notification as read
   * @param id - Notification ID
   * @returns Promise with updated notification
   */
  markNotificationAsRead: (id: number) => fetchWithErrorHandling(`/notifications/${id}`, {
    method: 'PUT',
  }),

  /**
   * Mark all notifications as read
   * @returns Promise with count of updated notifications
   */
  markAllNotificationsAsRead: () => fetchWithErrorHandling('/notifications', {
    method: 'PUT',
  }),

  /**
   * Delete notification
   * @param id - Notification ID
   * @returns Promise with deleted notification
   */
  deleteNotification: (id: number) => fetchWithErrorHandling(`/notifications/${id}`, {
    method: 'DELETE',
  }),

  /**
   * Create notification (admin only)
   * @param data - Notification data
   * @returns Promise with created notification
   */
  createNotification: (data: {
    userId: string;
    title: string;
    description: string;
    priority?: 'low' | 'normal' | 'high';
  }) => fetchWithErrorHandling('/notifications', {
    method: 'POST',
    body: JSON.stringify(data),
  }),

  /**
   * Get connected accounts
   * @returns Promise with connected accounts
   */
  getConnectedAccounts: () => fetchWithErrorHandling('/connected-accounts'),

  /**
   * Get connected account by ID
   * @param id - Connected account ID
   * @returns Promise with connected account
   */
  getConnectedAccountById: (id: number) => fetchWithErrorHandling(`/connected-accounts/${id}`),

  /**
   * Get connected accounts by challenge type
   * @param type - Challenge type
   * @returns Promise with connected accounts
   */
  getConnectedAccountsByType: (type: string) => fetchWithErrorHandling(`/connected-accounts/type/${type}`),

  /**
   * Calculate rebalancing requirements
   * @param data - Account data
   * @returns Promise with rebalancing requirements
   */
  calculateRebalancing: (data: {
    accountId: string;
    challengeType: string;
    accessToken: string;
  }) => fetchWithErrorHandling('/connected-accounts/calculate-rebalancing', {
    method: 'POST',
    body: JSON.stringify(data),
  }),

  /**
   * Verify connected account balance
   * @param data - Account data
   * @returns Promise with verification result
   */
  verifyConnectedAccountBalance: (data: {
    connectedAccountId: number;
    accessToken: string;
  }) => fetchWithErrorHandling('/connected-accounts/verify-balance', {
    method: 'POST',
    body: JSON.stringify(data),
  }),

  /**
   * Mark account as rebalanced
   * @param id - Connected account ID
   * @param newBalance - New balance
   * @returns Promise with updated account
   */
  markAccountRebalanced: (id: number, newBalance: number) => fetchWithErrorHandling(`/connected-accounts/${id}/rebalanced`, {
    method: 'PUT',
    body: JSON.stringify({ newBalance }),
  }),

  /**
   * Get connected accounts for challenge type
   * @param type - Challenge type
   * @returns Promise with connected accounts
   */
  getConnectedAccountsForChallengeType: (type: string) => fetchWithErrorHandling(`/challenge-entries/connected-accounts/${type}`),

  /**
   * Verify account balance for challenge
   * @param data - Challenge and account data
   * @returns Promise with verification result
   */
  verifyAccountBalanceForChallenge: (data: {
    challengeId: number;
    accountId: string;
    accessToken: string;
  }) => fetchWithErrorHandling('/challenge-entries/verify-balance', {
    method: 'POST',
    body: JSON.stringify(data),
  }),

  /**
   * Mark challenge entry balance as verified
   * @param id - Challenge entry ID
   * @returns Promise with updated challenge entry
   */
  markBalanceVerified: (id: number) => fetchWithErrorHandling(`/challenge-entries/${id}/verify-balance`, {
    method: 'PUT',
  }),

  /**
   * Get challenge metrics for a specific challenge entry
   * @param challengeEntryId - Challenge entry ID
   * @returns Promise with challenge metrics
   */
  getChallengeMetrics: (challengeEntryId: number) => fetchWithErrorHandling(`/metrics/challenge/${challengeEntryId}`),

  /**
   * Get aggregated metrics across all user challenges
   * @returns Promise with aggregated metrics
   */
  getAggregatedMetrics: () => fetchWithErrorHandling('/metrics/aggregated'),

  /**
   * Get user's cTrader account IDs
   * @returns Promise with array of cTrader account IDs
   */
  getCtraderAccounts: () => fetchWithErrorHandling('/user/ctrader-accounts'),

  /**
   * Create a challenge entry with payment only (first step)
   * @param data - Challenge entry payment data
   * @returns Promise with challenge entry data
   */
  createChallengeEntryPayment: (data: {
    challengeId: number;
    useWalletCredits?: boolean;
    walletCreditAmount?: number;
  }) => fetchWithErrorHandling('/challenge-entries/payment', {
    method: 'POST',
    body: JSON.stringify(data),
  }),

  /**
   * Connect cTrader to an existing challenge entry (second step)
   * @param id - Challenge entry ID
   * @param data - cTrader connection data
   * @returns Promise with updated challenge entry
   */
  connectCTraderToChallengeEntry: (id: number, data: {
    challengeId: number;
    ctraderAccountId: string;
    ctraderAccessToken: string;
    ctraderRefreshToken: string;
    connectStatus?: string;
    connectedAccountId?: number;
    accountReused?: boolean;
  }) => fetchWithErrorHandling(`/challenge-entries/${id}/connect-ctrader`, {
    method: 'POST',
    body: JSON.stringify(data),
  }),

  /**
   * Mark a challenge entry connection as failed (allows retry)
   * @param id - Challenge entry ID
   * @param data - Error information
   * @returns Promise with updated challenge entry
   */
  markConnectionFailed: (id: number, data: {
    errorMessage: string;
  }) => fetchWithErrorHandling(`/challenge-entries/${id}/mark-connection-failed`, {
    method: 'POST',
    body: JSON.stringify(data),
  }),

  /**
   * Get pending challenge entries that need cTrader connection
   * @returns Promise with pending challenge entries
   */
  getPendingConnectionEntries: () => fetchWithErrorHandling('/challenge-entries/pending-connections'),

  /**
   * Create a free challenge entry (combines entry creation + cTrader connection)
   * @param data - Free challenge entry data
   * @returns Promise with challenge entry data
   */
  createFreeChallengeEntry: (data: {
    challengeId: number;
    ctraderAccountId: string;
    ctraderAccessToken: string;
    ctraderRefreshToken: string;
    connectedAccountId?: number;
    accountReused?: boolean;
  }) => fetchWithErrorHandling('/challenge-entries/free', {
    method: 'POST',
    body: JSON.stringify(data),
  }),

  /**
   * Create a challenge entry (legacy method)
   * @param data - Challenge entry data
   * @returns Promise with challenge entry data
   */
  createChallengeEntry: (data: {
    challengeId: number;
    ctraderAccountId: string;
    ctraderAccessToken: string;
    ctraderRefreshToken: string;
    connectStatus?: string;
    useWalletCredits?: boolean;
    walletCreditAmount?: number;
    connectedAccountId?: number;
    accountReused?: boolean;
  }) => fetchWithErrorHandling('/challenge-entries', {
    method: 'POST',
    body: JSON.stringify(data),
  }),

  /**
   * Get user challenge entries
   * @returns Promise with user challenge entries
   */
  getUserChallengeEntries: () => fetchWithErrorHandling('/challenge-entries/me'),

  /**
   * Get challenge by ID
   * @param id - Challenge ID
   * @returns Promise with challenge data
   */
  getChallengeById: (id: number) => fetchWithErrorHandling(`/challenges/${id}`),

  /**
   * Get all challenges with optional filters
   * @param params - Optional query parameters (status, type, etc.)
   * @returns Promise with challenges data
   */
  getChallenges: (params: Record<string, string> = {}) => {
    const queryParams = new URLSearchParams();

    // Add all params to query string
    Object.entries(params).forEach(([key, value]) => {
      queryParams.append(key, value);
    });

    const queryString = queryParams.toString();
    console.log(`Fetching challenges with query: ${queryString}`);

    // Log the full URL for debugging
    const url = `/challenges${queryString ? `?${queryString}` : ''}`;
    console.log(`Full challenges API URL: ${url}`);

    return fetchWithErrorHandling(url);
  },

  /**
   * Get user accounts
   * @returns Promise with user accounts
   */
  getUserAccounts: () => fetchWithErrorHandling('/connected-accounts'),

  /**
   * Get wallet credits
   * @returns Promise with wallet credits
   */
  getWalletCredits: () => fetchWithErrorHandling('/wallet/credits'),

  /**
   * Check if user has enough credits for a challenge
   * @param challengeId - Challenge ID
   * @returns Promise with credit check result
   */
  checkCreditsForChallenge: (challengeId: number) => fetchWithErrorHandling(`/wallet/check-credits/${challengeId}`),

  /**
   * Get user trades
   * @returns Promise with user trades
   */
  getUserTrades: () => fetchWithErrorHandling('/trades'),

  /**
   * Get admin dashboard stats
   * @returns Promise with admin dashboard stats
   */
  getAdminDashboardStats: () => fetchWithErrorHandling('/admin/dashboard/stats'),

  /**
   * Get challenge equity curve
   * @param challengeEntryId - Challenge entry ID
   * @param timeRange - Time range (1d, 1w, 1m, all)
   * @returns Promise with equity curve data
   */
  getChallengeEquityCurve: (challengeEntryId: number, timeRange: string = 'all') =>
    fetchWithErrorHandling(`/challenge-entries/${challengeEntryId}/equity-curve?range=${timeRange}`),

  /**
   * Get aggregated equity curve
   * @param timeRange - Time range (1d, 1w, 1m, all)
   * @returns Promise with aggregated equity curve data
   */
  getAggregatedEquityCurve: (timeRange: string = 'all') =>
    fetchWithErrorHandling(`/metrics/aggregated-equity-curve?range=${timeRange}`),

  /**
   * Get audit logs
   * @param params Query parameters for filtering audit logs
   * @returns Promise with audit logs and pagination
   */
  getAuditLogs: (params: Record<string, string>) => {
    const queryParams = new URLSearchParams();

    // Add all params to query string
    Object.entries(params).forEach(([key, value]) => {
      queryParams.append(key, value);
    });

    const queryString = queryParams.toString();
    return fetchWithErrorHandling(`/audit-logs${queryString ? `?${queryString}` : ''}`);
  },

  // Journal API methods
  getJournalEntries: () => fetchWithErrorHandling('/journal'),

  getJournalEntryById: (id: number) => fetchWithErrorHandling(`/journal/${id}`),

  createJournalEntry: (data: {
    title: string;
    content: string;
    tags?: string[];
    mood?: 'positive' | 'neutral' | 'negative';
    tradeIds?: string[];
    attachments?: {
      type: 'image' | 'chart' | 'file';
      url: string;
      name: string;
    }[];
    dayProfitability?: 'profitable' | 'unprofitable' | 'break-even';
    pnlAmount?: number;
  }) => fetchWithErrorHandling('/journal', {
    method: 'POST',
    body: JSON.stringify(data),
  }),

  updateJournalEntry: (id: number, data: {
    title: string;
    content: string;
    tags?: string[];
    mood?: 'positive' | 'neutral' | 'negative';
    tradeIds?: string[];
    attachments?: {
      type: 'image' | 'chart' | 'file';
      url: string;
      name: string;
    }[];
    dayProfitability?: 'profitable' | 'unprofitable' | 'break-even';
    pnlAmount?: number;
  }) => fetchWithErrorHandling(`/journal/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  }),

  deleteJournalEntry: (id: number) => fetchWithErrorHandling(`/journal/${id}`, {
    method: 'DELETE',
  }),

  toggleFavoriteJournalEntry: (id: number) => fetchWithErrorHandling(`/journal/${id}/favorite`, {
    method: 'PUT',
  }),

  // Add more API methods as needed
};

/**
 * API object for backward compatibility
 * @deprecated Use apiService instead
 */
export const api = {
  getJournalEntries: apiService.getJournalEntries,
  getJournalEntryById: apiService.getJournalEntryById,
  createJournalEntry: apiService.createJournalEntry,
  updateJournalEntry: apiService.updateJournalEntry,
  deleteJournalEntry: apiService.deleteJournalEntry,
  toggleFavoriteJournalEntry: apiService.toggleFavoriteJournalEntry,
};
