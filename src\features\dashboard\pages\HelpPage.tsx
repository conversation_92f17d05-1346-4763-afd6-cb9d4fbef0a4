/**
 * Dashboard Help & Support Page
 * 
 * Provides user documentation, FAQs, and support contact information
 * to assist users with the platform.
 * 
 * @status experimental
 * @version 1.0.0
 */

import React, { useState } from 'react';
import { DashboardLayout } from '@/features/dashboard';
import { 
  <PERSON><PERSON>, 
  <PERSON>bs<PERSON>ontent, 
  TabsList, 
  TabsTrigger,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Button,
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Separator,
  Input
} from '@/common/components/ui';
import {   HelpCircle,   Book,   MessageSquare,   Mail,   Video,  Search,  ExternalLink,  FileText,  Send,  ChevronRight} from 'lucide-react';
import { Link } from 'react-router-dom';
import { toast } from 'sonner';

/**
 * FAQ Section Component
 */
const FAQSection: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  
  // Mock FAQ data - in production this would come from an API
  const faqItems = [
    {
      question: 'How do challenges work?',
      answer: 'Challenges are trading competitions where you compete with other traders based on your performance metrics. Each challenge has specific rules like maximum drawdown, minimum trades, etc. You can join challenges by paying an entry fee, and winners receive prizes based on their rankings.'
    },
    {
      question: 'How are winners determined?',
      answer: 'Winners are determined based on their PnL percentage (profit and loss). In case of a tie, the trader with the lower maximum drawdown percentage will be ranked higher. The top 3 winners receive cryptocurrency rewards, while the top 30% receive wallet credits.'
    },
    {
      question: 'What are wallet credits?',
      answer: 'Wallet credits are an internal virtual currency that can be used to enter future challenges. Credits expire after 30 days of inactivity and can be combined with cryptocurrency for partial payments when entering challenges.'
    },
    {
      question: 'How do I connect my cTrader account?',
      answer: 'You can connect your cTrader account by going to the Challenges page, selecting a challenge to join, and following the connection flow after payment. This involves authorizing our platform to access your trading data using OAuth2.'
    },
    {
      question: 'What happens if I get disqualified?',
      answer: 'If you violate any of the challenge rules (exceeding maximum drawdown, too few trades, etc.), you will be automatically disqualified. Disqualified traders cannot win prizes but can still view their performance metrics and learn from the experience.'
    },
    {
      question: 'How do I receive my prizes?',
      answer: 'Cryptocurrency prizes are sent to the wallet address specified in your profile settings. Wallet credits are automatically added to your account after the challenge ends and results are finalized by the admin.'
    },
    {
      question: 'Is there a minimum/maximum trade duration?',
      answer: 'Yes, to prevent scalping, there is typically a minimum trade duration requirement. The specific duration varies by challenge type and is clearly specified in the challenge rules.'
    },
    {
      question: 'Can I use the same cTrader account for multiple challenges?',
      answer: 'Yes, you can reuse a cTrader account for challenges of the same type (e.g., all daily challenges). The platform automatically handles account rebalancing between challenges.'
    }
  ];
  
  // Filter FAQ items based on search query
  const filteredFaqItems = searchQuery 
    ? faqItems.filter(item => 
        item.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.answer.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : faqItems;
  
  return (
    <Card className="border-blue-500/20 bg-[#0f1c2e]/60 backdrop-blur-sm">
      <CardHeader>
        <CardTitle className="text-lg font-medium">Frequently Asked Questions</CardTitle>
        <CardDescription>
          Find answers to the most common questions
        </CardDescription>
        
        <div className="relative mt-2">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-400" />
          <Input
            type="text"
            placeholder="Search FAQs..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="bg-blue-900/20 border-blue-500/30 pl-9 focus-visible:ring-blue-500/50"
          />
        </div>
      </CardHeader>
      
      <CardContent>
        <Accordion type="single" collapsible className="w-full">
          {filteredFaqItems.length > 0 ? (
            filteredFaqItems.map((item, index) => (
              <AccordionItem 
                key={index} 
                value={`item-${index}`}
                className="border-blue-500/20"
              >
                <AccordionTrigger className="text-sm font-medium hover:text-blue-400 transition-colors">
                  {item.question}
                </AccordionTrigger>
                <AccordionContent className="text-sm text-gray-300">
                  {item.answer}
                </AccordionContent>
              </AccordionItem>
            ))
          ) : (
            <div className="py-8 text-center">
              <HelpCircle className="mx-auto h-10 w-10 text-gray-400" />
              <p className="mt-2 text-gray-400">No FAQs found matching your search</p>
              <Button 
                variant="outline" 
                className="mt-4"
                onClick={() => setSearchQuery('')}
              >
                Clear Search
              </Button>
            </div>
          )}
        </Accordion>
      </CardContent>
    </Card>
  );
};

/**
 * Documentation Section Component
 */
const DocumentationSection: React.FC = () => {
  // Documentation categories and articles
  const documentationCategories = [
    {
      title: 'Getting Started',
      icon: <Book className="h-5 w-5 text-blue-400" />,
      articles: [
        { title: 'Platform Overview', link: '#', isNew: false },
        { title: 'Creating Your Account', link: '#', isNew: false },
        { title: 'Your First Challenge', link: '#', isNew: true },
        { title: 'Understanding Metrics', link: '#', isNew: false }
      ]
    },
    {
      title: 'Trading Challenges',
      icon: <FileText className="h-5 w-5 text-green-400" />,
      articles: [
        { title: 'Challenge Types Explained', link: '#', isNew: false },
        { title: 'Rules & Disqualification', link: '#', isNew: false },
        { title: 'Prize Distribution', link: '#', isNew: false },
        { title: 'Leaderboard Rankings', link: '#', isNew: false }
      ]
    },
    {
      title: 'cTrader Integration',
      icon: <ExternalLink className="h-5 w-5 text-amber-400" />,
      articles: [
        { title: 'Connecting Your Account', link: '#', isNew: false },
        { title: 'Account Security', link: '#', isNew: false },
        { title: 'Troubleshooting Connection Issues', link: '#', isNew: true },
        { title: 'Account Reuse Policy', link: '#', isNew: false }
      ]
    },
    {
      title: 'Wallet & Payments',
      icon: <FileText className="h-5 w-5 text-purple-400" />,
      articles: [
        { title: 'Wallet Credits Explained', link: '#', isNew: false },
        { title: 'Payment Methods', link: '#', isNew: false },
        { title: 'Prize Withdrawal Process', link: '#', isNew: false },
        { title: 'Credit Expiration Policy', link: '#', isNew: false }
      ]
    }
  ];
  
  return (
    <Card className="border-blue-500/20 bg-[#0f1c2e]/60 backdrop-blur-sm">
      <CardHeader>
        <CardTitle className="text-lg font-medium">Documentation</CardTitle>
        <CardDescription>
          Browse our comprehensive documentation
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {documentationCategories.map((category, index) => (
            <div 
              key={index} 
              className="bg-blue-900/20 border border-blue-500/20 rounded-lg p-4"
            >
              <div className="flex items-center gap-3 mb-3">
                {category.icon}
                <h3 className="font-medium text-white">{category.title}</h3>
              </div>
              
              <Separator className="bg-blue-500/20 my-3" />
              
              <ul className="space-y-3">
                {category.articles.map((article, articleIndex) => (
                  <li key={articleIndex}>
                    <Link 
                      to={article.link} 
                      className="flex items-center justify-between text-sm text-gray-300 hover:text-blue-400 transition-colors"
                    >
                      <span>{article.title}</span>
                      {article.isNew && (
                        <span className="text-xs bg-blue-500/20 text-blue-300 px-2 py-0.5 rounded-full">
                          New
                        </span>
                      )}
                    </Link>
                  </li>
                ))}
              </ul>
              
              <Button 
                variant="ghost" 
                size="sm" 
                className="w-full mt-3 text-xs text-blue-400 hover:text-blue-300 hover:bg-blue-500/10"
              >
                View All Articles
                <ChevronRight className="ml-1 h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
        
        <div className="mt-6 bg-blue-800/20 border border-blue-500/30 rounded-lg p-4">
          <div className="flex items-center gap-3 mb-2">
            <Video className="h-5 w-5 text-blue-400" />
            <h3 className="font-medium text-white">Video Tutorials</h3>
          </div>
          
          <p className="text-sm text-gray-300 mb-3">
            Learn visually with our step-by-step video guides
          </p>
          
          <Button variant="outline" className="w-full">
            Browse Video Library
            <ExternalLink className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * Contact Support Section Component
 */
const ContactSupportSection: React.FC = () => {
  const [message, setMessage] = useState('');
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // This would submit to the support backend API
    toast.success('Support message sent! We\'ll get back to you soon.');
    setMessage('');
  };
  
  return (
    <Card className="border-blue-500/20 bg-[#0f1c2e]/60 backdrop-blur-sm">
      <CardHeader>
        <CardTitle className="text-lg font-medium">Contact Support</CardTitle>
        <CardDescription>
          Get help from our support team
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-blue-900/20 border border-blue-500/20 rounded-lg p-4 flex flex-col items-center justify-center">
              <MessageSquare className="h-10 w-10 text-blue-400 mb-3" />
              <h3 className="font-medium text-white mb-1">Discord Support</h3>
              <p className="text-xs text-gray-400 text-center mb-4">
                Join our Discord community for quick support
              </p>
              <Button variant="outline" className="w-full">
                Join Discord
                <ExternalLink className="ml-2 h-4 w-4" />
              </Button>
            </div>
            
            <div className="bg-blue-900/20 border border-blue-500/20 rounded-lg p-4 flex flex-col items-center justify-center">
              <Mail className="h-10 w-10 text-blue-400 mb-3" />
              <h3 className="font-medium text-white mb-1">Email Support</h3>
              <p className="text-xs text-gray-400 text-center mb-4">
                Email our support team directly
              </p>
              <Button variant="outline" className="w-full">
                <EMAIL>
              </Button>
            </div>
          </div>
          
          <Separator className="bg-blue-500/20" />
          
          <div>
            <h3 className="font-medium text-white mb-3">Send a Message</h3>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="support-message" className="text-sm">
                  Your Message
                </label>
                <textarea
                  id="support-message"
                  placeholder="Describe your issue or question in detail..."
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  className="w-full h-32 bg-blue-900/20 border border-blue-500/30 rounded-md p-3 text-white focus:outline-none focus:ring-1 focus:ring-blue-500/50 resize-none"
                  required
                />
              </div>
              
              <div className="flex justify-end">
                <Button type="submit" disabled={!message.trim()}>
                  <Send className="mr-2 h-4 w-4" />
                  Send Message
                </Button>
              </div>
            </form>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * Main Help page component
 */
const HelpPage: React.FC = () => {
  return (
    <DashboardLayout>
      <div className="space-y-6 pb-12">
        <div className="space-y-0.5">
          <h2 className="text-2xl font-bold tracking-tight">Help & Support</h2>
          <p className="text-gray-400">
            Find answers, documentation, and support for TradeChampionX.
          </p>
        </div>
        
        <Tabs defaultValue="faq" className="w-full">
          <TabsList className="mb-6 bg-blue-900/20 border border-blue-500/30">
            <TabsTrigger value="faq" className="data-[state=active]:bg-blue-500/20">
              <HelpCircle className="h-4 w-4 mr-2" />
              FAQ
            </TabsTrigger>
            <TabsTrigger value="documentation" className="data-[state=active]:bg-blue-500/20">
              <Book className="h-4 w-4 mr-2" />
              Documentation
            </TabsTrigger>
            <TabsTrigger value="contact" className="data-[state=active]:bg-blue-500/20">
              <MessageSquare className="h-4 w-4 mr-2" />
              Contact Support
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="faq" className="space-y-4 mt-0">
            <FAQSection />
          </TabsContent>
          
          <TabsContent value="documentation" className="space-y-4 mt-0">
            <DocumentationSection />
          </TabsContent>
          
          <TabsContent value="contact" className="space-y-4 mt-0">
            <ContactSupportSection />
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default HelpPage; 