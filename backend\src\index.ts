/**
 * TradeChampionX MVP Backend Server
 * Clean, minimal server for crypto trading competition platform
 */

import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import http from 'http';
import { PrismaClient } from '@prisma/client';

// Load environment variables
dotenv.config();

// Initialize Express app
const app = express();
const port = process.env.PORT || 5003;

// Create HTTP server
const server = http.createServer(app);

// Initialize Prisma client
const prisma = new PrismaClient();

// Middleware
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'TradeChampionX API is running' });
});

// ===== CHALLENGE ROUTES =====

// Get all live challenges
app.get('/api/challenges', async (req, res) => {
  try {
    const challenges = await prisma.challenge.findMany({
      where: { status: 'live' },
      orderBy: { createdAt: 'desc' },
      include: {
        challengeEntries: {
          select: {
            id: true,
            wallet: true
          }
        }
      }
    });
    
    // Add participant count to each challenge
    const challengesWithStats = challenges.map(challenge => ({
      ...challenge,
      participantCount: challenge.challengeEntries.length
    }));
    
    res.json(challengesWithStats);
  } catch (error) {
    console.error('Error fetching challenges:', error);
    res.status(500).json({ error: 'Failed to fetch challenges' });
  }
});

// Get challenge by ID
app.get('/api/challenges/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const challenge = await prisma.challenge.findUnique({
      where: { id: parseInt(id) },
      include: {
        challengeEntries: {
          include: {
            user: {
              select: {
                id: true,
                displayName: true,
                walletAddress: true
              }
            },
            equitySnapshots: {
              orderBy: { timestamp: 'desc' },
              take: 1
            }
          }
        }
      }
    });
    
    if (!challenge) {
      return res.status(404).json({ error: 'Challenge not found' });
    }
    
    res.json(challenge);
  } catch (error) {
    console.error('Error fetching challenge:', error);
    res.status(500).json({ error: 'Failed to fetch challenge' });
  }
});

// ===== USER ROUTES =====

// Get current user (placeholder for wallet auth)
app.get('/api/user/me', async (req, res) => {
  try {
    // TODO: Extract wallet address from JWT token
    const walletAddress = req.headers['x-wallet-address'] as string;
    
    if (!walletAddress) {
      return res.status(401).json({ error: 'Wallet address required' });
    }
    
    let user = await prisma.user.findUnique({
      where: { walletAddress }
    });
    
    // Create user if doesn't exist
    if (!user) {
      user = await prisma.user.create({
        data: {
          id: walletAddress,
          walletAddress,
          displayName: `${walletAddress.slice(0, 6)}...${walletAddress.slice(-4)}`
        }
      });
    }
    
    res.json(user);
  } catch (error) {
    console.error('Error fetching user:', error);
    res.status(500).json({ error: 'Failed to fetch user' });
  }
});

// ===== HOST APPLICATION ROUTES =====

// Submit host application
app.post('/api/host/apply', async (req, res) => {
  try {
    const { wallet, socials, notes } = req.body;
    
    if (!wallet) {
      return res.status(400).json({ error: 'Wallet address required' });
    }
    
    // Check if application already exists
    const existingApplication = await prisma.hostApplication.findFirst({
      where: { wallet }
    });
    
    if (existingApplication) {
      return res.status(400).json({ error: 'Application already submitted' });
    }
    
    const application = await prisma.hostApplication.create({
      data: {
        wallet,
        socials,
        notes,
        status: 'pending'
      }
    });
    
    res.json(application);
  } catch (error) {
    console.error('Error creating host application:', error);
    res.status(500).json({ error: 'Failed to submit application' });
  }
});

// ===== LEADERBOARD ROUTES =====

// Get challenge leaderboard
app.get('/api/challenges/:id/leaderboard', async (req, res) => {
  try {
    const { id } = req.params;
    
    const entries = await prisma.challengeEntry.findMany({
      where: { 
        challengeId: parseInt(id),
        isEligible: true
      },
      include: {
        user: {
          select: {
            displayName: true,
            walletAddress: true,
            badges: true
          }
        },
        equitySnapshots: {
          orderBy: { timestamp: 'desc' },
          take: 1
        }
      }
    });
    
    // Calculate ROI and rank
    const leaderboard = entries
      .map(entry => {
        const latestEquity = entry.equitySnapshots[0]?.equity || entry.startEquity;
        const roi = ((Number(latestEquity) - Number(entry.startEquity)) / Number(entry.startEquity)) * 100;
        
        return {
          rank: 0, // Will be set after sorting
          walletAddress: entry.user.walletAddress,
          displayName: entry.user.displayName,
          equity: Number(latestEquity),
          roi: roi,
          badges: entry.user.badges
        };
      })
      .sort((a, b) => b.roi - a.roi)
      .map((entry, index) => ({
        ...entry,
        rank: index + 1
      }));
    
    res.json(leaderboard);
  } catch (error) {
    console.error('Error fetching leaderboard:', error);
    res.status(500).json({ error: 'Failed to fetch leaderboard' });
  }
});

// Start server
server.listen(port, () => {
  console.log(`🚀 TradeChampionX MVP Server running on port ${port}`);
  console.log(`📊 Health check: http://localhost:${port}/health`);
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('SIGTERM received, shutting down gracefully');
  await prisma.$disconnect();
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', async () => {
  console.log('SIGINT received, shutting down gracefully');
  await prisma.$disconnect();
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});
