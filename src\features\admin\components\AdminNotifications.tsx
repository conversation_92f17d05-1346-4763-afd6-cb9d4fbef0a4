/**
 * Admin Notifications Component
 * @description Component for managing notifications in the admin panel
 * @version 1.0.0
 * @status stable
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  Button,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Textarea,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Badge,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
  toast
} from '@/common/components/ui';
import {
  Send,
  Users,
  User,
  AlertTriangle,
  Info,
  Bell,
  Search,
  RefreshCw,
  Trash2
} from 'lucide-react';
import { apiService } from '@/common/services/api';
import { adminApiService } from '../services/adminApiService';
import { useNotifications, useUsers } from '../hooks/useAdminData';
import { formatDistanceToNow } from 'date-fns';

// Define notification type
interface Notification {
  id: number;
  userId: string;
  title: string;
  message: string;
  seen: boolean;
  type: string;
  priority: 'low' | 'normal' | 'high';
  createdAt: string;
  user?: {
    id: string;
    username: string;
    email: string;
  };
}

// Define user type
interface User {
  id: string;
  email: string;
  username: string;
}

/**
 * Admin notifications component
 * @returns JSX element
 */
const AdminNotifications: React.FC = () => {
  // State for notification form
  const [title, setTitle] = useState('');
  const [message, setMessage] = useState('');
  const [type, setType] = useState<'info' | 'success' | 'warning' | 'error'>('info');
  const [priority, setPriority] = useState<'low' | 'normal' | 'high'>('normal');
  const [selectedUserId, setSelectedUserId] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [notificationType, setNotificationType] = useState<'single' | 'batch' | 'system'>('single');
  const [selectedUserIds, setSelectedUserIds] = useState<string[]>([]);

  // Use the notifications hook
  const {
    notifications,
    loading,
    pagination,
    fetchNotifications,
    createNotification,
    createBatchNotification,
    createSystemNotification,
    deleteNotification,
    changePage
  } = useNotifications();

  // Use the users hook
  const {
    users,
    loading: userLoading,
    fetchUsers
  } = useUsers();

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!title || !message) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      let success = false;

      if (notificationType === 'single') {
        if (!selectedUserId) {
          toast.error('Please select a user');
          return;
        }

        success = await createNotification({
          userId: selectedUserId,
          title,
          message,
          type,
          priority
        });
      } else if (notificationType === 'batch') {
        if (selectedUserIds.length === 0) {
          toast.error('Please select at least one user');
          return;
        }

        success = await createBatchNotification({
          userIds: selectedUserIds,
          title,
          message,
          type,
          priority
        });
      } else if (notificationType === 'system') {
        success = await createSystemNotification({
          title,
          message,
          type,
          priority
        });
      }

      if (success) {
        // Reset form
        setTitle('');
        setMessage('');
        setType('info');
        setPriority('normal');
        setSelectedUserId('');
        setSelectedUserIds([]);
        setNotificationType('single');
      }
    } catch (error) {
      console.error('Error sending notification:', error);
    }
  };

  // Handle user selection for batch notifications
  const toggleUserSelection = (userId: string) => {
    if (selectedUserIds.includes(userId)) {
      setSelectedUserIds(selectedUserIds.filter(id => id !== userId));
    } else {
      setSelectedUserIds([...selectedUserIds, userId]);
    }
  };

  // Handle delete notification
  const handleDeleteNotification = async (id: number) => {
    await deleteNotification(id);
  };

  // Filter users based on search query
  const filteredUsers = users.filter(user =>
    user.username.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Notification Management</h2>
        <Button
          onClick={() => fetchNotifications()}
          variant="outline"
          size="sm"
          disabled={loading}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      <Tabs defaultValue="send">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="send">Send Notification</TabsTrigger>
          <TabsTrigger value="history">Notification History</TabsTrigger>
        </TabsList>

        <TabsContent value="send" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Send New Notification</CardTitle>
              <CardDescription>
                Send a notification to a specific user or all users
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Notification Type</label>
                  <Select
                    value={notificationType}
                    onValueChange={(value) => setNotificationType(value as 'single' | 'batch' | 'system')}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select notification type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="single">Single User</SelectItem>
                      <SelectItem value="batch">Multiple Users</SelectItem>
                      <SelectItem value="system">All Users (System)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Title</label>
                  <Input
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    placeholder="Notification title"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Message</label>
                  <Textarea
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    placeholder="Notification message"
                    required
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Type</label>
                    <Select
                      value={type}
                      onValueChange={(value) => setType(value as 'info' | 'success' | 'warning' | 'error')}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="info">Info</SelectItem>
                        <SelectItem value="success">Success</SelectItem>
                        <SelectItem value="warning">Warning</SelectItem>
                        <SelectItem value="error">Error</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Priority</label>
                    <Select
                      value={priority}
                      onValueChange={(value) => setPriority(value as 'low' | 'normal' | 'high')}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select priority" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="normal">Normal</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {notificationType !== 'system' && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      {notificationType === 'single' ? 'Select User' : 'Select Users'}
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <Search className="h-4 w-4 text-forex-muted" />
                      </div>
                      <Input
                        placeholder="Search users..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10"
                      />
                    </div>

                    <div className="border rounded-md mt-2 max-h-40 overflow-y-auto">
                      {userLoading ? (
                        <div className="flex items-center justify-center h-20">
                          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-forex-primary"></div>
                        </div>
                      ) : filteredUsers.length === 0 ? (
                        <div className="flex flex-col items-center justify-center h-20 text-forex-muted">
                          <Users className="h-5 w-5 mb-2 opacity-50" />
                          <p className="text-sm">No users found</p>
                        </div>
                      ) : (
                        <div className="divide-y">
                          {filteredUsers.map((user) => (
                            <div
                              key={user.id}
                              className={`p-2 cursor-pointer hover:bg-forex-hover flex items-center ${
                                notificationType === 'single'
                                  ? selectedUserId === user.id ? 'bg-forex-hover' : ''
                                  : selectedUserIds.includes(user.id) ? 'bg-forex-hover' : ''
                              }`}
                              onClick={() => {
                                if (notificationType === 'single') {
                                  setSelectedUserId(user.id);
                                } else {
                                  toggleUserSelection(user.id);
                                }
                              }}
                            >
                              <div className="h-8 w-8 rounded-full bg-forex-primary flex items-center justify-center mr-3">
                                {user.username.charAt(0).toUpperCase()}
                              </div>
                              <div className="flex-1">
                                <p className="text-sm font-medium">{user.username}</p>
                                <p className="text-xs text-forex-muted">{user.email}</p>
                              </div>
                              {notificationType === 'batch' && (
                                <div className="flex items-center">
                                  <input
                                    type="checkbox"
                                    checked={selectedUserIds.includes(user.id)}
                                    onChange={() => toggleUserSelection(user.id)}
                                    className="h-4 w-4"
                                  />
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>

                    {notificationType === 'batch' && (
                      <div className="text-sm text-forex-muted mt-1">
                        {selectedUserIds.length} users selected
                      </div>
                    )}
                  </div>
                )}

                <Button type="submit" className="w-full">
                  <Send className="h-4 w-4 mr-2" />
                  {notificationType === 'single'
                    ? 'Send to User'
                    : notificationType === 'batch'
                      ? `Send to ${selectedUserIds.length} Users`
                      : 'Send to All Users'
                  }
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Notification History</CardTitle>
              <CardDescription>
                View all notifications sent to users
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center h-40">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-forex-primary"></div>
                </div>
              ) : notifications.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-40 text-forex-muted">
                  <Bell className="h-8 w-8 mb-3 opacity-50" />
                  <p>No notifications found</p>
                </div>
              ) : (
                <div>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Title</TableHead>
                        <TableHead>User</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Priority</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Time</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {notifications.map((notification) => (
                        <TableRow key={notification.id}>
                          <TableCell className="font-medium">{notification.title}</TableCell>
                          <TableCell>
                            {notification.user ? (
                              <div className="flex items-center gap-2">
                                <div className="h-6 w-6 rounded-full bg-forex-primary flex items-center justify-center text-white">
                                  {notification.user.username.charAt(0).toUpperCase()}
                                </div>
                                <span>{notification.user.username}</span>
                              </div>
                            ) : (
                              notification.userId
                            )}
                          </TableCell>
                          <TableCell>
                            <Badge variant={
                              notification.type === 'error' ? 'destructive' :
                              notification.type === 'warning' ? 'warning' :
                              notification.type === 'success' ? 'success' :
                              'default'
                            }>
                              {notification.type}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant={
                              notification.priority === 'high' ? 'destructive' :
                              notification.priority === 'normal' ? 'default' :
                              'outline'
                            }>
                              {notification.priority}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant={notification.seen ? 'outline' : 'secondary'}>
                              {notification.seen ? 'Read' : 'Unread'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}
                          </TableCell>
                          <TableCell>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleDeleteNotification(notification.id)}
                            >
                              <Trash2 className="h-4 w-4 text-forex-muted hover:text-red-500" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>

                  {/* Pagination */}
                  <div className="flex items-center justify-between mt-4">
                    <div className="text-sm text-forex-muted">
                      Showing {notifications.length > 0 ? (pagination.page - 1) * pagination.limit + 1 : 0} to {Math.min(pagination.page * pagination.limit, pagination.totalCount)} of {pagination.totalCount} notifications
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => changePage(pagination.page - 1)}
                        disabled={pagination.page <= 1 || loading}
                      >
                        Previous
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => changePage(pagination.page + 1)}
                        disabled={pagination.page >= pagination.totalPages || loading}
                      >
                        Next
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdminNotifications;
