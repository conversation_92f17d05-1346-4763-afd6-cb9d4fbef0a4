import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/common/components/ui/card';
import { useAnalyticsContext } from '../context/AnalyticsContext';
import { Clock, Calendar, CheckCircle, XCircle } from 'lucide-react';
import InfoButton from './InfoButton';

/**
 * Time analysis component
 *
 * Analyzes trading performance by time of day and day of week.
 *
 * @component
 * @example
 * <TimeAnalysis />
 */
const TimeAnalysis: React.FC = () => {
  const { timeStats, trades } = useAnalyticsContext();

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  // Calculate day of week stats
  const dayOfWeekStats = React.useMemo(() => {
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const stats: Record<string, { totalTrades: number; winningTrades: number; pnl: number }> = {};

    // Initialize days
    days.forEach(day => {
      stats[day] = { totalTrades: 0, winningTrades: 0, pnl: 0 };
    });

    // Calculate stats
    trades.forEach(trade => {
      const day = days[new Date(trade.entryTime).getDay()];
      stats[day].totalTrades += 1;
      if (trade.pnl > 0) {
        stats[day].winningTrades += 1;
      }
      stats[day].pnl += trade.pnl;
    });

    return stats;
  }, [trades]);

  // Get best and worst times
  const getBestAndWorstTimes = () => {
    if (Object.keys(timeStats).length === 0) return { best: null, worst: null };

    let bestTime = '';
    let bestPnl = -Infinity;
    let worstTime = '';
    let worstPnl = Infinity;

    Object.entries(timeStats).forEach(([time, stats]) => {
      if (stats.totalTrades >= 3) {
        if (stats.pnl > bestPnl) {
          bestPnl = stats.pnl;
          bestTime = time;
        }
        if (stats.pnl < worstPnl) {
          worstPnl = stats.pnl;
          worstTime = time;
        }
      }
    });

    return {
      best: bestTime ? { time: bestTime, pnl: bestPnl, stats: timeStats[bestTime] } : null,
      worst: worstTime ? { time: worstTime, pnl: worstPnl, stats: timeStats[worstTime] } : null
    };
  };

  // Get best and worst days
  const getBestAndWorstDays = () => {
    if (Object.keys(dayOfWeekStats).length === 0) return { best: null, worst: null };

    let bestDay = '';
    let bestPnl = -Infinity;
    let worstDay = '';
    let worstPnl = Infinity;

    Object.entries(dayOfWeekStats).forEach(([day, stats]) => {
      if (stats.totalTrades >= 3) {
        if (stats.pnl > bestPnl) {
          bestPnl = stats.pnl;
          bestDay = day;
        }
        if (stats.pnl < worstPnl) {
          worstPnl = stats.pnl;
          worstDay = day;
        }
      }
    });

    return {
      best: bestDay ? { day: bestDay, pnl: bestPnl, stats: dayOfWeekStats[bestDay] } : null,
      worst: worstDay ? { day: worstDay, pnl: worstPnl, stats: dayOfWeekStats[worstDay] } : null
    };
  };

  const bestWorstTimes = getBestAndWorstTimes();
  const bestWorstDays = getBestAndWorstDays();

  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  };

  return (
    <Card className="border-gray-800 bg-gray-900/60 backdrop-blur-sm overflow-hidden">
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5 text-purple-400" />
          <div className="flex items-center gap-1.5">
            Time-Based Analysis
            <InfoButton
              content="Analyzes your trading performance by time of day and day of week to identify optimal trading times and patterns."
              className="ml-1"
            />
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {Object.keys(timeStats).length === 0 ? (
          <div className="text-center py-6 text-gray-400">
            <p>Not enough trade data for time-based analysis.</p>
          </div>
        ) : (
          <motion.div
            variants={container}
            initial="hidden"
            animate="show"
            className="space-y-6"
          >
            {/* Time of Day Analysis */}
            <motion.div variants={item}>
              <h3 className="text-sm font-medium text-gray-300 mb-3 flex items-center">
                <Clock className="h-4 w-4 mr-2 text-blue-400" />
                <div className="flex items-center gap-1.5">
                  Trading Hours Performance
                  <InfoButton
                    content="Identifies your most and least profitable trading hours to help optimize your trading schedule."
                    className="ml-1"
                    position="right"
                  />
                </div>
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Best Time */}
                {bestWorstTimes.best && (
                  <div className="bg-green-900/20 border border-green-900/30 rounded-lg p-3">
                    <div className="flex items-center gap-2 mb-2">
                      <CheckCircle className="h-4 w-4 text-green-400" />
                      <div className="text-sm font-medium text-green-400">Best Time to Trade</div>
                    </div>
                    <div className="text-xl font-bold">{bestWorstTimes.best.time}</div>
                    <div className="text-sm text-gray-400 mt-1">
                      Win Rate: {(bestWorstTimes.best.stats.winningTrades / bestWorstTimes.best.stats.totalTrades * 100).toFixed(1)}%
                    </div>
                    <div className="text-sm text-green-400 mt-1">
                      P&L: {formatCurrency(bestWorstTimes.best.pnl)}
                    </div>
                  </div>
                )}

                {/* Worst Time */}
                {bestWorstTimes.worst && (
                  <div className="bg-red-900/20 border border-red-900/30 rounded-lg p-3">
                    <div className="flex items-center gap-2 mb-2">
                      <XCircle className="h-4 w-4 text-red-400" />
                      <div className="text-sm font-medium text-red-400">Worst Time to Trade</div>
                    </div>
                    <div className="text-xl font-bold">{bestWorstTimes.worst.time}</div>
                    <div className="text-sm text-gray-400 mt-1">
                      Win Rate: {(bestWorstTimes.worst.stats.winningTrades / bestWorstTimes.worst.stats.totalTrades * 100).toFixed(1)}%
                    </div>
                    <div className="text-sm text-red-400 mt-1">
                      P&L: {formatCurrency(bestWorstTimes.worst.pnl)}
                    </div>
                  </div>
                )}
              </div>
            </motion.div>

            {/* Day of Week Analysis */}
            <motion.div variants={item}>
              <h3 className="text-sm font-medium text-gray-300 mb-3 flex items-center">
                <Calendar className="h-4 w-4 mr-2 text-purple-400" />
                <div className="flex items-center gap-1.5">
                  Day of Week Performance
                  <InfoButton
                    content="Shows which days of the week yield the best and worst trading results to help you plan your trading schedule."
                    className="ml-1"
                    position="right"
                  />
                </div>
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Best Day */}
                {bestWorstDays.best && (
                  <div className="bg-green-900/20 border border-green-900/30 rounded-lg p-3">
                    <div className="flex items-center gap-2 mb-2">
                      <CheckCircle className="h-4 w-4 text-green-400" />
                      <div className="text-sm font-medium text-green-400">Best Day to Trade</div>
                    </div>
                    <div className="text-xl font-bold">{bestWorstDays.best.day}</div>
                    <div className="text-sm text-gray-400 mt-1">
                      Win Rate: {(bestWorstDays.best.stats.winningTrades / bestWorstDays.best.stats.totalTrades * 100).toFixed(1)}%
                    </div>
                    <div className="text-sm text-green-400 mt-1">
                      P&L: {formatCurrency(bestWorstDays.best.pnl)}
                    </div>
                  </div>
                )}

                {/* Worst Day */}
                {bestWorstDays.worst && (
                  <div className="bg-red-900/20 border border-red-900/30 rounded-lg p-3">
                    <div className="flex items-center gap-2 mb-2">
                      <XCircle className="h-4 w-4 text-red-400" />
                      <div className="text-sm font-medium text-red-400">Worst Day to Trade</div>
                    </div>
                    <div className="text-xl font-bold">{bestWorstDays.worst.day}</div>
                    <div className="text-sm text-gray-400 mt-1">
                      Win Rate: {(bestWorstDays.worst.stats.winningTrades / bestWorstDays.worst.stats.totalTrades * 100).toFixed(1)}%
                    </div>
                    <div className="text-sm text-red-400 mt-1">
                      P&L: {formatCurrency(bestWorstDays.worst.pnl)}
                    </div>
                  </div>
                )}
              </div>
            </motion.div>
          </motion.div>
        )}
      </CardContent>
    </Card>
  );
};

export default TimeAnalysis;
