/**
 * Challenge Detail Dialog Component
 * @description Dialog for viewing challenge details in the admin panel
 */

import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/common/components/ui/dialog';
import { useSimpleDialogCleanup } from '@/common/hooks/useDialogCleanup';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/common/components/ui/card';
import { Button } from '@/common/components/ui/button';
import { Badge } from '@/common/components/ui/badge';
import { Separator } from '@/common/components/ui/separator';
import { ScrollArea } from '@/common/components/ui/scroll-area';
import {
  Calendar,
  Clock,
  DollarSign,
  Users,
  Trophy,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Percent,
  BarChart,
  Scale,
  Timer,
  Hash,
} from 'lucide-react';
import { formatCurrency, formatDate } from '@/common/utils/formatters';
import { Challenge, ChallengeStatus } from '@/features/challenges/types';

interface ChallengeDetailDialogProps {
  challenge: Challenge | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

/**
 * Challenge detail dialog component
 */
const ChallengeDetailDialog: React.FC<ChallengeDetailDialogProps> = ({
  challenge,
  open,
  onOpenChange,
}) => {
  // Use cleanup hook to prevent UI freezing
  const handleOpenChange = useSimpleDialogCleanup(onOpenChange);

  if (!challenge) return null;

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'active':
        return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'completed':
        return 'bg-purple-500/20 text-purple-400 border-purple-500/30';
      case 'cancelled':
        return 'bg-red-500/20 text-red-400 border-red-500/30';
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  // Format challenge duration
  const formatDuration = () => {
    const start = new Date(challenge.startDate);
    const end = new Date(challenge.endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return '1 day';
    if (diffDays < 7) return `${diffDays} days`;
    if (diffDays === 7) return '1 week';
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks`;
    if (diffDays >= 30 && diffDays < 60) return '1 month';
    return `${Math.ceil(diffDays / 30)} months`;
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[800px] bg-forex-darker border-forex-border text-white">
        <DialogHeader>
          <DialogTitle className="text-white flex items-center gap-2">
            <Trophy className="h-5 w-5 text-blue-400" />
            {challenge.name}
          </DialogTitle>
          <DialogDescription className="text-white/80">
            Challenge ID: {challenge.id}
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="max-h-[600px] pr-4">
          <div className="space-y-6">
            {/* Challenge overview */}
            <div className="flex flex-wrap gap-3">
              <Badge variant="outline" className={getStatusColor(challenge.status)}>
                {challenge.status.charAt(0).toUpperCase() + challenge.status.slice(1)}
              </Badge>
              <Badge variant="outline" className="bg-forex-dark border-forex-border">
                <Calendar className="h-3.5 w-3.5 mr-1.5" />
                {challenge.type.charAt(0).toUpperCase() + challenge.type.slice(1)}
              </Badge>
              <Badge variant="outline" className="bg-forex-dark border-forex-border">
                <Clock className="h-3.5 w-3.5 mr-1.5" />
                {formatDuration()}
              </Badge>
              <Badge variant="outline" className="bg-forex-dark border-forex-border">
                <DollarSign className="h-3.5 w-3.5 mr-1.5" />
                {formatCurrency(challenge.entryFee)}
              </Badge>
              <Badge variant="outline" className="bg-forex-dark border-forex-border">
                <Trophy className="h-3.5 w-3.5 mr-1.5" />
                {formatCurrency(challenge.prizePool)}
              </Badge>
              {challenge._count && (
                <Badge variant="outline" className="bg-forex-dark border-forex-border">
                  <Users className="h-3.5 w-3.5 mr-1.5" />
                  {challenge._count.challengeEntries} Participants
                </Badge>
              )}
            </div>

            {/* Challenge dates */}
            <Card className="bg-forex-dark border-forex-border">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-white">Timeline</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-xs text-gray-400">Start Date</p>
                  <p className="text-sm font-medium">{formatDate(challenge.startDate)}</p>
                </div>
                <div>
                  <p className="text-xs text-gray-400">End Date</p>
                  <p className="text-sm font-medium">{formatDate(challenge.endDate)}</p>
                </div>
              </CardContent>
            </Card>

            {/* Challenge rules */}
            <Card className="bg-forex-dark border-forex-border">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-white">Rules</CardTitle>
                <CardDescription className="text-xs text-gray-400">
                  Trading rules and requirements for this challenge
                </CardDescription>
              </CardHeader>
              <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {challenge.ruleset && (
                  <>
                    <div className="flex items-center gap-2">
                      <BarChart className="h-4 w-4 text-blue-400" />
                      <div>
                        <p className="text-xs text-gray-400">Initial Balance</p>
                        <p className="text-sm font-medium">
                          {formatCurrency(challenge.ruleset.initialBalance)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <AlertTriangle className="h-4 w-4 text-yellow-400" />
                      <div>
                        <p className="text-xs text-gray-400">Max Drawdown</p>
                        <p className="text-sm font-medium">
                          {challenge.ruleset.maxDrawdownPercent}%
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Scale className="h-4 w-4 text-blue-400" />
                      <div>
                        <p className="text-xs text-gray-400">Max Risk Per Trade</p>
                        <p className="text-sm font-medium">
                          {challenge.ruleset.maxRiskPerTradePercent}%
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Hash className="h-4 w-4 text-blue-400" />
                      <div>
                        <p className="text-xs text-gray-400">Minimum Trades</p>
                        <p className="text-sm font-medium">{challenge.ruleset.minTrades}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Timer className="h-4 w-4 text-blue-400" />
                      <div>
                        <p className="text-xs text-gray-400">Min Trade Duration</p>
                        <p className="text-sm font-medium">
                          {challenge.ruleset.minTradeDurationMinutes} minutes
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {challenge.ruleset.noHedging ? (
                        <CheckCircle className="h-4 w-4 text-green-400" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-400" />
                      )}
                      <div>
                        <p className="text-xs text-gray-400">No Hedging</p>
                        <p className="text-sm font-medium">
                          {challenge.ruleset.noHedging ? 'Required' : 'Allowed'}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {challenge.ruleset.noMartingale ? (
                        <CheckCircle className="h-4 w-4 text-green-400" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-400" />
                      )}
                      <div>
                        <p className="text-xs text-gray-400">No Martingale</p>
                        <p className="text-sm font-medium">
                          {challenge.ruleset.noMartingale ? 'Required' : 'Allowed'}
                        </p>
                      </div>
                    </div>
                    {challenge.ruleset.maxDailyDrawdownPercent && (
                      <div className="flex items-center gap-2">
                        <Percent className="h-4 w-4 text-yellow-400" />
                        <div>
                          <p className="text-xs text-gray-400">Max Daily Drawdown</p>
                          <p className="text-sm font-medium">
                            {challenge.ruleset.maxDailyDrawdownPercent}%
                          </p>
                        </div>
                      </div>
                    )}
                  </>
                )}
              </CardContent>
            </Card>
          </div>
        </ScrollArea>

        <DialogFooter className="flex justify-end">
          <Button variant="outline" onClick={() => handleOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ChallengeDetailDialog;
