import { useState, useEffect } from 'react';
import { TradeData, TradePattern } from '../context/AnalyticsContext';

/**
 * Hook for detecting trading patterns from trade data
 * 
 * @param trades - Array of trade data
 * @returns Detected trading patterns
 */
export const usePatternRecognition = (trades: TradeData[]) => {
  const [patterns, setPatterns] = useState<TradePattern[]>([]);

  useEffect(() => {
    if (trades.length < 5) {
      setPatterns([]);
      return;
    }

    // Sort trades by time
    const sortedTrades = [...trades].sort((a, b) => 
      new Date(a.entryTime).getTime() - new Date(b.entryTime).getTime()
    );
    
    const detectedPatterns: TradePattern[] = [];
    
    // Pattern 1: Consecutive wins (3 or more)
    let winStreak = 0;
    let winStreakTrades: number[] = [];
    
    sortedTrades.forEach((trade, index) => {
      if (trade.pnl > 0) {
        winStreak++;
        winStreakTrades.push(trade.id);
        
        if (winStreak >= 3 && (index === sortedTrades.length - 1 || sortedTrades[index + 1].pnl <= 0)) {
          // End of a win streak
          detectedPatterns.push({
            name: `Win Streak (${winStreak})`,
            description: `A series of ${winStreak} consecutive winning trades`,
            occurrences: 1,
            winRate: 100,
            averagePnl: winStreakTrades.reduce((sum, id) => {
              const trade = trades.find(t => t.id === id);
              return sum + (trade?.pnl || 0);
            }, 0) / winStreak,
            trades: [...winStreakTrades]
          });
          
          winStreak = 0;
          winStreakTrades = [];
        }
      } else {
        winStreak = 0;
        winStreakTrades = [];
      }
    });
    
    // Pattern 2: Consecutive losses (3 or more)
    let lossStreak = 0;
    let lossStreakTrades: number[] = [];
    
    sortedTrades.forEach((trade, index) => {
      if (trade.pnl <= 0) {
        lossStreak++;
        lossStreakTrades.push(trade.id);
        
        if (lossStreak >= 3 && (index === sortedTrades.length - 1 || sortedTrades[index + 1].pnl > 0)) {
          // End of a loss streak
          detectedPatterns.push({
            name: `Loss Streak (${lossStreak})`,
            description: `A series of ${lossStreak} consecutive losing trades`,
            occurrences: 1,
            winRate: 0,
            averagePnl: lossStreakTrades.reduce((sum, id) => {
              const trade = trades.find(t => t.id === id);
              return sum + (trade?.pnl || 0);
            }, 0) / lossStreak,
            trades: [...lossStreakTrades]
          });
          
          lossStreak = 0;
          lossStreakTrades = [];
        }
      } else {
        lossStreak = 0;
        lossStreakTrades = [];
      }
    });
    
    // Pattern 3: Same symbol consecutive trades
    const symbolGroups: Record<string, number[]> = {};
    
    sortedTrades.forEach(trade => {
      if (!symbolGroups[trade.symbol]) {
        symbolGroups[trade.symbol] = [];
      }
      symbolGroups[trade.symbol].push(trade.id);
    });
    
    Object.entries(symbolGroups).forEach(([symbol, tradeIds]) => {
      if (tradeIds.length >= 3) {
        const patternTrades = tradeIds.map(id => trades.find(t => t.id === id)!);
        const winningTrades = patternTrades.filter(t => t.pnl > 0).length;
        
        detectedPatterns.push({
          name: `${symbol} Focus`,
          description: `A series of ${tradeIds.length} trades on ${symbol}`,
          occurrences: tradeIds.length,
          winRate: (winningTrades / tradeIds.length) * 100,
          averagePnl: patternTrades.reduce((sum, t) => sum + t.pnl, 0) / tradeIds.length,
          trades: tradeIds
        });
      }
    });
    
    // Pattern 4: Time-based patterns (trading at specific hours)
    const hourGroups: Record<string, number[]> = {};
    
    sortedTrades.forEach(trade => {
      const hour = new Date(trade.entryTime).getHours();
      const hourKey = `${hour}:00`;
      
      if (!hourGroups[hourKey]) {
        hourGroups[hourKey] = [];
      }
      hourGroups[hourKey].push(trade.id);
    });
    
    Object.entries(hourGroups).forEach(([hour, tradeIds]) => {
      if (tradeIds.length >= 3) {
        const patternTrades = tradeIds.map(id => trades.find(t => t.id === id)!);
        const winningTrades = patternTrades.filter(t => t.pnl > 0).length;
        
        detectedPatterns.push({
          name: `${hour} Trading`,
          description: `Regular trading at ${hour}`,
          occurrences: tradeIds.length,
          winRate: (winningTrades / tradeIds.length) * 100,
          averagePnl: patternTrades.reduce((sum, t) => sum + t.pnl, 0) / tradeIds.length,
          trades: tradeIds
        });
      }
    });
    
    // Pattern 5: Direction patterns (buy/sell preferences)
    const buyTrades = sortedTrades.filter(trade => trade.direction === 'BUY');
    const sellTrades = sortedTrades.filter(trade => trade.direction === 'SELL');
    
    if (buyTrades.length >= 3 && buyTrades.length > sellTrades.length * 2) {
      const winningTrades = buyTrades.filter(t => t.pnl > 0).length;
      
      detectedPatterns.push({
        name: 'Buy Preference',
        description: `Strong preference for buy trades (${buyTrades.length} buys vs ${sellTrades.length} sells)`,
        occurrences: buyTrades.length,
        winRate: (winningTrades / buyTrades.length) * 100,
        averagePnl: buyTrades.reduce((sum, t) => sum + t.pnl, 0) / buyTrades.length,
        trades: buyTrades.map(t => t.id)
      });
    } else if (sellTrades.length >= 3 && sellTrades.length > buyTrades.length * 2) {
      const winningTrades = sellTrades.filter(t => t.pnl > 0).length;
      
      detectedPatterns.push({
        name: 'Sell Preference',
        description: `Strong preference for sell trades (${sellTrades.length} sells vs ${buyTrades.length} buys)`,
        occurrences: sellTrades.length,
        winRate: (winningTrades / sellTrades.length) * 100,
        averagePnl: sellTrades.reduce((sum, t) => sum + t.pnl, 0) / sellTrades.length,
        trades: sellTrades.map(t => t.id)
      });
    }
    
    setPatterns(detectedPatterns);
  }, [trades]);

  return patterns;
};
