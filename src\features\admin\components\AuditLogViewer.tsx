/**
 * Audit Log Viewer Component
 * @description Component for viewing audit logs in the admin panel
 * @version 1.0.0
 * @status stable
 */

import React, { useState, useEffect } from 'react';
import {
  Search,
  Filter,
  RefreshCw,
  Calendar,
  Clock,
  User,
  FileText,
  Tag,
  ChevronLeft,
  ChevronRight,
  ArrowUpDown,
  Info
} from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/common/components/ui/card';
import { Button } from '@/common/components/ui/button';
import { Input } from '@/common/components/ui/input';
import { Badge } from '@/common/components/ui/badge';
import { Skeleton } from '@/common/components/ui/skeleton';
import { useToast } from '@/common/components/ui/use-toast';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/common/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/common/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/common/components/ui/dialog';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/common/components/ui/tooltip';
import { apiService } from '@/common/services/api';
import { format } from 'date-fns';

// Audit log type definition
interface AuditLog {
  id: number;
  userId: string;
  action: string;
  entityType: string;
  entityId: string | null;
  details: any;
  ipAddress: string | null;
  userAgent: string | null;
  timestamp: string;
  user: {
    id: string;
    username: string;
    email: string;
  };
}

// Pagination type definition
interface Pagination {
  page: number;
  limit: number;
  totalCount: number;
  totalPages: number;
}

/**
 * Audit log viewer component
 */
const AuditLogViewer: React.FC = () => {
  const { toast } = useToast();
  const [loading, setLoading] = useState<boolean>(true);
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    limit: 50,
    totalCount: 0,
    totalPages: 0
  });
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [actionFilter, setActionFilter] = useState<string>('all');
  const [entityTypeFilter, setEntityTypeFilter] = useState<string>('all');
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');
  const [selectedLog, setSelectedLog] = useState<AuditLog | null>(null);
  const [detailsOpen, setDetailsOpen] = useState<boolean>(false);

  // Fetch audit logs
  const fetchAuditLogs = async () => {
    try {
      setLoading(true);

      // Build query parameters
      const params: Record<string, string> = {
        page: pagination.page.toString(),
        limit: pagination.limit.toString()
      };

      if (searchTerm) {
        params.userId = searchTerm;
      }

      if (actionFilter !== 'all') {
        params.action = actionFilter;
      }

      if (entityTypeFilter !== 'all') {
        params.entityType = entityTypeFilter;
      }

      if (startDate) {
        params.startDate = startDate;
      }

      if (endDate) {
        params.endDate = endDate;
      }

      // Fetch audit logs
      const response = await apiService.getAuditLogs(params);

      setAuditLogs(response.auditLogs);
      setPagination(response.pagination);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching audit logs:', error);
      toast({
        title: 'Error',
        description: 'Failed to load audit logs',
        variant: 'destructive',
      });
      setLoading(false);
    }
  };

  // Fetch audit logs on component mount and when filters change
  useEffect(() => {
    fetchAuditLogs();
  }, [pagination.page, actionFilter, entityTypeFilter, startDate, endDate]);

  // Handle search
  const handleSearch = () => {
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchAuditLogs();
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  // Handle view details
  const handleViewDetails = (log: AuditLog) => {
    setSelectedLog(log);
    setDetailsOpen(true);
  };

  // Format action for display
  const formatAction = (action: string) => {
    return action
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Get badge color for action
  const getActionBadgeColor = (action: string) => {
    if (action.includes('create')) return 'bg-green-500/20 text-green-500';
    if (action.includes('update')) return 'bg-blue-500/20 text-blue-500';
    if (action.includes('delete')) return 'bg-red-500/20 text-red-500';
    if (action.includes('add')) return 'bg-purple-500/20 text-purple-500';
    if (action.includes('deduct')) return 'bg-amber-500/20 text-amber-500';
    return 'bg-gray-500/20 text-gray-500';
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Audit Logs</h1>
        <Button
          variant="outline"
          size="sm"
          className="flex items-center gap-2"
          onClick={() => fetchAuditLogs()}
        >
          <RefreshCw className="h-4 w-4" />
          Refresh
        </Button>
      </div>

      {/* Filters */}
      <Card className="bg-forex-darker border-forex-border">
        <CardHeader className="pb-3">
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-forex-muted" />
                <Input
                  placeholder="Search by user ID..."
                  className="pl-9"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
            </div>
            <div>
              <Select
                value={actionFilter}
                onValueChange={setActionFilter}
              >
                <SelectTrigger>
                  <div className="flex items-center gap-2">
                    <Tag className="h-4 w-4" />
                    <SelectValue placeholder="Filter by action" />
                  </div>
                </SelectTrigger>
                <SelectContent className="bg-forex-darker border-forex-border">
                  <SelectItem value="all">All Actions</SelectItem>
                  <SelectItem value="create_challenge">Create Challenge</SelectItem>
                  <SelectItem value="update_challenge">Update Challenge</SelectItem>
                  <SelectItem value="delete_challenge">Delete Challenge</SelectItem>
                  <SelectItem value="add_credits">Add Credits</SelectItem>
                  <SelectItem value="deduct_credits">Deduct Credits</SelectItem>
                  <SelectItem value="expire_credits">Expire Credits</SelectItem>
                  <SelectItem value="disqualify_challenge_entry">Disqualify Entry</SelectItem>
                  <SelectItem value="create_notification">Create Notification</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Select
                value={entityTypeFilter}
                onValueChange={setEntityTypeFilter}
              >
                <SelectTrigger>
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    <SelectValue placeholder="Filter by entity type" />
                  </div>
                </SelectTrigger>
                <SelectContent className="bg-forex-darker border-forex-border">
                  <SelectItem value="all">All Entity Types</SelectItem>
                  <SelectItem value="challenge">Challenge</SelectItem>
                  <SelectItem value="wallet">Wallet</SelectItem>
                  <SelectItem value="challenge_entry">Challenge Entry</SelectItem>
                  <SelectItem value="notification">Notification</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex gap-2">
              <Input
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className="w-1/2"
              />
              <Input
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className="w-1/2"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Audit Logs Table */}
      <Card className="bg-forex-darker border-forex-border">
        <CardHeader className="pb-3">
          <CardTitle>Audit Logs</CardTitle>
          <CardDescription>
            {pagination.totalCount} {pagination.totalCount === 1 ? 'entry' : 'entries'} found
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, index) => (
                <div key={index} className="flex items-center space-x-4">
                  <Skeleton className="h-12 w-12 rounded-full" />
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-[250px]" />
                    <Skeleton className="h-4 w-[200px]" />
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="rounded-md border border-forex-border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Timestamp</TableHead>
                    <TableHead>User</TableHead>
                    <TableHead>Action</TableHead>
                    <TableHead>Entity Type</TableHead>
                    <TableHead>Entity ID</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {auditLogs.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8 text-forex-muted">
                        No audit logs found
                      </TableCell>
                    </TableRow>
                  ) : (
                    auditLogs.map((log) => (
                      <TableRow key={log.id}>
                        <TableCell>
                          <div className="flex flex-col">
                            <div className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              <span>{format(new Date(log.timestamp), 'yyyy-MM-dd')}</span>
                            </div>
                            <div className="flex items-center gap-1 text-xs text-forex-muted">
                              <Clock className="h-3 w-3" />
                              <span>{format(new Date(log.timestamp), 'HH:mm:ss')}</span>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <User className="h-4 w-4" />
                            <span>{log.user.username}</span>
                          </div>
                          <div className="text-xs text-forex-muted">{log.user.email}</div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getActionBadgeColor(log.action)}>
                            {formatAction(log.action)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {log.entityType.charAt(0).toUpperCase() + log.entityType.slice(1)}
                        </TableCell>
                        <TableCell>
                          {log.entityId || '-'}
                        </TableCell>
                        <TableCell className="text-right">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleViewDetails(log)}
                                >
                                  <Info className="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>View Details</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-forex-muted">
            Showing {auditLogs.length} of {pagination.totalCount} entries
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page - 1)}
              disabled={pagination.page === 1 || loading}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <span className="text-sm">
              Page {pagination.page} of {pagination.totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page + 1)}
              disabled={pagination.page === pagination.totalPages || loading}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </CardFooter>
      </Card>

      {/* Details Dialog */}
      <Dialog open={detailsOpen} onOpenChange={setDetailsOpen}>
        <DialogContent className="bg-forex-darker border-forex-border">
          <DialogHeader>
            <DialogTitle>Audit Log Details</DialogTitle>
            <DialogDescription>
              {selectedLog && `ID: ${selectedLog.id}`}
            </DialogDescription>
          </DialogHeader>
          {selectedLog && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-forex-muted">Timestamp</h3>
                  <p>{format(new Date(selectedLog.timestamp), 'yyyy-MM-dd HH:mm:ss')}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-forex-muted">User</h3>
                  <p>{selectedLog.user.username} ({selectedLog.user.email})</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-forex-muted">Action</h3>
                  <p>{formatAction(selectedLog.action)}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-forex-muted">Entity</h3>
                  <p>{selectedLog.entityType} {selectedLog.entityId ? `(ID: ${selectedLog.entityId})` : ''}</p>
                </div>
                <div className="col-span-2">
                  <h3 className="text-sm font-medium text-forex-muted">IP Address</h3>
                  <p>{selectedLog.ipAddress || 'N/A'}</p>
                </div>
                <div className="col-span-2">
                  <h3 className="text-sm font-medium text-forex-muted">User Agent</h3>
                  <p className="text-sm">{selectedLog.userAgent || 'N/A'}</p>
                </div>
                {selectedLog.details && (
                  <div className="col-span-2">
                    <h3 className="text-sm font-medium text-forex-muted">Details</h3>
                    <pre className="bg-forex-dark p-3 rounded-md text-sm mt-2 overflow-auto max-h-40">
                      {JSON.stringify(selectedLog.details, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            </div>
          )}
          <DialogFooter>
            <Button onClick={() => setDetailsOpen(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AuditLogViewer;
