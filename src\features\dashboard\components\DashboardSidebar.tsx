import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link, useLocation } from 'react-router-dom';
import {
  LayoutDashboard,
  LineChart,
  Trophy,
  Wallet,
  BookOpen,
  Users,
  Settings,
  HelpCircle,
  ChevronLeft,
  ChevronRight,
  ArrowDownUp,
  Sparkles
} from 'lucide-react';
import {
  Button,
  Badge,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
  TouchArea
} from '@/common/components/ui';
import { useResponsive } from '@/common/context';

// Navigation item interface
interface NavItem {
  name: string;
  path: string;
  icon: React.ElementType;
  badge?: string | number;
  badgeColor?: string;
  tooltip?: string;
  isNew?: boolean;
}

interface DashboardSidebarProps {
  onCollapse?: (collapsed: boolean) => void;
}

/**
 * Enhanced dashboard sidebar component
 *
 * Provides navigation for the dashboard with advanced animations,
 * visual effects, and interactive elements.
 *
 * Features:
 * - Animated transitions for navigation items
 * - Hover effects with particle animations
 * - Active state indicators with animated underlines
 * - Responsive design with collapsible sidebar
 * - Badge indicators with customizable colors
 * - Tooltips for additional information
 *
 * @component
 * @example
 * <DashboardSidebar onCollapse={(collapsed) => console.log(collapsed)} />
 */
const DashboardSidebar: React.FC<DashboardSidebarProps> = ({ onCollapse }) => {
  const location = useLocation();
  const { isMobile, isTouch, screenSize } = useResponsive();
  const [collapsed, setCollapsed] = useState(screenSize === 'md');
  const [activeItemIndex, setActiveItemIndex] = useState<number | null>(null);

  // Update collapsed state when screen size changes
  useEffect(() => {
    // Auto-collapse on medium screens, keep expanded on larger screens
    const shouldCollapse = screenSize === 'md';
    setCollapsed(shouldCollapse);
    if (onCollapse) {
      onCollapse(shouldCollapse);
    }
  }, [screenSize, onCollapse]);

  // Enhanced navigation items with tooltips
  const navItems: NavItem[] = [
    {
      name: 'Dashboard',
      path: '/dashboard',
      icon: LayoutDashboard,
      tooltip: 'View your trading dashboard'
    },
    {
      name: 'Analytics',
      path: '/dashboard/analytics',
      icon: LineChart,
      tooltip: 'Advanced trading metrics and analysis'
    },
    {
      name: 'Challenges',
      path: '/dashboard/challenges',
      icon: Trophy,
      tooltip: 'View and manage your trading challenges'
    },
    {
      name: 'Transactions',
      path: '/dashboard/transactions',
      icon: ArrowDownUp,
      tooltip: 'View your transaction history'
    },
    {
      name: 'Journal',
      path: '/dashboard/journal',
      icon: BookOpen,
      tooltip: 'Record and review your trading journal'
    },
    {
      name: 'Leaderboard',
      path: '/dashboard/leaderboard',
      icon: Users,
      tooltip: 'See how you rank against other traders'
    },
    {
      name: 'Settings',
      path: '/dashboard/settings',
      icon: Settings,
      tooltip: 'Configure your account settings'
    },
    {
      name: 'Help',
      path: '/dashboard/help',
      icon: HelpCircle,
      tooltip: 'Get help and support'
    },
  ];

  // Find active item index on mount and route change
  useEffect(() => {
    const index = navItems.findIndex(item => item.path === location.pathname);
    setActiveItemIndex(index >= 0 ? index : null);
  }, [location.pathname]);

  // Enhanced animation variants
  const sidebarVariants = {
    expanded: { width: '240px' },
    collapsed: { width: '70px' }
  };

  const itemVariants = {
    expanded: { opacity: 1, x: 0 },
    collapsed: { opacity: 0, x: -10 }
  };

  // Staggered animation for nav items
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05,
        delayChildren: 0.1
      }
    }
  };

  const itemAnimationVariants = {
    hidden: {
      opacity: 0,
      y: 10,
      scale: 0.95
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 350,
        damping: 25
      }
    }
  };

  // Generate random particles for hover effect
  const generateParticles = (count: number) => {
    return Array.from({ length: count }).map((_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      size: Math.random() * 2 + 1,
      duration: Math.random() * 1.5 + 0.5,
      delay: Math.random() * 0.3,
    }));
  };

  const particles = generateParticles(8);

  return (
    <motion.aside
      className={`
        bg-gradient-to-b from-[#0f1c2e]/90 to-[#0a1a2f]/90 backdrop-blur-md
        border-r border-blue-500/10 h-[calc(100vh-4rem)] fixed top-16 left-0
        z-20 shadow-lg overflow-hidden transition-all duration-300
        ${screenSize === 'xs' || screenSize === 'sm' ? 'hidden' : 'block'}
      `}
      initial="expanded"
      animate={collapsed ? 'collapsed' : 'expanded'}
      variants={sidebarVariants}
      transition={{ duration: 0.3, ease: "easeInOut" }}
    >
      {/* Animated background elements */}
      <div className="absolute inset-0 bg-[url('/bg-pattern.svg')] opacity-5"></div>
      <div className="absolute top-0 right-0 w-1/2 h-1/2 bg-blue-500/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-indigo-500/5 rounded-full blur-3xl"></div>

      <div className="flex flex-col h-full relative z-10">
        {/* Logo or branding element with collapse button */}
        <div className={`p-4 border-b border-blue-500/10 flex items-center ${collapsed ? 'justify-center' : 'justify-between'}`}>
          {!collapsed && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="text-blue-100 font-bold flex items-center"
            >
              <Sparkles className="w-5 h-5 text-blue-400 mr-2" />
              <motion.span
                initial="collapsed"
                animate="expanded"
                exit="collapsed"
                variants={itemVariants}
                className="bg-gradient-to-r from-blue-400 to-indigo-500 bg-clip-text text-transparent"
              >
                TradeChampionX
              </motion.span>
            </motion.div>
          )}

          {/* Collapse button */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <motion.div
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <Button
                    variant="ghost"
                    size="icon"
                    className={`${collapsed ? 'h-8 w-8' : 'h-7 w-7'} rounded-full text-blue-300 hover:text-blue-100 hover:bg-blue-500/20 border border-blue-500/30`}
                    onClick={() => {
                      const newCollapsedState = !collapsed;
                      setCollapsed(newCollapsedState);
                      if (onCollapse) {
                        onCollapse(newCollapsedState);
                      }
                    }}
                  >
                    <motion.div
                      animate={{ rotate: collapsed ? 0 : 180 }}
                      transition={{ duration: 0.3 }}
                    >
                      {collapsed ? <ChevronRight size={collapsed ? 18 : 16} /> : <ChevronLeft size={collapsed ? 18 : 16} />}
                    </motion.div>
                  </Button>
                </motion.div>
              </TooltipTrigger>
              <TooltipContent side="right" className="bg-[#0f1c2e] border border-blue-500/20">
                <p>{collapsed ? "Expand sidebar" : "Collapse sidebar"}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        {/* Navigation items with enhanced animations */}
        <nav className={`flex-1 py-6 ${collapsed ? 'px-2' : 'px-3'} overflow-y-auto scrollbar-hide`}>
          <motion.ul
            className="space-y-2"
            initial="hidden"
            animate="visible"
            variants={containerVariants}
          >
            {navItems.map((item, index) => {
              const isActive = location.pathname === item.path;

              return (
                <motion.li
                  key={item.path}
                  variants={itemAnimationVariants}
                  whileHover={{ x: collapsed ? 0 : 5 }}
                  className="relative"
                >
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Link to={item.path}>
                          <TouchArea
                            scale={isTouch}
                            press={isTouch}
                            hover={!isTouch}
                            subtle={isActive}
                          >
                            <Button
                              variant={isActive ? 'default' : 'ghost'}
                              className={`w-full ${collapsed ? 'justify-center p-2' : 'justify-start'} ${
                                isActive
                                  ? 'bg-blue-500/20 hover:bg-blue-500/30 text-blue-100'
                                  : 'hover:bg-blue-500/10 text-blue-100/70 hover:text-blue-100'
                              } transition-all duration-200 group relative overflow-hidden rounded-lg ${isTouch ? 'h-12' : ''}`}
                            >
                            {/* Enhanced background animation on hover */}
                            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/0 via-blue-500/10 to-blue-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 bg-[length:200%_100%] group-hover:animate-shimmer"></div>

                            {/* Animated particles on hover - only visible when not collapsed */}
                            {!collapsed && (
                              <AnimatePresence>
                                {particles.map(particle => (
                                  <motion.div
                                    key={`particle-${item.name}-${particle.id}`}
                                    className="absolute rounded-full bg-blue-400/30 pointer-events-none hidden group-hover:block"
                                    initial={{ opacity: 0 }}
                                    animate={{
                                      opacity: [0, 0.6, 0],
                                      scale: [0, 1, 0.5],
                                      x: [0, particle.x - 50],
                                      y: [0, particle.y - 50],
                                    }}
                                    transition={{
                                      duration: particle.duration,
                                      delay: particle.delay,
                                      repeat: Infinity,
                                      repeatDelay: Math.random() * 2
                                    }}
                                    style={{
                                      left: '50%',
                                      top: '50%',
                                      width: `${particle.size}px`,
                                      height: `${particle.size}px`,
                                    }}
                                  />
                                ))}
                              </AnimatePresence>
                            )}

                            {/* Animated icon */}
                            <motion.div
                              whileHover={{ scale: 1.2, rotate: isActive ? 0 : 10 }}
                              transition={{ type: "spring", stiffness: 400, damping: 10 }}
                              className={`${isActive ? 'text-blue-400' : 'text-blue-300'} ${collapsed ? '' : 'mr-3'} flex-shrink-0`}
                            >
                              <item.icon size={collapsed ? 22 : 20} />
                            </motion.div>

                            {/* Text and badge */}
                            <AnimatePresence>
                              {!collapsed && (
                                <motion.div
                                  className="flex items-center justify-between w-full"
                                  initial="collapsed"
                                  animate="expanded"
                                  exit="collapsed"
                                  variants={itemVariants}
                                >
                                  <span className="relative">
                                    {item.name}
                                  </span>
                                </motion.div>
                              )}
                            </AnimatePresence>

                            {/* Active indicator - animated underline or glow */}
                            {isActive && (
                              <>
                                {collapsed ? (
                                  <motion.div
                                    className="absolute inset-0 bg-blue-500/10 rounded-lg border border-blue-500/30 shadow-[0_0_10px_rgba(59,130,246,0.3)]"
                                    layoutId="activeNavIndicatorCollapsed"
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    transition={{ duration: 0.3 }}
                                  />
                                ) : (
                                  <motion.div
                                    className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-400 to-indigo-500"
                                    layoutId="activeNavIndicator"
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    transition={{ duration: 0.3 }}
                                  />
                                )}
                              </>
                            )}
                          </Button>
                          </TouchArea>
                        </Link>
                      </TooltipTrigger>
                      {collapsed && (
                        <TooltipContent side="right" className="bg-[#0f1c2e] border border-blue-500/20">
                          <div className="flex flex-col">
                            <span>{item.name}</span>
                            {item.tooltip && <span className="text-xs text-gray-400">{item.tooltip}</span>}
                          </div>
                        </TooltipContent>
                      )}
                    </Tooltip>
                  </TooltipProvider>
                </motion.li>
              );
            })}
          </motion.ul>
        </nav>

        {/* Footer with version info */}
        <div className="p-3 border-t border-blue-500/10 flex justify-center">
          {!collapsed && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="text-xs text-blue-300/40"
            >
              v5.0.0
            </motion.div>
          )}
        </div>
      </div>
    </motion.aside>
  );
};

export default DashboardSidebar;
