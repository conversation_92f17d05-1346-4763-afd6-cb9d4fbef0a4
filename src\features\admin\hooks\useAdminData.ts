/**
 * Admin Data Hook
 * @description Custom hook for fetching and managing admin data
 * @version 1.0.0
 * @status stable
 */

import { useState, useEffect, useCallback } from 'react';
import { useToast } from '@/common/components/ui/use-toast';
import { adminApiService } from '../services/adminApiService';

/**
 * Hook for fetching and managing users data
 */
export const useUsers = (initialParams = {}) => {
  const { toast } = useToast();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [params, setParams] = useState(initialParams);

  const fetchUsers = useCallback(async (queryParams) => {
    try {
      setLoading(true);
      setError(null);

      // Use provided params or current params
      const actualParams = queryParams || params;
      const response = await adminApiService.user.getAllUsers(actualParams);
      setUsers(response?.data?.users || []);

      setLoading(false);
    } catch (err) {
      console.error('Error fetching users:', err);
      setError(err);
      setLoading(false);

      toast({
        title: 'Error',
        description: 'Failed to load users',
        variant: 'destructive',
      });
    }
  }, [toast]); // Remove params dependency to prevent infinite loops

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  const updateUserStatus = async (userId, status) => {
    try {
      await adminApiService.user.updateUserStatus(userId, status);

      // Update local state
      setUsers(users.map(user =>
        user.id === userId ? { ...user, status } : user
      ));

      toast({
        title: 'Success',
        description: `User ${status === 'active' ? 'activated' : 'banned'} successfully`,
      });

      return true;
    } catch (err) {
      console.error(`Error ${status === 'active' ? 'activating' : 'banning'} user:`, err);

      toast({
        title: 'Error',
        description: `Failed to ${status === 'active' ? 'activate' : 'ban'} user`,
        variant: 'destructive',
      });

      return false;
    }
  };

  const adjustUserCredits = async (userId, amount, reason, operation) => {
    try {
      let response;

      if (operation === 'add') {
        response = await adminApiService.user.addCreditsToUser(userId, amount, reason);
      } else {
        response = await adminApiService.user.deductCreditsFromUser(userId, amount, reason);
      }

      // Update local state
      setUsers(users.map(user =>
        user.id === userId ? { ...user, walletCredit: response.data.walletCredit } : user
      ));

      toast({
        title: 'Success',
        description: `Successfully ${operation === 'add' ? 'added' : 'deducted'} $${amount} ${operation === 'add' ? 'to' : 'from'} user's wallet`,
      });

      return true;
    } catch (err) {
      console.error(`Error ${operation === 'add' ? 'adding' : 'deducting'} credits:`, err);

      toast({
        title: 'Error',
        description: `Failed to ${operation === 'add' ? 'add' : 'deduct'} credits`,
        variant: 'destructive',
      });

      return false;
    }
  };

  return {
    users,
    loading,
    error,
    fetchUsers,
    updateUserStatus,
    adjustUserCredits,
    setParams,
  };
};

/**
 * Hook for fetching and managing challenges data
 */
export const useChallenges = (initialParams = {}) => {
  const { toast } = useToast();
  const [challenges, setChallenges] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [params, setParams] = useState(initialParams);

  const fetchChallenges = useCallback(async (queryParams) => {
    try {
      setLoading(true);
      setError(null);

      // Use provided params or current params
      const actualParams = queryParams || params;
      const response = await adminApiService.challenge.getAllChallenges(actualParams);

      // Handle different response formats - the backend returns the challenges directly
      const challengeData = Array.isArray(response) ? response :
                           (response.data && Array.isArray(response.data)) ? response.data :
                           (response.data && response.data.challenges) ? response.data.challenges : [];

      // If we got an empty array and it wasn't expected, show a message but don't treat as error
      if (challengeData.length === 0) {
        console.log('No challenges found or could not retrieve challenges');
      } else {
        console.log(`Retrieved ${challengeData.length} challenges successfully`);
      }

      setChallenges(challengeData);
      setLoading(false);
    } catch (err) {
      console.error('Error fetching challenges:', err);
      setError(err);
      setLoading(false);

      // Error is already handled by the withErrorHandling wrapper in adminApiService
      // No need to show another toast here
    }
  }, []); // Remove params dependency to prevent infinite loops

  useEffect(() => {
    fetchChallenges();
  }, [fetchChallenges]);

  const createChallenge = async (challengeData) => {
    try {
      const response = await adminApiService.challenge.createChallenge(challengeData);

      // Check if the response indicates success
      if (response.success === false) {
        console.warn('Challenge creation may have failed:', response);
        return null;
      }

      // Get the challenge data from the response
      const newChallenge = response.data || response;

      // Update local state if we have valid data
      if (newChallenge && (newChallenge.id || newChallenge.name)) {
        setChallenges([...challenges, newChallenge]);
        console.log('Challenge added to local state:', newChallenge);
      } else {
        console.warn('Received challenge data is incomplete:', newChallenge);
      }

      return newChallenge;
    } catch (err) {
      console.error('Error creating challenge:', err);
      // Error is already handled by the withErrorHandling wrapper in adminApiService
      return null;
    }
  };

  const updateChallenge = async (challengeId, challengeData) => {
    try {
      const response = await adminApiService.challenge.updateChallenge(challengeId, challengeData);

      // Check if the response indicates success
      if (response.success === false) {
        console.warn('Challenge update may have failed:', response);
        return null;
      }

      // Get the updated challenge data
      const updatedChallenge = response.data || response;

      // Update local state if we have valid data
      if (updatedChallenge) {
        setChallenges(challenges.map(challenge =>
          challenge.id === challengeId ? updatedChallenge : challenge
        ));
        console.log('Challenge updated in local state:', updatedChallenge);
      } else {
        console.warn('Received updated challenge data is incomplete:', updatedChallenge);
      }

      return updatedChallenge;
    } catch (err) {
      console.error('Error updating challenge:', err);
      // Error is already handled by the withErrorHandling wrapper in adminApiService
      return null;
    }
  };

  const deleteChallenge = async (challengeId) => {
    try {
      console.log('Attempting to delete challenge with ID:', challengeId);

      // Log the URL that will be used
      console.log('Delete URL:', `/challenges/${challengeId}`);

      const response = await adminApiService.challenge.deleteChallenge(challengeId);

      console.log('Delete challenge response:', response);

      // Check if the response indicates success
      if (response.success === false) {
        console.warn('Challenge deletion may have failed:', response);
        return false;
      }

      // Update local state
      setChallenges(challenges.filter(challenge => challenge.id !== challengeId));
      console.log('Challenge removed from local state:', challengeId);

      return true;
    } catch (err) {
      console.error('Error deleting challenge:', err);
      console.error('Error details:', JSON.stringify(err, null, 2));
      // Error is already handled by the withErrorHandling wrapper in adminApiService
      return false;
    }
  };

  return {
    challenges,
    loading,
    error,
    fetchChallenges,
    createChallenge,
    updateChallenge,
    deleteChallenge,
    setParams,
  };
};

/**
 * Hook for fetching and managing transactions data
 */
export const useTransactions = (initialParams = {}) => {
  const { toast } = useToast();
  const [transactions, setTransactions] = useState([]);
  const [transactionStats, setTransactionStats] = useState({
    totalCredits: 0,
    totalDebits: 0,
    creditCount: 0,
    debitCount: 0,
    recentTransactions: 0,
    netBalance: 0
  });
  const [loading, setLoading] = useState(true);
  const [statsLoading, setStatsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    totalCount: 0,
    totalPages: 1
  });
  const [params, setParams] = useState(initialParams);

  const fetchTransactions = useCallback(async (queryParams) => {
    try {
      setLoading(true);
      setError(null);

      // Use provided params or current params
      const actualParams = queryParams || params;
      const response = await adminApiService.transaction.getAllTransactions(actualParams);

      // Handle different response formats
      const transactionData = response?.data?.transactions || response?.transactions || [];
      setTransactions(transactionData);

      if (response?.data?.pagination || response?.pagination) {
        setPagination(response.data?.pagination || response.pagination);
      }

      setLoading(false);
    } catch (err) {
      console.error('Error fetching transactions:', err);
      setError(err);
      setLoading(false);

      toast({
        title: 'Error',
        description: 'Failed to load transactions',
        variant: 'destructive',
      });
    }
  }, [toast]); // Remove params dependency to prevent infinite loops

  const fetchTransactionStats = useCallback(async () => {
    try {
      setStatsLoading(true);

      const response = await adminApiService.transaction.getTransactionStats();

      // Handle different response formats
      const statsData = response?.data || response || {
        totalCredits: 0,
        totalDebits: 0,
        creditCount: 0,
        debitCount: 0,
        recentTransactions: 0,
        netBalance: 0
      };

      setTransactionStats(statsData);
      setStatsLoading(false);
    } catch (err) {
      console.error('Error fetching transaction stats:', err);
      setStatsLoading(false);

      // Set default stats on error
      setTransactionStats({
        totalCredits: 0,
        totalDebits: 0,
        creditCount: 0,
        debitCount: 0,
        recentTransactions: 0,
        netBalance: 0
      });

      toast({
        title: 'Error',
        description: 'Failed to load transaction statistics',
        variant: 'destructive',
      });
    }
  }, [toast]);

  useEffect(() => {
    fetchTransactions();
    fetchTransactionStats();
  }, [fetchTransactions, fetchTransactionStats]);

  const changePage = useCallback((newPage) => {
    setParams(prev => ({
      ...prev,
      page: newPage
    }));
  }, []);

  return {
    transactions,
    transactionStats,
    loading,
    statsLoading,
    error,
    pagination,
    fetchTransactions,
    fetchTransactionStats,
    setParams,
    changePage
  };
};

/**
 * Hook for fetching and managing prize distributions data
 */
export const usePrizeDistributions = (initialParams = {}) => {
  const { toast } = useToast();
  const [prizeDistributions, setPrizeDistributions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [params, setParams] = useState(initialParams);

  const fetchPrizeDistributions = useCallback(async (queryParams) => {
    try {
      setLoading(true);
      setError(null);

      // Use provided params or current params
      const actualParams = queryParams || params;
      const response = await adminApiService.prizeDistribution.getPrizeDistributions(actualParams);
      setPrizeDistributions(response.data || []);

      setLoading(false);
    } catch (err) {
      console.error('Error fetching prize distributions:', err);
      setError(err);
      setLoading(false);

      toast({
        title: 'Error',
        description: 'Failed to load prize distributions',
        variant: 'destructive',
      });
    }
  }, [toast]); // Remove params dependency to prevent infinite loops

  useEffect(() => {
    fetchPrizeDistributions();
  }, [fetchPrizeDistributions]);

  const approvePrizeDistribution = async (distributionId) => {
    try {
      const response = await adminApiService.prizeDistribution.approvePrizeDistribution(distributionId);

      // Update local state
      setPrizeDistributions(prizeDistributions.map(distribution =>
        distribution.id === distributionId ? response.data : distribution
      ));

      toast({
        title: 'Success',
        description: 'Prize distribution approved successfully',
      });

      return response.data;
    } catch (err) {
      console.error('Error approving prize distribution:', err);

      toast({
        title: 'Error',
        description: 'Failed to approve prize distribution',
        variant: 'destructive',
      });

      return null;
    }
  };

  const markPrizeDistributionAsPaid = async (distributionId, paymentTxHash, paymentAddress) => {
    try {
      const response = await adminApiService.prizeDistribution.markPrizeDistributionAsPaid(
        distributionId,
        paymentTxHash,
        paymentAddress
      );

      // Update local state
      setPrizeDistributions(prizeDistributions.map(distribution =>
        distribution.id === distributionId ? response.data : distribution
      ));

      toast({
        title: 'Success',
        description: 'Prize distribution marked as paid successfully',
      });

      return response.data;
    } catch (err) {
      console.error('Error marking prize distribution as paid:', err);

      toast({
        title: 'Error',
        description: 'Failed to mark prize distribution as paid',
        variant: 'destructive',
      });

      return null;
    }
  };

  return {
    prizeDistributions,
    loading,
    error,
    fetchPrizeDistributions,
    approvePrizeDistribution,
    markPrizeDistributionAsPaid,
    setParams,
  };
};

/**
 * Hook for fetching and managing notifications data
 */
export const useNotifications = (initialParams = {}) => {
  const { toast } = useToast();
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    totalCount: 0,
    totalPages: 1
  });
  const [params, setParams] = useState(initialParams);

  const fetchNotifications = useCallback(async (queryParams) => {
    try {
      setLoading(true);
      setError(null);

      // Use provided params or current params
      const actualParams = queryParams || params;
      const response = await adminApiService.notification.getAllNotifications(actualParams);
      setNotifications(response.data.notifications || []);

      if (response.data.pagination) {
        setPagination(response.data.pagination);
      }

      setLoading(false);
    } catch (err) {
      console.error('Error fetching notifications:', err);
      setError(err);
      setLoading(false);

      toast({
        title: 'Error',
        description: 'Failed to load notifications',
        variant: 'destructive',
      });
    }
  }, [toast]); // Remove params dependency to prevent infinite loops

  useEffect(() => {
    fetchNotifications();
  }, [fetchNotifications]);

  const createNotification = useCallback(async (notificationData) => {
    try {
      await adminApiService.notification.createNotification(notificationData);

      toast({
        title: 'Success',
        description: 'Notification created successfully',
      });

      fetchNotifications();
      return true;
    } catch (err) {
      console.error('Error creating notification:', err);

      toast({
        title: 'Error',
        description: 'Failed to create notification',
        variant: 'destructive',
      });

      return false;
    }
  }, [fetchNotifications, toast]);

  const createBatchNotification = useCallback(async (notificationData) => {
    try {
      await adminApiService.notification.createBatchNotification(notificationData);

      toast({
        title: 'Success',
        description: `Batch notification sent to ${notificationData.userIds.length} users`,
      });

      fetchNotifications();
      return true;
    } catch (err) {
      console.error('Error creating batch notification:', err);

      toast({
        title: 'Error',
        description: 'Failed to create batch notification',
        variant: 'destructive',
      });

      return false;
    }
  }, [fetchNotifications, toast]);

  const createSystemNotification = useCallback(async (notificationData) => {
    try {
      await adminApiService.notification.createSystemNotification(notificationData);

      toast({
        title: 'Success',
        description: 'System notification sent to all users',
      });

      fetchNotifications();
      return true;
    } catch (err) {
      console.error('Error creating system notification:', err);

      toast({
        title: 'Error',
        description: 'Failed to create system notification',
        variant: 'destructive',
      });

      return false;
    }
  }, [fetchNotifications, toast]);

  const deleteNotification = useCallback(async (id) => {
    try {
      await adminApiService.notification.deleteNotification(id);

      toast({
        title: 'Success',
        description: 'Notification deleted successfully',
      });

      fetchNotifications();
      return true;
    } catch (err) {
      console.error('Error deleting notification:', err);

      toast({
        title: 'Error',
        description: 'Failed to delete notification',
        variant: 'destructive',
      });

      return false;
    }
  }, [fetchNotifications, toast]);

  const changePage = useCallback((newPage) => {
    setParams(prev => ({
      ...prev,
      page: newPage
    }));
  }, []);

  return {
    notifications,
    loading,
    error,
    pagination,
    fetchNotifications,
    createNotification,
    createBatchNotification,
    createSystemNotification,
    deleteNotification,
    setParams,
    changePage
  };
};

/**
 * Hook for fetching and managing metrics data
 */
export const useMetrics = (initialParams = {}) => {
  const { toast } = useToast();
  const [metrics, setMetrics] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [params, setParams] = useState(initialParams);

  const fetchMetrics = useCallback(async (queryParams = params) => {
    try {
      setLoading(true);
      setError(null);

      const response = await adminApiService.metrics.getSystemMetrics(queryParams);
      setMetrics(response.data || {});

      setLoading(false);
    } catch (err) {
      console.error('Error fetching system metrics:', err);
      setError(err);
      setLoading(false);

      toast({
        title: 'Error',
        description: 'Failed to load system metrics',
        variant: 'destructive',
      });
    }
  }, [params, toast]);

  useEffect(() => {
    fetchMetrics();
  }, [fetchMetrics]);

  return {
    metrics,
    loading,
    error,
    fetchMetrics,
    setParams,
  };
};

/**
 * Hook for fetching and managing system settings
 */
export const useSystemSettings = () => {
  const { toast } = useToast();
  const [settings, setSettings] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchSettings = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await adminApiService.settings.getSystemSettings();
      setSettings(response.data || {});

      setLoading(false);
    } catch (err) {
      console.error('Error fetching system settings:', err);
      setError(err);
      setLoading(false);

      toast({
        title: 'Error',
        description: 'Failed to load system settings',
        variant: 'destructive',
      });
    }
  }, [toast]);

  useEffect(() => {
    fetchSettings();
  }, [fetchSettings]);

  const updateSettings = async (settingsData) => {
    try {
      setLoading(true);

      const response = await adminApiService.settings.updateSystemSettings(settingsData);
      setSettings(response.data || {});

      setLoading(false);

      toast({
        title: 'Success',
        description: 'Settings updated successfully',
      });

      return true;
    } catch (err) {
      console.error('Error updating system settings:', err);
      setError(err);
      setLoading(false);

      toast({
        title: 'Error',
        description: 'Failed to update system settings',
        variant: 'destructive',
      });

      return false;
    }
  };

  return {
    settings,
    loading,
    error,
    fetchSettings,
    updateSettings,
  };
};

/**
 * Hook for fetching and managing system metrics
 */
export const useSystemMetrics = (initialParams = {}) => {
  const { toast } = useToast();
  const [metrics, setMetrics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [params, setParams] = useState(initialParams);

  const fetchMetrics = useCallback(async (queryParams = params) => {
    try {
      setLoading(true);
      setError(null);

      const response = await adminApiService.metrics.getSystemMetrics(queryParams);
      setMetrics(response.data || {});

      setLoading(false);
    } catch (err) {
      console.error('Error fetching system metrics:', err);
      setError(err);
      setLoading(false);

      toast({
        title: 'Error',
        description: 'Failed to load system metrics',
        variant: 'destructive',
      });
    }
  }, [params, toast]);

  useEffect(() => {
    fetchMetrics();
  }, [fetchMetrics]);

  return {
    metrics,
    loading,
    error,
    fetchMetrics,
    setParams,
  };
};
