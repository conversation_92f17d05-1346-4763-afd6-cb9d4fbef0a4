import React, { useState } from 'react';
import { Info } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/common/components/ui/tooltip';

interface InfoButtonProps {
  content: string;
  position?: 'top' | 'bottom' | 'left' | 'right';
  className?: string;
}

/**
 * Info button component with tooltip
 *
 * Displays an information icon that shows a tooltip with additional information when hovered.
 *
 * @component
 * @example
 * <InfoButton content="This is additional information" />
 */
const InfoButton: React.FC<InfoButtonProps> = ({
  content,
  position = 'top',
  className = ''
}) => {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={300}>
        <TooltipTrigger asChild>
          <button
            type="button"
            className={`inline-flex items-center justify-center text-gray-400 hover:text-gray-300 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 ${className}`}
            aria-label="More information"
          >
            <Info className="h-4 w-4" />
          </button>
        </TooltipTrigger>
        <TooltipContent side={position} className="bg-gray-800 border-gray-700 text-gray-200 text-xs max-w-xs">
          <p>{content}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default InfoButton;
