/**
 * Account Types
 * @description TypeScript types for account-related data
 * @version 1.0.0
 * @status stable
 */

import { ChallengeType } from '@/features/challenges/types';

/**
 * Connected account status enum
 */
export enum ConnectedAccountStatus {
  READY = 'ready',
  NEEDS_REBALANCING = 'needs_rebalancing',
  IN_ACTIVE_CHALLENGE = 'in_active_challenge',
  DISCONNECTED = 'disconnected',
}

/**
 * Rebalancing calculation
 */
export interface RebalancingCalculation {
  needsRebalancing: boolean;
  currentBalance: number;
  targetBalance: number;
  difference: number;
  action: 'deposit' | 'withdraw' | 'none';
}

/**
 * Connected account
 */
export interface ConnectedAccount {
  id: number;
  userId: string;
  challengeType: ChallengeType;
  ctraderAccountId: string;
  currentEquity: number;
  initialBalance: number;
  resetCount: number;
  isActive: boolean;
  lastRebalanced?: string;
  createdAt: string;
  updatedAt: string;
  status: ConnectedAccountStatus;
  rebalancing?: RebalancingCalculation;
}

/**
 * Balance verification result
 */
export interface BalanceVerificationResult {
  verified: boolean;
  currentBalance: number;
  requiredBalance: number;
  difference: number;
  message: string;
}
