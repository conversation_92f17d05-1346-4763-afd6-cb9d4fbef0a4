import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  AlertTriangle,
  Scale,
  ArrowUpDown,
  Target,
  BarChart2,
  TrendingUp,
  TrendingDown,
  Info,
  Filter,
  SlidersHorizontal,
  RefreshCw
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/common/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/common/components/ui/table';
import { Progress } from '@/common/components/ui/progress';
import { Button } from '@/common/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/common/components/ui/select';
import { Badge } from '@/common/components/ui/badge';
import { useAnalyticsContext } from '../context/AnalyticsContext';
import InfoButton from './InfoButton';
import RealTimeIndicator from './RealTimeIndicator';

/**
 * Trade Risk Analysis component
 *
 * Displays detailed risk analysis for trades, including risk-reward ratios,
 * risk per trade, and risk distribution. Includes real-time updates.
 *
 * @component
 * @example
 * <TradeRiskAnalysis />
 */
const TradeRiskAnalysis: React.FC = () => {
  const {
    trades,
    metrics,
    lastUpdate,
    updateCounts,
    refreshData
  } = useAnalyticsContext();
  const [sortBy, setSortBy] = useState<'date' | 'risk' | 'reward' | 'ratio'>('date');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [riskFilter, setRiskFilter] = useState<'all' | 'high' | 'medium' | 'low'>('all');
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);

  // Handle refresh
  const handleRefresh = () => {
    setIsRefreshing(true);
    refreshData();

    // Reset refreshing state after animation
    setTimeout(() => {
      setIsRefreshing(false);
    }, 1000);
  };

  // Format time since last update
  const getTimeSinceLastUpdate = () => {
    const update = lastUpdate.trades;
    if (!update) return 'No updates yet';

    const now = new Date();
    const diffMs = now.getTime() - update.getTime();

    if (diffMs < 1000) return 'Just now';
    if (diffMs < 60000) return `${Math.floor(diffMs / 1000)}s ago`;
    if (diffMs < 3600000) return `${Math.floor(diffMs / 60000)}m ago`;

    return `${Math.floor(diffMs / 3600000)}h ago`;
  };

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get risk level
  const getRiskLevel = (riskPercent: number) => {
    if (riskPercent <= 1) return { level: 'Low', color: 'bg-green-400', textColor: 'text-green-400' };
    if (riskPercent <= 2) return { level: 'Medium', color: 'bg-yellow-400', textColor: 'text-yellow-400' };
    return { level: 'High', color: 'bg-red-400', textColor: 'text-red-400' };
  };

  // Filter trades based on risk level
  const filteredTrades = trades.filter(trade => {
    if (riskFilter === 'all') return true;

    // Calculate risk percentage (simplified)
    const riskPercent = trade.stopLoss && trade.openPrice
      ? Math.abs(trade.openPrice - trade.stopLoss) * trade.lotSize / 100
      : 0;

    const riskLevel = getRiskLevel(riskPercent).level.toLowerCase();
    return riskLevel === riskFilter;
  });

  // Sort trades
  const sortedTrades = [...filteredTrades].sort((a, b) => {
    if (sortBy === 'date') {
      return sortDirection === 'asc'
        ? new Date(a.entryTime).getTime() - new Date(b.entryTime).getTime()
        : new Date(b.entryTime).getTime() - new Date(a.entryTime).getTime();
    }

    // Calculate risk and reward for sorting
    const aRiskPercent = a.stopLoss && a.openPrice
      ? Math.abs(a.openPrice - a.stopLoss) * a.lotSize / 100
      : 0;

    const bRiskPercent = b.stopLoss && b.openPrice
      ? Math.abs(b.openPrice - b.stopLoss) * b.lotSize / 100
      : 0;

    const aRewardPercent = a.takeProfit && a.openPrice
      ? Math.abs(a.takeProfit - a.openPrice) * a.lotSize / 100
      : 0;

    const bRewardPercent = b.takeProfit && b.openPrice
      ? Math.abs(b.takeProfit - b.openPrice) * b.lotSize / 100
      : 0;

    const aRatio = aRiskPercent > 0 ? aRewardPercent / aRiskPercent : 0;
    const bRatio = bRiskPercent > 0 ? bRewardPercent / bRiskPercent : 0;

    if (sortBy === 'risk') {
      return sortDirection === 'asc'
        ? aRiskPercent - bRiskPercent
        : bRiskPercent - aRiskPercent;
    }

    if (sortBy === 'reward') {
      return sortDirection === 'asc'
        ? aRewardPercent - bRewardPercent
        : bRewardPercent - aRewardPercent;
    }

    // Sort by ratio
    return sortDirection === 'asc'
      ? aRatio - bRatio
      : bRatio - aRatio;
  });

  // Handle sort
  const handleSort = (column: 'date' | 'risk' | 'reward' | 'ratio') => {
    if (sortBy === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortDirection('desc');
    }
  };

  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05
      }
    }
  };

  const item = {
    hidden: { y: 10, opacity: 0 },
    show: { y: 0, opacity: 1 }
  };

  // Calculate risk distribution
  const riskDistribution = {
    low: trades.filter(t => {
      const riskPercent = t.stopLoss && t.openPrice
        ? Math.abs(t.openPrice - t.stopLoss) * t.lotSize / 100
        : 0;
      return getRiskLevel(riskPercent).level === 'Low';
    }).length,
    medium: trades.filter(t => {
      const riskPercent = t.stopLoss && t.openPrice
        ? Math.abs(t.openPrice - t.stopLoss) * t.lotSize / 100
        : 0;
      return getRiskLevel(riskPercent).level === 'Medium';
    }).length,
    high: trades.filter(t => {
      const riskPercent = t.stopLoss && t.openPrice
        ? Math.abs(t.openPrice - t.stopLoss) * t.lotSize / 100
        : 0;
      return getRiskLevel(riskPercent).level === 'High';
    }).length
  };

  const totalRiskTrades = riskDistribution.low + riskDistribution.medium + riskDistribution.high;

  return (
    <Card className="border-gray-800 bg-gray-900/60 backdrop-blur-sm overflow-hidden relative">
      {/* Real-time indicator */}
      <RealTimeIndicator dataType="trades" position="top-right" />

      <CardHeader className="pb-2">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-amber-400" />
            <div className="flex items-center gap-1.5">
              Trade Risk Analysis
              <InfoButton
                content="Detailed analysis of risk management in your trades, including risk-reward ratios and risk distribution."
                className="ml-1"
              />
            </div>
          </div>

          <div className="flex items-center gap-3">
            <div className="text-xs text-gray-400 flex items-center gap-1">
              <span>Updated:</span>
              <span className={lastUpdate.trades ? 'text-gray-300' : 'text-gray-500'}>
                {getTimeSinceLastUpdate()}
              </span>
              {updateCounts.trades > 0 && (
                <Badge variant="outline" className="ml-1 text-[10px] h-4 px-1 border-blue-700 text-blue-400">
                  {updateCounts.trades} updates
                </Badge>
              )}
            </div>

            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              <AnimatePresence mode="wait">
                {isRefreshing ? (
                  <motion.div
                    key="refreshing"
                    initial={{ opacity: 0, rotate: 0 }}
                    animate={{ opacity: 1, rotate: 360 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.5 }}
                  >
                    <RefreshCw className="h-4 w-4 text-blue-400" />
                  </motion.div>
                ) : (
                  <motion.div
                    key="refresh"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                  >
                    <RefreshCw className="h-4 w-4 text-gray-400 hover:text-white" />
                  </motion.div>
                )}
              </AnimatePresence>
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Risk Summary */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700">
              <div className="flex items-center justify-between mb-2">
                <div className="text-sm font-medium text-gray-300">Average Risk/Reward</div>
                <ArrowUpDown className="h-4 w-4 text-blue-400" />
              </div>
              <div className="text-2xl font-bold">{metrics?.averageRiskRewardRatio?.toFixed(2) || '0.00'}</div>
              <Progress
                value={Math.min((metrics?.averageRiskRewardRatio || 0) * 33.3, 100)}
                max={100}
                className="h-1.5 mt-2 bg-gray-700"
                indicatorClassName="bg-blue-400"
              />
              <div className="text-xs text-gray-400 mt-1">
                {metrics?.averageRiskRewardRatio && metrics.averageRiskRewardRatio >= 2
                  ? 'Excellent risk management'
                  : metrics?.averageRiskRewardRatio && metrics.averageRiskRewardRatio >= 1
                    ? 'Good risk management'
                    : 'Needs improvement'}
              </div>
            </div>

            <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700">
              <div className="flex items-center justify-between mb-2">
                <div className="text-sm font-medium text-gray-300">Max Risk Per Trade</div>
                <AlertTriangle className="h-4 w-4 text-amber-400" />
              </div>
              <div className="text-2xl font-bold">{metrics?.maxRiskPerTrade?.toFixed(2) || '0.00'}%</div>
              <Progress
                value={Math.min((metrics?.maxRiskPerTrade || 0) * 20, 100)}
                max={100}
                className="h-1.5 mt-2 bg-gray-700"
                indicatorClassName="bg-amber-400"
              />
              <div className="text-xs text-gray-400 mt-1">
                {metrics?.maxRiskPerTrade && metrics.maxRiskPerTrade <= 1
                  ? 'Conservative risk approach'
                  : metrics?.maxRiskPerTrade && metrics.maxRiskPerTrade <= 2
                    ? 'Moderate risk approach'
                    : 'Aggressive risk approach'}
              </div>
            </div>

            <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700">
              <div className="flex items-center justify-between mb-2">
                <div className="text-sm font-medium text-gray-300">Risk Distribution</div>
                <SlidersHorizontal className="h-4 w-4 text-purple-400" />
              </div>
              <div className="flex gap-1 h-4 mt-2">
                <div
                  className="bg-green-400 rounded-l-sm"
                  style={{ width: `${totalRiskTrades ? (riskDistribution.low / totalRiskTrades) * 100 : 0}%` }}
                ></div>
                <div
                  className="bg-yellow-400"
                  style={{ width: `${totalRiskTrades ? (riskDistribution.medium / totalRiskTrades) * 100 : 0}%` }}
                ></div>
                <div
                  className="bg-red-400 rounded-r-sm"
                  style={{ width: `${totalRiskTrades ? (riskDistribution.high / totalRiskTrades) * 100 : 0}%` }}
                ></div>
              </div>
              <div className="flex justify-between text-xs text-gray-400 mt-2">
                <div>Low: {riskDistribution.low}</div>
                <div>Medium: {riskDistribution.medium}</div>
                <div>High: {riskDistribution.high}</div>
              </div>
            </div>
          </div>

          {/* Filters and Controls */}
          <div className="flex flex-wrap gap-2 justify-between items-center">
            <div className="flex gap-2 items-center">
              <Filter className="h-4 w-4 text-gray-400" />
              <Select value={riskFilter} onValueChange={(value: any) => setRiskFilter(value)}>
                <SelectTrigger className="w-[120px] h-8 text-xs bg-gray-800 border-gray-700">
                  <SelectValue placeholder="Risk Level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Risks</SelectItem>
                  <SelectItem value="low">Low Risk</SelectItem>
                  <SelectItem value="medium">Medium Risk</SelectItem>
                  <SelectItem value="high">High Risk</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                className={`text-xs ${sortBy === 'date' ? 'bg-blue-900/30 border-blue-700' : 'bg-gray-800 border-gray-700'}`}
                onClick={() => handleSort('date')}
              >
                Date {sortBy === 'date' && (sortDirection === 'asc' ? '↑' : '↓')}
              </Button>
              <Button
                variant="outline"
                size="sm"
                className={`text-xs ${sortBy === 'risk' ? 'bg-blue-900/30 border-blue-700' : 'bg-gray-800 border-gray-700'}`}
                onClick={() => handleSort('risk')}
              >
                Risk {sortBy === 'risk' && (sortDirection === 'asc' ? '↑' : '↓')}
              </Button>
              <Button
                variant="outline"
                size="sm"
                className={`text-xs ${sortBy === 'reward' ? 'bg-blue-900/30 border-blue-700' : 'bg-gray-800 border-gray-700'}`}
                onClick={() => handleSort('reward')}
              >
                Reward {sortBy === 'reward' && (sortDirection === 'asc' ? '↑' : '↓')}
              </Button>
              <Button
                variant="outline"
                size="sm"
                className={`text-xs ${sortBy === 'ratio' ? 'bg-blue-900/30 border-blue-700' : 'bg-gray-800 border-gray-700'}`}
                onClick={() => handleSort('ratio')}
              >
                R:R Ratio {sortBy === 'ratio' && (sortDirection === 'asc' ? '↑' : '↓')}
              </Button>
            </div>
          </div>

          {/* Trade Risk Table */}
          {sortedTrades.length === 0 ? (
            <div className="text-center py-6 text-gray-400">
              <AlertTriangle className="h-12 w-12 mx-auto mb-3 text-gray-500" />
              <p>No trade data available for risk analysis.</p>
              <p className="text-sm mt-2">Trades with stop loss and take profit values are required for risk analysis.</p>
            </div>
          ) : (
            <div className="rounded-md border border-gray-700 overflow-hidden">
              <Table>
                <TableHeader className="bg-gray-800/50">
                  <TableRow>
                    <TableHead className="text-xs">Symbol</TableHead>
                    <TableHead className="text-xs">Date</TableHead>
                    <TableHead className="text-xs">Direction</TableHead>
                    <TableHead className="text-xs">Risk</TableHead>
                    <TableHead className="text-xs">Reward</TableHead>
                    <TableHead className="text-xs">R:R Ratio</TableHead>
                    <TableHead className="text-xs">Outcome</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedTrades.slice(0, 10).map((trade) => {
                    // Calculate risk and reward
                    const riskPercent = trade.stopLoss && trade.openPrice
                      ? Math.abs(trade.openPrice - trade.stopLoss) * trade.lotSize / 100
                      : 0;

                    const rewardPercent = trade.takeProfit && trade.openPrice
                      ? Math.abs(trade.takeProfit - trade.openPrice) * trade.lotSize / 100
                      : 0;

                    const ratio = riskPercent > 0 ? rewardPercent / riskPercent : 0;
                    const riskLevel = getRiskLevel(riskPercent);

                    return (
                      <TableRow key={trade.id} className="hover:bg-gray-800/30">
                        <TableCell className="text-xs font-medium">{trade.symbol}</TableCell>
                        <TableCell className="text-xs">{formatDate(trade.entryTime)}</TableCell>
                        <TableCell className="text-xs">
                          <Badge variant={trade.direction === 'BUY' ? 'default' : 'destructive'} className="text-[10px]">
                            {trade.direction}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-xs">
                          <div className="flex items-center gap-1.5">
                            <div className={`w-2 h-2 rounded-full ${riskLevel.color}`}></div>
                            <span>{riskPercent.toFixed(2)}%</span>
                          </div>
                        </TableCell>
                        <TableCell className="text-xs">{rewardPercent.toFixed(2)}%</TableCell>
                        <TableCell className="text-xs font-medium">
                          <span className={ratio >= 1 ? 'text-green-400' : 'text-amber-400'}>
                            {ratio.toFixed(2)}
                          </span>
                        </TableCell>
                        <TableCell className="text-xs">
                          <span className={trade.pnl >= 0 ? 'text-green-400' : 'text-red-400'}>
                            {formatCurrency(trade.pnl)}
                          </span>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default TradeRiskAnalysis;
