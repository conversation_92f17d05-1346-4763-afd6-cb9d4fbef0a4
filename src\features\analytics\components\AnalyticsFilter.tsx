import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { apiService } from '@/common/services/api';
import { useAnalyticsContext } from '../context/AnalyticsContext';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/common/components/ui/select';
import { Label } from '@/common/components/ui/label';
import { Card, CardContent } from '@/common/components/ui/card';
import { Info } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/common/components/ui/tooltip';

/**
 * Analytics filter component
 *
 * Allows filtering analytics data by cTrader account ID or viewing aggregated data
 */
const AnalyticsFilter: React.FC = () => {
  const { selectedFilter, setSelectedFilter } = useAnalyticsContext();

  // Fetch user's cTrader account IDs
  const { data: ctraderAccounts, isLoading } = useQuery({
    queryKey: ['ctraderAccounts'],
    queryFn: async () => {
      try {
        const accounts = await apiService.getCtraderAccounts();
        return accounts || [];
      } catch (error) {
        console.error('Error fetching cTrader accounts:', error);
        return [];
      }
    },
  });

  const handleFilterChange = (value: string) => {
    if (value === 'all') {
      setSelectedFilter({ type: 'all' });
    } else {
      setSelectedFilter({ type: 'ctraderAccount', ctraderAccountId: value });
    }
  };

  return (
    <Card className="mb-6">
      <CardContent className="pt-6">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <Label htmlFor="filter-select" className="text-sm font-medium">
              View Analytics For
            </Label>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                </TooltipTrigger>
                <TooltipContent className="max-w-sm">
                  <p>
                    "All Challenges" shows combined stats across all your trading activity.
                    Select a specific cTrader account to view stats for just that account.
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>

        <Select
          value={selectedFilter.type === 'ctraderAccount' ? selectedFilter.ctraderAccountId : 'all'}
          onValueChange={handleFilterChange}
          disabled={isLoading}
        >
          <SelectTrigger id="filter-select" className="w-full md:w-[300px]">
            <SelectValue placeholder="Select view" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Challenges</SelectItem>
            {ctraderAccounts && ctraderAccounts.length > 0 && (
              <>
                <div className="px-2 py-1.5 text-xs font-medium text-muted-foreground">
                  cTrader Accounts
                </div>
                {ctraderAccounts.map((account: string) => (
                  <SelectItem key={account} value={account}>
                    Account {account.substring(0, 6)}...{account.substring(account.length - 4)}
                  </SelectItem>
                ))}
              </>
            )}
          </SelectContent>
        </Select>
      </CardContent>
    </Card>
  );
};

export default AnalyticsFilter;
