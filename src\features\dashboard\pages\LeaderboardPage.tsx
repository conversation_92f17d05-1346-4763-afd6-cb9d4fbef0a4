/**
 * Dashboard Leaderboard Page
 *
 * Displays comprehensive leaderboards for all challenges and allows
 * filtering by challenge type, time period, and more.
 *
 * @status experimental
 * @version 1.0.0
 */

import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { DashboardLayout } from '@/features/dashboard';
import {
  fetchGlobalLeaderboard,
  selectGlobalLeaderboard,
  selectLeaderboardLoading,
  selectLeaderboardError,
  selectLeaderboardLastUpdated
} from '@/features/leaderboard/redux/leaderboardSlice';
import { RootState } from '@/app/store';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Button,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Input,
  Badge,
  Separator
} from '@/common/components/ui';
import {
  Trophy,
  Medal,
  Users,
  Filter,
  Search,
  Calendar,
  ArrowUpDown,
  Clock,
  <PERSON><PERSON><PERSON>,
  Star,
  Medal as MedalIcon,
  Award,
  RefreshCw
} from 'lucide-react';
import { motion } from 'framer-motion';
import { useChallengeContext } from '../context/ChallengeContext';

// Mock data for leaderboard entries
const generateMockLeaderboardData = (count: number) => {
  const entries = [];
  for (let i = 1; i <= count; i++) {
    entries.push({
      rank: i,
      userId: `user-${i}`,
      username: `Trader${i}`,
      pnl: (Math.random() * 40 - 10).toFixed(2),
      drawdown: (Math.random() * 15).toFixed(2),
      winRate: (Math.random() * 60 + 40).toFixed(1),
      trades: Math.floor(Math.random() * 50) + 10,
      isCurrentUser: i === 5, // Make one entry the current user
    });
  }
  return entries;
};

/**
 * Global leaderboard tab content
 */
const GlobalLeaderboard: React.FC = () => {
  const [timeframe, setTimeframe] = useState('allTime');
  const [searchQuery, setSearchQuery] = useState('');
  const dispatch = useDispatch();
  const leaderboardData = useSelector(selectGlobalLeaderboard);
  const loading = useSelector(selectLeaderboardLoading);
  const error = useSelector(selectLeaderboardError);
  const lastUpdated = useSelector((state: RootState) => selectLeaderboardLastUpdated(state, 'global'));

  // Fetch global leaderboard on component mount
  useEffect(() => {
    dispatch(fetchGlobalLeaderboard(50));
  }, [dispatch]);

  // Filter data based on search query
  const filteredData = searchQuery
    ? leaderboardData.filter(entry =>
        entry.username.toLowerCase().includes(searchQuery.toLowerCase()))
    : leaderboardData;

  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row justify-between gap-4">
        <div className="flex flex-wrap gap-2">
          <Select value={timeframe} onValueChange={setTimeframe}>
            <SelectTrigger className="w-[180px] bg-blue-900/20 border-blue-500/30">
              <SelectValue placeholder="Select timeframe" />
            </SelectTrigger>
            <SelectContent className="bg-blue-900/95 border-blue-500/30">
              <SelectItem value="allTime">All Time</SelectItem>
              <SelectItem value="month">This Month</SelectItem>
              <SelectItem value="week">This Week</SelectItem>
              <SelectItem value="today">Today</SelectItem>
            </SelectContent>
          </Select>

          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search traders..."
              className="pl-9 bg-blue-900/20 border-blue-500/30"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" className="h-9">
            <Filter className="mr-2 h-4 w-4" />
            More Filters
          </Button>
        </div>
      </div>

      <Card className="border-blue-500/20 bg-[#0f1c2e]/60 backdrop-blur-sm overflow-hidden">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Global Rankings</CardTitle>
              <CardDescription>Top traders across all active challenges</CardDescription>
            </div>
            {lastUpdated && (
              <div className="text-xs text-gray-400 flex items-center">
                <Clock className="h-3 w-3 mr-1" />
                Last updated: {new Date(lastUpdated).toLocaleTimeString()}
              </div>
            )}
          </div>
        </CardHeader>

        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <p className="text-red-400 mb-4">{error}</p>
              <Button
                variant="outline"
                onClick={() => dispatch(fetchGlobalLeaderboard(50))}
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Try Again
              </Button>
            </div>
          ) : filteredData.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-400 mb-4">No leaderboard data available</p>
              <Button
                variant="outline"
                onClick={() => dispatch(fetchGlobalLeaderboard(50))}
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh
              </Button>
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="w-full min-w-[800px]">
                  <thead>
                    <tr className="border-b border-blue-500/20 text-xs text-gray-400">
                      <th className="py-3 px-4 text-left">Rank</th>
                      <th className="py-3 px-4 text-left">Trader</th>
                      <th className="py-3 px-4 text-right">
                        <div className="flex items-center justify-end">
                          <span>P&L %</span>
                          <ArrowUpDown className="ml-1 h-3 w-3" />
                        </div>
                      </th>
                      <th className="py-3 px-4 text-right">
                        <div className="flex items-center justify-end">
                          <span>Max DD</span>
                          <ArrowUpDown className="ml-1 h-3 w-3" />
                        </div>
                      </th>
                      <th className="py-3 px-4 text-right">
                        <div className="flex items-center justify-end">
                          <span>Win Rate</span>
                          <ArrowUpDown className="ml-1 h-3 w-3" />
                        </div>
                      </th>
                      <th className="py-3 px-4 text-right">
                        <div className="flex items-center justify-end">
                          <span>Trades</span>
                        </div>
                      </th>
                      <th className="py-3 px-4 text-right">
                        <div className="flex items-center justify-end">
                          <span>Challenge</span>
                        </div>
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredData.slice(0, 20).map((entry) => (
                      <motion.tr
                        key={entry.userId}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="text-sm border-b border-blue-500/10 hover:bg-blue-500/10 transition-colors"
                      >
                        <td className="py-3 px-4">
                          {entry.rank <= 3 ? (
                            <div className="flex items-center">
                              {entry.rank === 1 && <Trophy className="h-4 w-4 text-yellow-400 mr-1" />}
                              {entry.rank === 2 && <Medal className="h-4 w-4 text-gray-400 mr-1" />}
                              {entry.rank === 3 && <Award className="h-4 w-4 text-amber-700 mr-1" />}
                              <span className="font-bold">#{entry.rank}</span>
                            </div>
                          ) : (
                            <span>#{entry.rank}</span>
                          )}
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center">
                            <div className="h-8 w-8 rounded-full bg-gradient-to-br from-blue-600 to-indigo-800 flex items-center justify-center mr-2 text-xs font-bold">
                              {entry.username.substring(0, 2).toUpperCase()}
                            </div>
                            <div className="font-medium">{entry.username}</div>
                          </div>
                        </td>
                        <td className="py-3 px-4 text-right">
                          <span className={entry.score >= 0 ? 'text-green-400' : 'text-red-400'}>
                            {entry.score >= 0 ? '+' : ''}{entry.score.toFixed(2)}%
                          </span>
                        </td>
                        <td className="py-3 px-4 text-right">
                          <span className="text-amber-400">{entry.drawdown.toFixed(2)}%</span>
                        </td>
                        <td className="py-3 px-4 text-right">
                          {entry.winRate.toFixed(1)}%
                        </td>
                        <td className="py-3 px-4 text-right">
                          {entry.tradeCount}
                        </td>
                        <td className="py-3 px-4 text-right">
                          {entry.challengeName || `Challenge #${entry.challengeId}`}
                        </td>
                      </motion.tr>
                    ))}
                  </tbody>
                </table>
              </div>

              <div className="flex justify-between items-center mt-4">
                <div className="text-xs text-gray-400">
                  Showing {Math.min(filteredData.length, 20)} of {filteredData.length} traders
                </div>

                {filteredData.length > 20 && (
                  <Button variant="outline" size="sm">
                    Load More
                  </Button>
                )}
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

/**
 * Challenge-specific leaderboard tab content
 */
const ChallengeLeaderboards: React.FC = () => {
  const { challenges } = useChallengeContext();
  const [selectedChallengeType, setSelectedChallengeType] = useState('daily');
  const [selectedChallenge, setSelectedChallenge] = useState<string | null>(null);

  // Filter challenges by type
  const filteredChallenges = challenges.filter(
    challenge => challenge.type.toLowerCase() === selectedChallengeType
  );

  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row gap-4">
        <Tabs defaultValue="daily" onValueChange={setSelectedChallengeType} className="w-full">
          <TabsList className="mb-4 bg-blue-900/20 border border-blue-500/30">
            <TabsTrigger value="daily" className="data-[state=active]:bg-blue-500/20">
              Daily
            </TabsTrigger>
            <TabsTrigger value="weekly" className="data-[state=active]:bg-blue-500/20">
              Weekly
            </TabsTrigger>
            <TabsTrigger value="monthly" className="data-[state=active]:bg-blue-500/20">
              Monthly
            </TabsTrigger>
            <TabsTrigger value="micro" className="data-[state=active]:bg-blue-500/20">
              Micro
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredChallenges.map((challenge) => (
          <Card
            key={challenge.id}
            className={`border-blue-500/20 bg-[#0f1c2e]/60 backdrop-blur-sm cursor-pointer transition-all hover:shadow-lg hover:border-blue-500/40 ${
              selectedChallenge === challenge.id ? 'ring-2 ring-blue-500' : ''
            }`}
            onClick={() => setSelectedChallenge(challenge.id)}
          >
            <CardHeader className="pb-2">
              <div className="flex justify-between">
                <CardTitle className="text-base font-medium">{challenge.title}</CardTitle>
                <Badge className={
                  challenge.status === 'active' ? 'bg-green-500/20 text-green-300' :
                  challenge.status === 'upcoming' ? 'bg-blue-500/20 text-blue-300' :
                  'bg-gray-500/20 text-gray-300'
                }>
                  {challenge.status}
                </Badge>
              </div>
              <CardDescription className="flex items-center text-xs">
                <Calendar className="h-3 w-3 mr-1" />
                {new Date(challenge.startDate).toLocaleDateString()} - {new Date(challenge.endDate).toLocaleDateString()}
              </CardDescription>
            </CardHeader>

            <CardContent>
              <div className="space-y-3 mt-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">Prize Pool:</span>
                  <span className="font-medium">${challenge.prizePool.toLocaleString()}</span>
                </div>

                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">Participants:</span>
                  <span className="font-medium">{challenge.participants}</span>
                </div>

                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">Your Rank:</span>
                  <span className="font-medium">
                    {challenge.userRank ? (
                      <span>#{challenge.userRank}</span>
                    ) : (
                      <span className="text-gray-500">Not joined</span>
                    )}
                  </span>
                </div>

                <Separator className="bg-blue-500/20 my-3" />

                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={(e) => {
                    e.stopPropagation();
                    window.location.href = `/leaderboard/${challenge.id}`;
                  }}
                >
                  <Trophy className="mr-2 h-4 w-4" />
                  View Leaderboard
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

/**
 * Hall of fame tab content
 */
const HallOfFame: React.FC = () => {
  const hallOfFameEntries = [
    {
      id: '1',
      username: 'MasterTrader',
      achievements: ['Top 3 Winner', '10x Challenge Winner', 'Perfect Month'],
      stats: {
        winRate: '78.2%',
        bestReturn: '+187.4%',
        challengesWon: 24,
      },
      joinDate: 'Jan 2023',
      avatarColor: 'from-yellow-500 to-amber-700',
    },
    {
      id: '2',
      username: 'ForexKing',
      achievements: ['Streak Legend', 'Top 3 Winner', 'No Drawdown'],
      stats: {
        winRate: '71.3%',
        bestReturn: '+145.8%',
        challengesWon: 18,
      },
      joinDate: 'Mar 2023',
      avatarColor: 'from-blue-600 to-indigo-800',
    },
    {
      id: '3',
      username: 'PipMaster',
      achievements: ['Weekly Champion', '30+ Consecutive Wins', 'Risk Genius'],
      stats: {
        winRate: '69.5%',
        bestReturn: '+132.1%',
        challengesWon: 15,
      },
      joinDate: 'Feb 2023',
      avatarColor: 'from-purple-600 to-indigo-800',
    },
    {
      id: '4',
      username: 'ChartWizard',
      achievements: ['Top 30% Legend', 'Micro Master', 'Pattern Spotter'],
      stats: {
        winRate: '67.8%',
        bestReturn: '+118.9%',
        challengesWon: 12,
      },
      joinDate: 'Apr 2023',
      avatarColor: 'from-green-500 to-emerald-700',
    },
  ];

  return (
    <div className="space-y-6">
      <Card className="border-blue-500/20 bg-[#0f1c2e]/60 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Star className="h-5 w-5 text-yellow-400 mr-2" />
            Hall of Fame
          </CardTitle>
          <CardDescription>
            Top-performing traders recognized for their exceptional achievements
          </CardDescription>
        </CardHeader>

        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {hallOfFameEntries.map((entry) => (
              <Card key={entry.id} className="bg-blue-900/20 border-blue-500/20">
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <div className={`h-14 w-14 rounded-full bg-gradient-to-br ${entry.avatarColor} flex items-center justify-center text-lg font-bold`}>
                      {entry.username.substring(0, 2).toUpperCase()}
                    </div>

                    <div className="flex-1">
                      <h3 className="font-bold text-lg">{entry.username}</h3>
                      <p className="text-xs text-gray-400">Member since {entry.joinDate}</p>

                      <div className="flex flex-wrap gap-2 mt-3">
                        {entry.achievements.map((achievement, i) => (
                          <Badge key={i} className="bg-blue-500/20 text-blue-300">
                            {achievement}
                          </Badge>
                        ))}
                      </div>

                      <div className="grid grid-cols-3 gap-4 mt-4">
                        <div>
                          <p className="text-xs text-gray-400">Win Rate</p>
                          <p className="font-medium">{entry.stats.winRate}</p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-400">Best Return</p>
                          <p className="font-medium text-green-400">{entry.stats.bestReturn}</p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-400">Challenges Won</p>
                          <p className="font-medium">{entry.stats.challengesWon}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

/**
 * Main dashboard leaderboard page component
 */
const LeaderboardPage: React.FC = () => {
  return (
    <DashboardLayout>
      <div className="space-y-6 pb-12">
        <div className="space-y-0.5">
          <h2 className="text-2xl font-bold tracking-tight">Leaderboards</h2>
          <p className="text-gray-400">
            Track top performers across all challenges and view your rankings.
          </p>
        </div>

        <Tabs defaultValue="global" className="space-y-6">
          <TabsList className="mb-6 bg-blue-900/20 border border-blue-500/30">
            <TabsTrigger value="global" className="data-[state=active]:bg-blue-500/20">
              <Trophy className="h-4 w-4 mr-2" />
              Global Rankings
            </TabsTrigger>
            <TabsTrigger value="challenges" className="data-[state=active]:bg-blue-500/20">
              <BarChart className="h-4 w-4 mr-2" />
              Challenge Leaderboards
            </TabsTrigger>
            <TabsTrigger value="halloffame" className="data-[state=active]:bg-blue-500/20">
              <MedalIcon className="h-4 w-4 mr-2" />
              Hall of Fame
            </TabsTrigger>
          </TabsList>

          <TabsContent value="global" className="mt-0 space-y-4">
            <GlobalLeaderboard />
          </TabsContent>

          <TabsContent value="challenges" className="mt-0 space-y-4">
            <ChallengeLeaderboards />
          </TabsContent>

          <TabsContent value="halloffame" className="mt-0 space-y-4">
            <HallOfFame />
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default LeaderboardPage;