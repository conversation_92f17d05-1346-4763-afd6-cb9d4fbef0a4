/**
 * Dashboard Challenges Page
 *
 * Unified interface for browsing and managing challenges within the dashboard layout.
 *
 * @status stable
 * @version 2.0.0
 */

import React, { useState, useEffect } from 'react';
import { ChallengeBrowser, ChallengeManagementHub } from '@/features/challenges';
import { DashboardLayout } from '@/features/dashboard';
import { Toaster } from '@/common/components/ui/sonner';
import { motion } from 'framer-motion';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/common/components/ui';
import { Trophy, List } from 'lucide-react';
import { useLocation, useNavigate } from 'react-router-dom';

/**
 * Dashboard Challenges Page component
 * Shows a unified interface for browsing and managing challenges
 *
 * @returns DashboardChallengesPage component
 */
const DashboardChallengesPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('management');
  const location = useLocation();
  const navigate = useNavigate();

  // Check for tab parameter in URL
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const tabParam = params.get('tab');

    if (tabParam && (tabParam === 'browser' || tabParam === 'management')) {
      setActiveTab(tabParam);

      // Remove the tab parameter from the URL to keep it clean
      const newParams = new URLSearchParams(location.search);
      newParams.delete('tab');
      const newSearch = newParams.toString();
      const newPath = location.pathname + (newSearch ? `?${newSearch}` : '');

      // Only update if there are other parameters
      if (location.search !== `?tab=${tabParam}`) {
        navigate(newPath, { replace: true });
      }
    }
  }, [location, navigate]);

  return (
    <DashboardLayout>
      <motion.div
        className="space-y-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Tabs
          defaultValue="management"
          value={activeTab}
          onValueChange={(value) => {
            setActiveTab(value);
            // Update URL with the selected tab for bookmarking/sharing
            const newUrl = new URL(window.location.href);
            newUrl.searchParams.set('tab', value);
            window.history.replaceState({}, '', newUrl.toString());
          }}
          className="w-full"
        >
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-3xl font-bold tracking-tight">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-blue-600">
                Challenges
              </span>
            </h1>
            <TabsList className="grid grid-cols-2 w-[400px]">
              <TabsTrigger value="management" className="flex items-center gap-2">
                <Trophy className="h-4 w-4" />
                Your Challenges
              </TabsTrigger>
              <TabsTrigger value="browser" className="flex items-center gap-2">
                <List className="h-4 w-4" />
                Browse Challenges
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="management" className="mt-0">
            <ChallengeManagementHub />
          </TabsContent>

          <TabsContent value="browser" className="mt-0">
            <ChallengeBrowser />
          </TabsContent>
        </Tabs>
      </motion.div>
      <Toaster position="top-right" />
    </DashboardLayout>
  );
};

export default DashboardChallengesPage;