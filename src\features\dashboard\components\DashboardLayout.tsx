import React, { ReactNode, useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import DashboardHeader from './DashboardHeader';
import DashboardSidebar from './DashboardSidebar';
import MobileNavigation from './MobileNavigation';
import DynamicBackground from './DynamicBackground';
import { ChallengeProvider } from '../context/ChallengeContext';
import { useResponsive } from '@/common/context';
import ErrorBoundary from '@/common/components/ErrorBoundary';

interface DashboardLayoutProps {
  children: ReactNode;
}

/**
 * Main dashboard layout component
 *
 * Provides the structure for the dashboard with header, sidebar, and content area.
 * Wraps children in the ChallengeProvider for multi-challenge support.
 *
 * @component
 * @example
 * <DashboardLayout>
 *   <DashboardContent />
 * </DashboardLayout>
 */
const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  const { isMobile, screenSize } = useResponsive();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [showMobileMenu, setShowMobileMenu] = useState(false);

  // Handle sidebar collapse state
  const handleSidebarCollapse = (collapsed: boolean) => {
    setSidebarCollapsed(collapsed);
  };

  // Listen for custom event to toggle mobile menu
  useEffect(() => {
    const handleToggleMobileMenu = () => {
      setShowMobileMenu(prev => !prev);
    };

    window.addEventListener('toggle-mobile-menu', handleToggleMobileMenu);

    return () => {
      window.removeEventListener('toggle-mobile-menu', handleToggleMobileMenu);
    };
  }, []);

  // Determine the appropriate padding based on screen size
  const getMainPadding = () => {
    if (screenSize === 'xs' || screenSize === 'sm') {
      return 'pb-20'; // Extra padding for mobile navigation
    }
    return '';
  };

  return (
    <ErrorBoundary componentName="ChallengeProvider" containError={true}>
      <ChallengeProvider>
        <DynamicBackground>
          <div className="min-h-screen text-white">
            {/* Pass showMobileMenu state to header */}
            <DashboardHeader />

            <div className="flex pt-16"> {/* Added padding-top to account for fixed header */}
              {/* Sidebar - hidden on mobile */}
              <DashboardSidebar onCollapse={handleSidebarCollapse} />

              {/* Main content area */}
              <motion.main
                className={`
                  flex-1 p-4 sm:p-5 md:p-6 transition-all duration-300
                  ${sidebarCollapsed ? 'lg:ml-[80px]' : 'lg:ml-[240px]'}
                  ${getMainPadding()}
                `}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5 }}
              >
                <div className="container mx-auto">
                  {children}
                </div>
              </motion.main>
            </div>

            {/* Mobile navigation - only visible on small screens */}
            <MobileNavigation />
          </div>
        </DynamicBackground>
      </ChallengeProvider>
    </ErrorBoundary>
  );
};

export default DashboardLayout;
