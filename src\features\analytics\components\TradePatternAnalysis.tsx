import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/common/components/ui/card';
import { Badge } from '@/common/components/ui/badge';
import { useAnalyticsContext } from '../context/AnalyticsContext';
import { Lightbulb, TrendingUp, TrendingDown, AlertCircle } from 'lucide-react';
import InfoButton from './InfoButton';

/**
 * Trade pattern analysis component
 *
 * Identifies recurring patterns in trading behavior and correlates them with outcomes.
 *
 * @component
 * @example
 * <TradePatternAnalysis />
 */
const TradePatternAnalysis: React.FC = () => {
  const { patterns, trades } = useAnalyticsContext();

  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { y: 20, opacity: 0 },
    show: { y: 0, opacity: 1 }
  };

  // Get trade details by ID
  const getTradeById = (id: number) => {
    return trades.find(trade => trade.id === id);
  };

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  return (
    <Card className="border-gray-800 bg-gray-900/60 backdrop-blur-sm overflow-hidden">
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2">
          <Lightbulb className="h-5 w-5 text-yellow-400" />
          <div className="flex items-center gap-1.5">
            Trade Pattern Analysis
            <InfoButton
              content="This analysis identifies recurring patterns in your trading behavior and correlates them with outcomes. Use these insights to reinforce successful patterns and avoid problematic ones."
              className="ml-1"
            />
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {patterns.length === 0 ? (
          <div className="text-center py-6 text-gray-400">
            <AlertCircle className="h-12 w-12 mx-auto mb-3 text-gray-500" />
            <p>Not enough trade data to identify patterns.</p>
            <p className="text-sm mt-2">Complete more trades to unlock pattern recognition.</p>
          </div>
        ) : (
          <motion.div
            variants={container}
            initial="hidden"
            animate="show"
            className="space-y-4"
          >
            {patterns.map((pattern, index) => (
              <motion.div
                key={index}
                variants={item}
                className="bg-gray-800/50 rounded-lg p-4 border border-gray-700 hover:border-gray-600 transition-colors"
              >
                <div className="flex justify-between items-start mb-2">
                  <div className="flex items-center gap-1.5">
                    <h3 className="font-medium text-white">{pattern.name}</h3>
                    <InfoButton
                      content={`This pattern occurs ${pattern.occurrences} times in your trading history. Understanding and leveraging this pattern could improve your trading results.`}
                      className="ml-1"
                    />
                  </div>
                  <Badge className={pattern.winRate >= 50 ? 'bg-green-600' : 'bg-red-600'}>
                    {pattern.winRate.toFixed(1)}% Win Rate
                  </Badge>
                </div>

                <p className="text-sm text-gray-400 mb-3">{pattern.description}</p>

                <div className="grid grid-cols-3 gap-2 text-xs mb-3">
                  <div className="bg-gray-700/50 rounded p-2">
                    <span className="block text-gray-400">Occurrences</span>
                    <span className="font-medium">{pattern.occurrences}</span>
                  </div>
                  <div className="bg-gray-700/50 rounded p-2">
                    <span className="block text-gray-400">Avg P&L</span>
                    <span className={`font-medium ${pattern.averagePnl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                      {formatCurrency(pattern.averagePnl)}
                    </span>
                  </div>
                  <div className="bg-gray-700/50 rounded p-2">
                    <span className="block text-gray-400">Trend</span>
                    <span className="font-medium flex items-center">
                      {pattern.averagePnl >= 0 ? (
                        <>
                          <TrendingUp className="h-3 w-3 text-green-400 mr-1" />
                          <span className="text-green-400">Positive</span>
                        </>
                      ) : (
                        <>
                          <TrendingDown className="h-3 w-3 text-red-400 mr-1" />
                          <span className="text-red-400">Negative</span>
                        </>
                      )}
                    </span>
                  </div>
                </div>

                <div className="text-xs text-gray-400">
                  <div className="font-medium mb-1">Recent Examples:</div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                    {pattern.trades.slice(0, 4).map(tradeId => {
                      const trade = getTradeById(tradeId);
                      if (!trade) return null;

                      return (
                        <div key={tradeId} className="bg-gray-700/30 rounded p-2 flex justify-between">
                          <div>
                            <span className="block">{trade.symbol}</span>
                            <span className="text-xs text-gray-500">
                              {new Date(trade.entryTime).toLocaleDateString()}
                            </span>
                          </div>
                          <span className={trade.pnl >= 0 ? 'text-green-400' : 'text-red-400'}>
                            {formatCurrency(trade.pnl)}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        )}
      </CardContent>
    </Card>
  );
};

export default TradePatternAnalysis;
