/**
 * Batch Payment Form Component
 * @description Component for processing batch payments for prize distributions
 * @version 1.0.0
 * @status stable
 */

import React, { useState } from 'react';
import { 
  <PERSON><PERSON>,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  Input,
  Label,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/common/components/ui';
import { DollarSign, RefreshCw, AlertCircle } from 'lucide-react';
import { apiService } from '@/common/services/api';
import { toast } from 'sonner';
import { formatCurrency } from '@/common/utils/formatters';

interface PrizeDistribution {
  id: number;
  userId: string;
  rank: number;
  amount: number;
  status: string;
  user: {
    username: string;
    email: string;
    cryptoAddress?: string;
  };
}

interface BatchPaymentFormProps {
  challengeId: number;
  prizeDistributions: PrizeDistribution[];
  onSuccess: () => void;
}

interface PaymentData {
  id: number;
  paymentTxHash: string;
  paymentAddress?: string;
}

/**
 * Batch Payment Form Component
 * @param props - Component props
 * @returns Batch Payment Form Component
 */
const BatchPaymentForm: React.FC<BatchPaymentFormProps> = ({ 
  challengeId,
  prizeDistributions,
  onSuccess
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [paymentData, setPaymentData] = useState<PaymentData[]>([]);
  const [commonTxHash, setCommonTxHash] = useState('');
  const [useCommonTxHash, setUseCommonTxHash] = useState(false);

  // Initialize payment data when dialog opens
  const handleOpenChange = (open: boolean) => {
    if (open) {
      // Initialize payment data for each distribution
      setPaymentData(prizeDistributions.map(dist => ({
        id: dist.id,
        paymentTxHash: '',
        paymentAddress: dist.user.cryptoAddress || ''
      })));
    }
    setIsOpen(open);
  };

  // Update payment data for a specific distribution
  const updatePaymentData = (id: number, field: keyof PaymentData, value: string) => {
    setPaymentData(prev => 
      prev.map(item => 
        item.id === id ? { ...item, [field]: value } : item
      )
    );
  };

  // Apply common TX hash to all distributions
  const applyCommonTxHash = () => {
    if (useCommonTxHash && commonTxHash) {
      setPaymentData(prev => 
        prev.map(item => ({ ...item, paymentTxHash: commonTxHash }))
      );
    }
  };

  // Toggle use of common TX hash
  const toggleUseCommonTxHash = () => {
    const newValue = !useCommonTxHash;
    setUseCommonTxHash(newValue);
    if (newValue && commonTxHash) {
      applyCommonTxHash();
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    // Validate payment data
    const invalidEntries = paymentData.filter(item => !item.paymentTxHash || !item.paymentAddress);
    if (invalidEntries.length > 0) {
      toast.error('All payment entries must have a transaction hash and payment address');
      return;
    }

    setIsSubmitting(true);
    try {
      await apiService.post(`/api/prize-distributions/challenges/${challengeId}/mark-paid-batch`, {
        payments: paymentData
      });
      toast.success('Batch payment processed successfully');
      onSuccess();
      setIsOpen(false);
    } catch (error: any) {
      console.error('Error processing batch payment:', error);
      toast.error(error.response?.data?.error || 'Failed to process batch payment');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button variant="default" size="sm">
          <DollarSign className="h-4 w-4 mr-2" />
          Process Batch Payment
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>Process Batch Payment</DialogTitle>
          <DialogDescription>
            Mark multiple prize distributions as paid in a single operation.
            Enter payment details for each distribution.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          <div className="flex items-center space-x-4">
            <input
              type="checkbox"
              id="useCommonTxHash"
              checked={useCommonTxHash}
              onChange={toggleUseCommonTxHash}
              className="h-4 w-4 rounded border-forex-border text-forex-primary focus:ring-forex-primary"
            />
            <Label htmlFor="useCommonTxHash">Use same transaction hash for all payments</Label>
            
            {useCommonTxHash && (
              <div className="flex-1 flex items-center space-x-2">
                <Input
                  placeholder="Enter transaction hash"
                  value={commonTxHash}
                  onChange={(e) => setCommonTxHash(e.target.value)}
                />
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={applyCommonTxHash}
                  disabled={!commonTxHash}
                >
                  Apply
                </Button>
              </div>
            )}
          </div>

          <div className="rounded-md border border-forex-border/30 max-h-[400px] overflow-y-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Rank</TableHead>
                  <TableHead>User</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Payment Address</TableHead>
                  <TableHead>Transaction Hash</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paymentData.map((payment, index) => {
                  const distribution = prizeDistributions.find(d => d.id === payment.id);
                  if (!distribution) return null;
                  
                  return (
                    <TableRow key={payment.id}>
                      <TableCell className="font-medium">{distribution.rank}</TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{distribution.user.username}</div>
                          <div className="text-xs text-forex-muted">{distribution.user.email}</div>
                        </div>
                      </TableCell>
                      <TableCell>{formatCurrency(distribution.amount)}</TableCell>
                      <TableCell>
                        <Input
                          placeholder="Payment address"
                          value={payment.paymentAddress || ''}
                          onChange={(e) => updatePaymentData(payment.id, 'paymentAddress', e.target.value)}
                          className="w-full"
                        />
                      </TableCell>
                      <TableCell>
                        <Input
                          placeholder="Transaction hash"
                          value={payment.paymentTxHash}
                          onChange={(e) => updatePaymentData(payment.id, 'paymentTxHash', e.target.value)}
                          className="w-full"
                          disabled={useCommonTxHash}
                        />
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>

          {paymentData.some(p => !p.paymentAddress || !p.paymentTxHash) && (
            <div className="flex items-center text-amber-500 text-sm">
              <AlertCircle className="h-4 w-4 mr-2" />
              All payments must have both a payment address and transaction hash
            </div>
          )}
        </div>

        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={() => setIsOpen(false)}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit}
            disabled={isSubmitting || paymentData.some(p => !p.paymentAddress || !p.paymentTxHash)}
          >
            {isSubmitting ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <DollarSign className="h-4 w-4 mr-2" />
                Process Payments
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default BatchPaymentForm;
