import React, { useState, useRef } from 'react';
import { motion } from 'framer-motion';
import { Trophy, Clock, TrendingUp, AlertTriangle, ChevronRight, ChevronLeft } from 'lucide-react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  Button,
  Badge
} from '@/common/components/ui';
import { useChallengeContext, ChallengeType } from '../context/ChallengeContext';

/**
 * Active challenges component
 *
 * Displays cards for active trading challenges in a horizontal slider.
 *
 * @component
 * @example
 * <ActiveChallenges />
 */
const ActiveChallenges: React.FC = () => {
  const { challenges, selectChallenge } = useChallengeContext();
  const sliderRef = useRef<HTMLDivElement>(null);
  const [currentIndex, setCurrentIndex] = useState(0);

  // Filter active challenges
  const activeChallenges = challenges.filter(c => c.status === 'active');

  // Get challenge type badge color
  const getChallengeTypeColor = (type: ChallengeType): string => {
    switch (type) {
      case 'daily':
        return 'bg-blue-500/20 text-blue-300 border-blue-500/30';
      case 'weekly':
        return 'bg-green-500/20 text-green-300 border-green-500/30';
      case 'monthly':
        return 'bg-purple-500/20 text-purple-300 border-purple-500/30';
      default:
        return 'bg-gray-500/20 text-gray-300 border-gray-500/30';
    }
  };

  // Calculate time remaining
  const getTimeRemaining = (endDate: Date): string => {
    const now = new Date();
    const diffMs = endDate.getTime() - now.getTime();

    if (diffMs <= 0) return 'Ended';

    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

    if (diffDays > 0) {
      return `${diffDays}d ${diffHours}h remaining`;
    } else {
      const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
      return `${diffHours}h ${diffMinutes}m remaining`;
    }
  };

  // Slider navigation
  const handlePrev = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
      scrollToCard(currentIndex - 1);
    }
  };

  const handleNext = () => {
    if (currentIndex < activeChallenges.length - 1) {
      setCurrentIndex(currentIndex + 1);
      scrollToCard(currentIndex + 1);
    }
  };

  const scrollToCard = (index: number) => {
    if (sliderRef.current) {
      const cardWidth = sliderRef.current.scrollWidth / activeChallenges.length;
      sliderRef.current.scrollTo({
        left: cardWidth * index,
        behavior: 'smooth'
      });
    }
  };

  // Animation variants

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3
      }
    }
  };

  return (
    <Card className="border-blue-500/20 bg-[#0f1c2e]/60 backdrop-blur-sm">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg font-medium">Active Challenges</CardTitle>
            <CardDescription className="text-xs text-gray-400">
              Your current trading challenges
            </CardDescription>
          </div>

          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              variant="ghost"
              size="sm"
              className="text-blue-400 hover:text-blue-300 hover:bg-blue-500/10"
            >
              View all
              <ChevronRight size={16} className="ml-1" />
            </Button>
          </motion.div>
        </div>
      </CardHeader>
      <CardContent>
        {activeChallenges.length === 0 ? (
          <div className="text-center py-6 flex flex-col items-center justify-center">
            <Trophy className="h-10 w-10 text-blue-500/20 mx-auto mb-2" />
            <h3 className="text-base font-medium text-white mb-1">No Active Challenges</h3>
            <p className="text-sm text-gray-400 mb-3">
              Join a challenge to start competing and earning rewards.
            </p>
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button className="bg-blue-500/20 text-blue-300 border border-blue-500/30 hover:bg-blue-500/30">
                Browse Challenges
              </Button>
            </motion.div>
          </div>
        ) : (
          <div className="relative">
            {/* Slider navigation buttons */}
            {activeChallenges.length > 1 && (
              <div className="flex justify-between absolute top-1/2 -translate-y-1/2 left-0 right-0 z-10 pointer-events-none">
                <button
                  className={`flex items-center justify-center w-8 h-8 bg-blue-500/30 hover:bg-blue-500/50 text-white rounded-full shadow-md transition-all pointer-events-auto ${currentIndex === 0 ? 'opacity-40 cursor-not-allowed' : 'opacity-90'}`}
                  onClick={handlePrev}
                  disabled={currentIndex === 0}
                >
                  <ChevronLeft size={18} />
                </button>
                <button
                  className={`flex items-center justify-center w-8 h-8 bg-blue-500/30 hover:bg-blue-500/50 text-white rounded-full shadow-md transition-all pointer-events-auto ${currentIndex === activeChallenges.length - 1 ? 'opacity-40 cursor-not-allowed' : 'opacity-90'}`}
                  onClick={handleNext}
                  disabled={currentIndex === activeChallenges.length - 1}
                >
                  <ChevronRight size={18} />
                </button>
              </div>
            )}

            {/* Slider container */}
            <div
              ref={sliderRef}
              className="overflow-x-auto scrollbar-hide snap-x snap-mandatory flex space-x-4 pb-2 px-2 mx-2"
              style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
            >
              {/* Hide scrollbar */}

              {activeChallenges.map((challenge) => (
                <motion.div
                  key={challenge.id}
                  variants={itemVariants}
                  className="snap-center flex-shrink-0"
                  style={{ width: 'calc(100% - 1.5rem)' }}
                  whileHover={{
                    y: -3,
                    transition: { duration: 0.2 }
                  }}
                >
                  <div
                    className="border border-blue-500/20 rounded-lg p-3 hover:bg-blue-500/5 transition-colors duration-200 cursor-pointer group relative"
                    onClick={() => selectChallenge(challenge.id)}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div>
                        <div className="flex items-center">
                          <h3 className="text-sm font-medium text-white mr-2">
                            {challenge.name}
                          </h3>
                          <Badge className={`${getChallengeTypeColor(challenge.type)} border text-xs`}>
                            {challenge.type}
                          </Badge>
                        </div>
                        <div className="flex items-center mt-1">
                          <Clock size={12} className="text-gray-400 mr-1" />
                          <span className="text-xs text-gray-400">
                            {getTimeRemaining(challenge.endDate)}
                          </span>
                        </div>
                      </div>

                      <div className="text-right">
                        <div className="flex items-center justify-end">
                          <Trophy size={12} className="text-amber-400 mr-1" />
                          <span className="text-xs text-gray-300">
                            Rank {challenge.currentRank} of {challenge.totalParticipants}
                          </span>
                        </div>
                        <div className="flex items-center justify-end mt-1">
                          <TrendingUp size={12} className="text-green-400 mr-1" />
                          <span className="text-xs text-green-400">
                            +{challenge.metrics?.pnlPercent.toFixed(2)}%
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between text-xs mt-2">
                      <div className="flex items-center">
                        <AlertTriangle size={10} className="text-amber-400 mr-1" />
                        <span className="text-gray-300">
                          Max Drawdown: {challenge.metrics?.drawdownPercent.toFixed(2)}%
                        </span>
                      </div>
                      <span className="text-gray-400">
                        Limit: 5.00%
                      </span>
                    </div>

                    {/* Animated border on hover */}
                    <div className="absolute inset-0 rounded-lg border border-blue-500/0 group-hover:border-blue-500/30 pointer-events-none transition-all duration-300"></div>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Pagination dots */}
            {activeChallenges.length > 1 && (
              <div className="flex justify-center mt-4 mb-1 space-x-2">
                {activeChallenges.map((_, index) => (
                  <button
                    key={index}
                    className={`h-2 rounded-full transition-all duration-300 ${
                      index === currentIndex ? 'w-6 bg-blue-400' : 'w-2 bg-blue-500/30 hover:bg-blue-500/50'
                    }`}
                    onClick={() => {
                      setCurrentIndex(index);
                      scrollToCard(index);
                    }}
                    aria-label={`Go to slide ${index + 1}`}
                  />
                ))}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ActiveChallenges;
