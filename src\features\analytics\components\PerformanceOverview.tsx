import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  TrendingUp, TrendingDown, BarChart2, AlertTriangle, ChevronUp, ChevronDown,
  Percent, Clock, Award, Target, BarChart, Scale, Sigma, Waves,
  ArrowUpDown, Gauge, Sparkles, RefreshCw
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/common/components/ui/card';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/common/components/ui/tabs';
import { useAnalyticsContext } from '../context/AnalyticsContext';
import InfoButton from './InfoButton';
import { Progress } from '@/common/components/ui/progress';
import RealTimeIndicator from './RealTimeIndicator';
import { Button } from '@/common/components/ui/button';

interface MetricCardProps {
  title: string;
  value: string | number;
  description: string;
  icon: React.ElementType;
  color: string;
  trend?: 'up' | 'down' | 'neutral';
  trendValue?: string;
  infoText?: string;
  progressValue?: number;
  progressMax?: number;
  progressColor?: string;
  benchmark?: string;
  rating?: 'excellent' | 'good' | 'average' | 'poor';
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  description,
  icon: Icon,
  color,
  trend,
  trendValue,
  infoText,
  progressValue,
  progressMax = 100,
  progressColor,
  benchmark,
  rating
}) => {
  // Get rating color and icon
  const getRatingDetails = () => {
    switch (rating) {
      case 'excellent':
        return { color: 'text-green-400', icon: <Sparkles className="h-3 w-3 text-green-400" /> };
      case 'good':
        return { color: 'text-blue-400', icon: <ChevronUp className="h-3 w-3 text-blue-400" /> };
      case 'average':
        return { color: 'text-yellow-400', icon: <ArrowUpDown className="h-3 w-3 text-yellow-400" /> };
      case 'poor':
        return { color: 'text-red-400', icon: <ChevronDown className="h-3 w-3 text-red-400" /> };
      default:
        return { color: '', icon: null };
    }
  };

  const ratingDetails = getRatingDetails();

  return (
    <Card className="border-gray-800 bg-gray-900/60 backdrop-blur-sm overflow-hidden group hover:shadow-lg transition-all duration-300">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium flex items-center justify-between">
          <div className="flex items-center gap-1.5">
            {title}
            {infoText && <InfoButton content={infoText} className="ml-1" />}
          </div>
          <Icon className={`${color} h-4 w-4`} />
        </CardTitle>
      </CardHeader>
      <CardContent className="relative">
        <div className="flex items-center gap-2">
          <div className="text-2xl font-bold">{value}</div>
          {rating && (
            <div className={`text-xs ${ratingDetails.color} flex items-center gap-0.5 bg-gray-800/50 px-1.5 py-0.5 rounded-full`}>
              {ratingDetails.icon}
              <span className="capitalize">{rating}</span>
            </div>
          )}
        </div>

        <p className="text-xs text-gray-400 mt-1">{description}</p>

        {progressValue !== undefined && (
          <div className="mt-3 space-y-1">
            <Progress
              value={progressValue}
              max={progressMax}
              className="h-1.5 bg-gray-700"
              indicatorClassName={progressColor || color.replace('text-', 'bg-')}
            />
            {benchmark && (
              <div className="text-xs text-gray-500 flex justify-between">
                <span>0</span>
                <span>{benchmark}</span>
                <span>{progressMax}</span>
              </div>
            )}
          </div>
        )}

        {trend && trendValue && (
          <div className="flex items-center mt-2 text-xs">
            {trend === 'up' ? (
              <div className="flex items-center text-green-400">
                <ChevronUp size={14} />
                <span>{trendValue}</span>
              </div>
            ) : trend === 'down' ? (
              <div className="flex items-center text-red-400">
                <ChevronDown size={14} />
                <span>{trendValue}</span>
              </div>
            ) : null}
          </div>
        )}

        {/* Animated background effect */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
      </CardContent>
    </Card>
  );
};

/**
 * Performance overview component
 *
 * Displays key trading metrics with animations and visual styling.
 * Organized into tabs for different metric categories.
 * Includes real-time update indicators and refresh functionality.
 *
 * @component
 * @example
 * <PerformanceOverview />
 */
const PerformanceOverview: React.FC = () => {
  const {
    metrics,
    trades,
    isConnected,
    lastUpdate,
    updateCounts,
    refreshData
  } = useAnalyticsContext();
  const [activeTab, setActiveTab] = useState<string>("performance");
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);

  // Handle refresh
  const handleRefresh = () => {
    setIsRefreshing(true);
    refreshData();

    // Reset refreshing state after animation
    setTimeout(() => {
      setIsRefreshing(false);
    }, 1000);
  };

  // Format time since last update
  const getTimeSinceLastUpdate = () => {
    const update = lastUpdate.metrics;
    if (!update) return 'No updates yet';

    const now = new Date();
    const diffMs = now.getTime() - update.getTime();

    if (diffMs < 1000) return 'Just now';
    if (diffMs < 60000) return `${Math.floor(diffMs / 1000)}s ago`;
    if (diffMs < 3600000) return `${Math.floor(diffMs / 60000)}m ago`;

    return `${Math.floor(diffMs / 3600000)}h ago`;
  };

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  // Default metrics if no data available
  const displayMetrics = {
    // Performance metrics
    winRate: metrics?.winRate || 0,
    pnlPercent: metrics?.pnlPercent || 0,
    totalPnl: metrics?.totalPnl || 0,
    totalTrades: metrics?.totalTrades || trades.length || 0,
    profitFactor: metrics?.profitFactor || 0,
    maxDrawdown: metrics?.maxDrawdown || 0,
    dailyDrawdown: metrics?.dailyDrawdown || 0,
    averageDuration: metrics?.averageDuration || 0,

    // Risk metrics
    expectancy: metrics?.expectancy || 0,
    riskRewardRatio: metrics?.averageRiskRewardRatio || 0,
    maxRiskPerTrade: metrics?.maxRiskPerTrade || 0,
    averageRiskPerTrade: metrics?.averageRiskPerTrade || 0,

    // Trade metrics
    averageWin: metrics?.averageWin || 0,
    averageLoss: metrics?.averageLoss || 0,
    largestWin: metrics?.largestWin || 0,
    largestLoss: metrics?.largestLoss || 0,
    maxConsecutiveWins: metrics?.maxConsecutiveWins || 0,
    maxConsecutiveLosses: metrics?.maxConsecutiveLosses || 0,

    // Advanced metrics
    sharpeRatio: metrics?.sharpeRatio || 0,
    sortinoRatio: metrics?.sortinoRatio || 0,
    tradingDays: metrics?.tradingDays || 0,
    averageTradesPerDay: metrics?.averageTradesPerDay || 0
  };

  // Format duration in minutes and seconds
  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  };

  // Get rating based on metric value
  const getRating = (value: number, thresholds: { excellent: number, good: number, average: number }) => {
    if (value >= thresholds.excellent) return 'excellent';
    if (value >= thresholds.good) return 'good';
    if (value >= thresholds.average) return 'average';
    return 'poor';
  };

  // Get inverse rating (where lower is better)
  const getInverseRating = (value: number, thresholds: { excellent: number, good: number, average: number }) => {
    if (value <= thresholds.excellent) return 'excellent';
    if (value <= thresholds.good) return 'good';
    if (value <= thresholds.average) return 'average';
    return 'poor';
  };

  // Performance metrics cards
  const performanceCards = [
    {
      title: 'Win Rate',
      value: `${displayMetrics.winRate.toFixed(1)}%`,
      description: 'Percentage of winning trades',
      icon: Award,
      color: 'text-blue-400',
      trend: displayMetrics.winRate > 50 ? 'up' : 'down',
      trendValue: `${Math.abs(displayMetrics.winRate - 50).toFixed(1)}% vs 50%`,
      infoText: 'Win Rate is the percentage of trades that result in profit. A higher win rate indicates more consistent profitability.',
      progressValue: displayMetrics.winRate,
      progressMax: 100,
      benchmark: '50%',
      rating: getRating(displayMetrics.winRate, { excellent: 65, good: 55, average: 45 })
    },
    {
      title: 'P&L',
      value: `${displayMetrics.pnlPercent > 0 ? '+' : ''}${displayMetrics.pnlPercent.toFixed(2)}%`,
      description: `${formatCurrency(displayMetrics.totalPnl)}`,
      icon: TrendingUp,
      color: displayMetrics.pnlPercent >= 0 ? 'text-green-400' : 'text-red-400',
      trend: displayMetrics.pnlPercent >= 0 ? 'up' : 'down',
      trendValue: `${Math.abs(displayMetrics.pnlPercent).toFixed(2)}%`,
      infoText: 'Profit and Loss (P&L) shows your total returns as a percentage of your initial balance.',
      rating: displayMetrics.pnlPercent >= 10 ? 'excellent' :
              displayMetrics.pnlPercent >= 5 ? 'good' :
              displayMetrics.pnlPercent >= 0 ? 'average' : 'poor'
    },
    {
      title: 'Profit Factor',
      value: displayMetrics.profitFactor.toFixed(2),
      description: 'Gross profit / gross loss',
      icon: Scale,
      color: 'text-yellow-400',
      trend: displayMetrics.profitFactor > 1 ? 'up' : 'down',
      trendValue: `${Math.abs(displayMetrics.profitFactor - 1).toFixed(2)} vs 1.0`,
      infoText: 'Profit Factor is the ratio of gross profit to gross loss. A value above 1.0 indicates overall profitability.',
      progressValue: Math.min(displayMetrics.profitFactor * 25, 100),
      progressMax: 100,
      benchmark: '1.0',
      rating: getRating(displayMetrics.profitFactor, { excellent: 2.5, good: 1.75, average: 1.25 })
    },
    {
      title: 'Max Drawdown',
      value: `${displayMetrics.maxDrawdown.toFixed(2)}%`,
      description: 'Maximum percentage drawdown',
      icon: AlertTriangle,
      color: displayMetrics.maxDrawdown < 10 ? 'text-green-400' : 'text-red-400',
      infoText: 'Maximum Drawdown is the largest percentage drop from a peak to a trough in your account balance. Lower values indicate better risk management.',
      progressValue: displayMetrics.maxDrawdown,
      progressMax: 20,
      progressColor: 'bg-red-400',
      benchmark: '10%',
      rating: getInverseRating(displayMetrics.maxDrawdown, { excellent: 5, good: 10, average: 15 })
    }
  ];

  // Risk metrics cards
  const riskCards = [
    {
      title: 'Expectancy',
      value: formatCurrency(displayMetrics.expectancy),
      description: 'Expected profit per trade',
      icon: Target,
      color: displayMetrics.expectancy >= 0 ? 'text-green-400' : 'text-red-400',
      trend: displayMetrics.expectancy >= 0 ? 'up' : 'down',
      trendValue: formatCurrency(Math.abs(displayMetrics.expectancy)),
      infoText: 'Expectancy is the average amount you can expect to win (or lose) per trade. It combines win rate with average win/loss size.',
      rating: getRating(displayMetrics.expectancy, { excellent: 50, good: 20, average: 0 })
    },
    {
      title: 'Risk/Reward',
      value: displayMetrics.riskRewardRatio.toFixed(2),
      description: 'Average risk to reward ratio',
      icon: ArrowUpDown,
      color: displayMetrics.riskRewardRatio >= 1 ? 'text-green-400' : 'text-yellow-400',
      infoText: 'Risk/Reward Ratio compares the potential profit to potential loss on your trades. Higher values indicate you\'re risking less to gain more.',
      progressValue: Math.min(displayMetrics.riskRewardRatio * 33.3, 100),
      progressMax: 100,
      benchmark: '1.0',
      rating: getRating(displayMetrics.riskRewardRatio, { excellent: 2, good: 1.5, average: 1 })
    },
    {
      title: 'Max Risk/Trade',
      value: `${displayMetrics.maxRiskPerTrade.toFixed(2)}%`,
      description: 'Maximum risk per trade',
      icon: Gauge,
      color: displayMetrics.maxRiskPerTrade <= 2 ? 'text-green-400' : 'text-amber-400',
      infoText: 'Maximum Risk per Trade is the highest percentage of your account that you risked on a single trade.',
      progressValue: displayMetrics.maxRiskPerTrade,
      progressMax: 5,
      progressColor: 'bg-amber-400',
      benchmark: '2%',
      rating: getInverseRating(displayMetrics.maxRiskPerTrade, { excellent: 1, good: 2, average: 3 })
    },
    {
      title: 'Avg Risk/Trade',
      value: `${displayMetrics.averageRiskPerTrade.toFixed(2)}%`,
      description: 'Average risk per trade',
      icon: AlertTriangle,
      color: displayMetrics.averageRiskPerTrade <= 1 ? 'text-green-400' : 'text-amber-400',
      infoText: 'Average Risk per Trade is the typical percentage of your account that you risk on each trade.',
      progressValue: displayMetrics.averageRiskPerTrade,
      progressMax: 3,
      progressColor: 'bg-amber-400',
      benchmark: '1%',
      rating: getInverseRating(displayMetrics.averageRiskPerTrade, { excellent: 0.5, good: 1, average: 2 })
    }
  ];

  // Trade metrics cards
  const tradeCards = [
    {
      title: 'Trades',
      value: displayMetrics.totalTrades,
      description: 'Total number of trades',
      icon: BarChart2,
      color: 'text-purple-400',
      infoText: 'The total number of completed trades in the selected time period or challenge.'
    },
    {
      title: 'Avg Win',
      value: formatCurrency(displayMetrics.averageWin),
      description: 'Average winning trade',
      icon: TrendingUp,
      color: 'text-green-400',
      infoText: 'The average profit from your winning trades.'
    },
    {
      title: 'Avg Loss',
      value: formatCurrency(Math.abs(displayMetrics.averageLoss)),
      description: 'Average losing trade',
      icon: TrendingDown,
      color: 'text-red-400',
      infoText: 'The average loss from your losing trades.'
    },
    {
      title: 'Avg Duration',
      value: formatDuration(displayMetrics.averageDuration),
      description: 'Average trade duration',
      icon: Clock,
      color: 'text-blue-400',
      infoText: 'Average Duration shows how long your trades typically remain open. This can help identify your trading style (scalping, day trading, swing trading).'
    }
  ];

  // Advanced metrics cards
  const advancedCards = [
    {
      title: 'Sharpe Ratio',
      value: displayMetrics.sharpeRatio.toFixed(2),
      description: 'Risk-adjusted return',
      icon: Sigma,
      color: 'text-indigo-400',
      infoText: 'Sharpe Ratio measures the performance of your trading strategy compared to a risk-free investment, adjusted for risk.',
      progressValue: Math.min(displayMetrics.sharpeRatio * 33.3, 100),
      progressMax: 100,
      benchmark: '1.0',
      rating: getRating(displayMetrics.sharpeRatio, { excellent: 2, good: 1, average: 0.5 })
    },
    {
      title: 'Sortino Ratio',
      value: displayMetrics.sortinoRatio.toFixed(2),
      description: 'Downside risk-adjusted return',
      icon: Waves,
      color: 'text-purple-400',
      infoText: 'Sortino Ratio is similar to Sharpe Ratio but only considers downside risk, making it more relevant for trading strategies.',
      progressValue: Math.min(displayMetrics.sortinoRatio * 33.3, 100),
      progressMax: 100,
      benchmark: '1.0',
      rating: getRating(displayMetrics.sortinoRatio, { excellent: 2, good: 1, average: 0.5 })
    },
    {
      title: 'Consecutive Wins',
      value: displayMetrics.maxConsecutiveWins,
      description: 'Longest winning streak',
      icon: Award,
      color: 'text-green-400',
      infoText: 'The maximum number of consecutive winning trades you\'ve achieved.'
    },
    {
      title: 'Consecutive Losses',
      value: displayMetrics.maxConsecutiveLosses,
      description: 'Longest losing streak',
      icon: AlertTriangle,
      color: 'text-red-400',
      infoText: 'The maximum number of consecutive losing trades you\'ve experienced.',
      rating: getInverseRating(displayMetrics.maxConsecutiveLosses, { excellent: 2, good: 4, average: 6 })
    }
  ];

  // Animation variants for staggered entrance
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { y: 20, opacity: 0 },
    show: { y: 0, opacity: 1 }
  };

  return (
    <Card className="border-gray-800 bg-gray-900/60 backdrop-blur-sm overflow-hidden relative">
      {/* Real-time indicator */}
      <RealTimeIndicator dataType="metrics" position="top-right" />

      <CardHeader className="pb-2">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <BarChart className="h-5 w-5 text-blue-400" />
            <div className="flex items-center gap-1.5">
              Performance Metrics
              <InfoButton
                content="Comprehensive analysis of your trading performance across multiple dimensions. These metrics help you understand your strengths and areas for improvement."
                className="ml-1"
              />
            </div>
          </div>

          <div className="flex items-center gap-3">
            <div className="text-xs text-gray-400 flex items-center gap-1">
              <span>Updated:</span>
              <span className={lastUpdate.metrics ? 'text-gray-300' : 'text-gray-500'}>
                {getTimeSinceLastUpdate()}
              </span>
            </div>

            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              <AnimatePresence mode="wait">
                {isRefreshing ? (
                  <motion.div
                    key="refreshing"
                    initial={{ opacity: 0, rotate: 0 }}
                    animate={{ opacity: 1, rotate: 360 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.5 }}
                  >
                    <RefreshCw className="h-4 w-4 text-blue-400" />
                  </motion.div>
                ) : (
                  <motion.div
                    key="refresh"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                  >
                    <RefreshCw className="h-4 w-4 text-gray-400 hover:text-white" />
                  </motion.div>
                )}
              </AnimatePresence>
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="performance" className="w-full" onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-4 mb-4">
            <TabsTrigger value="performance" className="text-xs">Performance</TabsTrigger>
            <TabsTrigger value="risk" className="text-xs">Risk Analysis</TabsTrigger>
            <TabsTrigger value="trades" className="text-xs">Trade Metrics</TabsTrigger>
            <TabsTrigger value="advanced" className="text-xs">Advanced</TabsTrigger>
          </TabsList>

          <TabsContent value="performance">
            <motion.div
              className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4"
              variants={container}
              initial="hidden"
              animate="show"
              key="performance"
            >
              {performanceCards.map((card, index) => (
                <motion.div key={index} variants={item}>
                  <MetricCard {...card} />
                </motion.div>
              ))}
            </motion.div>
          </TabsContent>

          <TabsContent value="risk">
            <motion.div
              className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4"
              variants={container}
              initial="hidden"
              animate="show"
              key="risk"
            >
              {riskCards.map((card, index) => (
                <motion.div key={index} variants={item}>
                  <MetricCard {...card} />
                </motion.div>
              ))}
            </motion.div>
          </TabsContent>

          <TabsContent value="trades">
            <motion.div
              className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4"
              variants={container}
              initial="hidden"
              animate="show"
              key="trades"
            >
              {tradeCards.map((card, index) => (
                <motion.div key={index} variants={item}>
                  <MetricCard {...card} />
                </motion.div>
              ))}
            </motion.div>
          </TabsContent>

          <TabsContent value="advanced">
            <motion.div
              className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4"
              variants={container}
              initial="hidden"
              animate="show"
              key="advanced"
            >
              {advancedCards.map((card, index) => (
                <motion.div key={index} variants={item}>
                  <MetricCard {...card} />
                </motion.div>
              ))}
            </motion.div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default PerformanceOverview;
