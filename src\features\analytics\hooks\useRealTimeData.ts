import { useState, useEffect, useCallback } from 'react';
import { socketService } from '@/common/services/socketService';
import { socketCacheService } from '@/common/services/socketCacheService';
import { SocketEvent } from '@/types/socketEvents';
import { TradeData, UserMetrics, DrawdownUpdate } from '../context/AnalyticsContext';

/**
 * Options for the useRealTimeData hook
 */
interface UseRealTimeDataOptions {
  /**
   * Challenge entry ID to filter data
   */
  challengeEntryId?: number;

  /**
   * cTrader account ID to filter data
   */
  ctraderAccountId?: string;

  /**
   * Whether to use cache
   */
  useCache?: boolean;

  /**
   * Cache TTL in milliseconds
   */
  cacheTTL?: number;

  /**
   * Whether to initialize with cached data
   */
  initWithCache?: boolean;
}

/**
 * Real-time data hook result
 */
interface UseRealTimeDataResult {
  /**
   * Trade data
   */
  trades: TradeData[];

  /**
   * User metrics
   */
  metrics: UserMetrics | null;

  /**
   * Drawdown data
   */
  drawdown: DrawdownUpdate | null;

  /**
   * Whether the socket is connected
   */
  isConnected: boolean;

  /**
   * Whether data is loading
   */
  isLoading: boolean;

  /**
   * Error message
   */
  error: string | null;

  /**
   * Reconnect function
   */
  reconnect: () => void;

  /**
   * Last update timestamp
   */
  lastUpdate: Record<string, Date | null>;

  /**
   * Update counts
   */
  updateCounts: Record<string, number>;

  /**
   * Refresh data function
   */
  refreshData: () => void;
}

/**
 * Hook for real-time data with caching
 *
 * @param options - Hook options
 * @returns Real-time data and status
 */
export const useRealTimeData = (options: UseRealTimeDataOptions = {}): UseRealTimeDataResult => {
  const {
    challengeEntryId,
    ctraderAccountId,
    useCache = true,
    cacheTTL = 60 * 1000, // 1 minute default
    initWithCache = true
  } = options;

  // Initialize cache service if using cache
  useEffect(() => {
    if (useCache) {
      socketCacheService.init();
    }
  }, [useCache]);

  // State
  const [trades, setTrades] = useState<TradeData[]>(() => {
    if (useCache && initWithCache) {
      return socketCacheService.get<TradeData[]>('trades') || [];
    }
    return [];
  });

  const [metrics, setMetrics] = useState<UserMetrics | null>(() => {
    if (useCache && initWithCache) {
      return socketCacheService.get<UserMetrics>('metrics');
    }
    return null;
  });

  const [drawdown, setDrawdown] = useState<DrawdownUpdate | null>(() => {
    if (useCache && initWithCache) {
      return socketCacheService.get<DrawdownUpdate>('drawdown');
    }
    return null;
  });

  const [isConnected, setIsConnected] = useState<boolean>(socketService.isConnected());
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Record<string, Date | null>>({
    trades: null,
    metrics: null,
    drawdown: null
  });
  const [updateCounts, setUpdateCounts] = useState<Record<string, number>>({
    trades: 0,
    metrics: 0,
    drawdown: 0
  });

  // Handle connection status changes
  useEffect(() => {
    const handleConnect = () => setIsConnected(true);
    const handleDisconnect = () => setIsConnected(false);

    socketService.on(SocketEvent.CONNECT, handleConnect);
    socketService.on(SocketEvent.DISCONNECT, handleDisconnect);

    // Set initial connection status
    setIsConnected(socketService.isConnected());

    return () => {
      socketService.off(SocketEvent.CONNECT, handleConnect);
      socketService.off(SocketEvent.DISCONNECT, handleDisconnect);
    };
  }, []);

  // Handle trade events
  useEffect(() => {
    const handleTradeCreated = (data: TradeData) => {
      // Apply filters
      if (challengeEntryId && data.challengeEntryId !== challengeEntryId) return;

      // If filtering by cTrader account ID, we need to check if this trade belongs to that account
      // This would require additional data in the trade object or a lookup mechanism
      // For now, we'll assume the trade data includes a ctraderAccountId field
      if (ctraderAccountId && data.ctraderAccountId !== ctraderAccountId) return;

      setTrades(prev => [data, ...prev]);
      setLastUpdate(prev => ({ ...prev, trades: new Date() }));
      setUpdateCounts(prev => ({ ...prev, trades: prev.trades + 1 }));

      // Cache data if using cache
      if (useCache) {
        socketCacheService.updateCache('trades', (trades) => [data, ...(trades || [])], cacheTTL);
      }
    };

    const handleTradeUpdated = (data: TradeData) => {
      // Apply filters
      if (challengeEntryId && data.challengeEntryId !== challengeEntryId) return;

      // If filtering by cTrader account ID, we need to check if this trade belongs to that account
      if (ctraderAccountId && data.ctraderAccountId !== ctraderAccountId) return;

      setTrades(prev => prev.map(trade => trade.id === data.id ? data : trade));
      setLastUpdate(prev => ({ ...prev, trades: new Date() }));
      setUpdateCounts(prev => ({ ...prev, trades: prev.trades + 1 }));

      // Cache data if using cache
      if (useCache) {
        socketCacheService.updateCache('trades', (trades) => {
          if (!trades) return [data];
          return trades.map(trade => trade.id === data.id ? data : trade);
        }, cacheTTL);
      }
    };

    socketService.on(SocketEvent.TRADE_CREATED, handleTradeCreated);
    socketService.on(SocketEvent.TRADE_UPDATED, handleTradeUpdated);

    return () => {
      socketService.off(SocketEvent.TRADE_CREATED, handleTradeCreated);
      socketService.off(SocketEvent.TRADE_UPDATED, handleTradeUpdated);
    };
  }, [challengeEntryId, ctraderAccountId, useCache, cacheTTL]);

  // Handle metrics updates
  useEffect(() => {
    const handleMetricsUpdated = (data: UserMetrics) => {
      setMetrics(data);
      setLastUpdate(prev => ({ ...prev, metrics: new Date() }));
      setUpdateCounts(prev => ({ ...prev, metrics: prev.metrics + 1 }));

      // Cache data if using cache
      if (useCache) {
        socketCacheService.set('metrics', data, cacheTTL);
      }
    };

    socketService.on(SocketEvent.USER_METRICS_UPDATED, handleMetricsUpdated);

    return () => {
      socketService.off(SocketEvent.USER_METRICS_UPDATED, handleMetricsUpdated);
    };
  }, [useCache, cacheTTL]);

  // Handle drawdown updates
  useEffect(() => {
    const handleDrawdownUpdate = (data: DrawdownUpdate) => {
      // Apply filters
      if (challengeEntryId && data.challengeEntryId !== challengeEntryId) return;

      // If filtering by cTrader account ID, we need to check if this drawdown update belongs to that account
      // This would require additional data in the drawdown object or a lookup mechanism
      // For now, we'll assume the drawdown data includes a ctraderAccountId field or we can look it up
      if (ctraderAccountId) {
        // In a real implementation, you would check if this drawdown update is for the selected cTrader account
        // For now, we'll just pass it through if no challengeEntryId filter is applied
        if (challengeEntryId) return;
      }

      setDrawdown(data);
      setLastUpdate(prev => ({ ...prev, drawdown: new Date() }));
      setUpdateCounts(prev => ({ ...prev, drawdown: prev.drawdown + 1 }));

      // Cache data if using cache
      if (useCache) {
        socketCacheService.set('drawdown', data, cacheTTL);
      }
    };

    socketService.on(SocketEvent.REAL_TIME_DRAWDOWN_UPDATE, handleDrawdownUpdate);

    return () => {
      socketService.off(SocketEvent.REAL_TIME_DRAWDOWN_UPDATE, handleDrawdownUpdate);
    };
  }, [challengeEntryId, ctraderAccountId, useCache, cacheTTL]);

  // Set loading state to false after initial setup
  useEffect(() => {
    setIsLoading(false);
  }, []);

  // Reconnect function
  const reconnect = useCallback(() => {
    try {
      socketService.reconnect();
    } catch (err) {
      setError('Failed to reconnect to WebSocket server');
    }
  }, []);

  // Refresh data function
  const refreshData = useCallback(() => {
    // Clear cache if using cache
    if (useCache) {
      socketCacheService.clear('trades');
      socketCacheService.clear('metrics');
      socketCacheService.clear('drawdown');
    }

    // Set loading state
    setIsLoading(true);

    // TODO: Fetch fresh data from API
    // For now, just set loading to false after a short delay
    setTimeout(() => {
      setIsLoading(false);
    }, 500);
  }, [useCache]);

  return {
    trades,
    metrics,
    drawdown,
    isConnected,
    isLoading,
    error,
    reconnect,
    lastUpdate,
    updateCounts,
    refreshData
  };
};
