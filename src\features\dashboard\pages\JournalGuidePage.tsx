import React from 'react';
import { <PERSON> } from 'react-router-dom';
import {
  ArrowLeft,
  BookOpen,
  FileText,
  Lightbulb,
  CheckCircle,
  Tag,
  Pencil,
  Bookmark,
  Star,
  ChevronRight,
  X
} from 'lucide-react';
import { Button } from '@/common/components/ui';
import { DashboardLayout } from '@/features/dashboard';

/**
 * Journal Guide Page Component
 *
 * A comprehensive guide on how to use the trading journal effectively
 *
 * @component
 * @version 1.0.0
 */
const JournalGuidePage: React.FC = () => {
  return (
    <DashboardLayout>
      <div className="min-h-[calc(100vh-64px)] bg-[#191919] text-gray-200 overflow-y-auto">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-600 py-12 px-6 relative">
          {/* Background pattern */}
          <div className="absolute inset-0 opacity-10">
            <svg className="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
              <path d="M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z" fill="currentColor" fillRule="evenodd"/>
            </svg>
          </div>

          <div className="max-w-5xl mx-auto relative z-10">
            <Link to="/dashboard/journal" className="inline-flex items-center text-blue-100 hover:text-white mb-6 transition-colors">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Journal
            </Link>

            <h1 className="text-4xl font-bold text-white mb-4">Master the Art of Trading Journaling</h1>
            <p className="text-xl text-blue-100 max-w-3xl">Learn how to use your trading journal effectively to improve performance, track progress, and develop winning strategies.</p>
          </div>
        </div>

        {/* Content */}
        <div className="max-w-5xl mx-auto px-6 py-12">
          {/* Navigation */}
          <div className="flex flex-wrap gap-4 mb-12">
            <a href="#why-journal" className="px-4 py-2 bg-[#202020] rounded-md hover:bg-[#2e2e2e] transition-colors">Why Journal?</a>
            <a href="#getting-started" className="px-4 py-2 bg-[#202020] rounded-md hover:bg-[#2e2e2e] transition-colors">Getting Started</a>
            <a href="#journal-features" className="px-4 py-2 bg-[#202020] rounded-md hover:bg-[#2e2e2e] transition-colors">Journal Features</a>
            <a href="#templates" className="px-4 py-2 bg-[#202020] rounded-md hover:bg-[#2e2e2e] transition-colors">Templates</a>
            <a href="#best-practices" className="px-4 py-2 bg-[#202020] rounded-md hover:bg-[#2e2e2e] transition-colors">Best Practices</a>
            <a href="#examples" className="px-4 py-2 bg-[#202020] rounded-md hover:bg-[#2e2e2e] transition-colors">Examples</a>
          </div>

          {/* Why Journal Section */}
          <section id="why-journal" className="mb-16">
            <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
              <Lightbulb className="h-6 w-6 mr-3 text-blue-400" />
              Why Keep a Trading Journal?
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
              <div className="bg-[#202020] p-6 rounded-lg border border-[#313131]">
                <h3 className="text-xl font-medium text-white mb-3">Identify Patterns</h3>
                <p className="text-gray-300">A trading journal helps you identify patterns in your trading behavior, both successful and unsuccessful. By documenting your trades and the reasoning behind them, you can spot recurring themes.</p>
              </div>

              <div className="bg-[#202020] p-6 rounded-lg border border-[#313131]">
                <h3 className="text-xl font-medium text-white mb-3">Improve Decision Making</h3>
                <p className="text-gray-300">Reviewing your past decisions helps you understand your thought process and emotional state during trades, leading to more objective and disciplined trading decisions.</p>
              </div>

              <div className="bg-[#202020] p-6 rounded-lg border border-[#313131]">
                <h3 className="text-xl font-medium text-white mb-3">Track Progress</h3>
                <p className="text-gray-300">A journal provides a clear record of your trading journey, allowing you to see how your skills and strategies have evolved over time and measure your improvement.</p>
              </div>

              <div className="bg-[#202020] p-6 rounded-lg border border-[#313131]">
                <h3 className="text-xl font-medium text-white mb-3">Refine Strategies</h3>
                <p className="text-gray-300">By analyzing your successful and unsuccessful trades, you can refine your trading strategies, focusing on what works and eliminating what doesn't.</p>
              </div>
            </div>

            <div className="bg-blue-600/10 border border-blue-600/20 rounded-lg p-6">
              <h4 className="text-lg font-medium text-blue-300 mb-2">Did You Know?</h4>
              <p className="text-gray-300">Studies show that traders who maintain detailed journals consistently outperform those who don't. The act of reflection and analysis is a key differentiator between amateur and professional traders.</p>
            </div>
          </section>

          {/* Getting Started Section */}
          <section id="getting-started" className="mb-16">
            <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
              <BookOpen className="h-6 w-6 mr-3 text-blue-400" />
              Getting Started with Your Journal
            </h2>

            <div className="space-y-6 mb-8">
              <div className="flex items-start">
                <div className="bg-blue-600 rounded-full p-2 mr-4 mt-1">
                  <span className="text-white font-bold">1</span>
                </div>
                <div>
                  <h3 className="text-xl font-medium text-white mb-2">Create Your First Entry</h3>
                  <p className="text-gray-300 mb-3">Start by clicking the "New Entry" button in your journal dashboard. Give your entry a meaningful title that summarizes the main focus or lesson.</p>
                  <div className="bg-[#202020] p-4 rounded-md border border-[#313131]">
                    <p className="text-sm text-gray-400">Example title: "EUR/USD Breakout Trade Analysis - May 15"</p>
                  </div>
                </div>
              </div>

              <div className="flex items-start">
                <div className="bg-blue-600 rounded-full p-2 mr-4 mt-1">
                  <span className="text-white font-bold">2</span>
                </div>
                <div>
                  <h3 className="text-xl font-medium text-white mb-2">Structure Your Content</h3>
                  <p className="text-gray-300 mb-3">Use headings, lists, and other formatting options to organize your thoughts. A well-structured entry makes it easier to review later.</p>
                  <div className="bg-[#202020] p-4 rounded-md border border-[#313131]">
                    <p className="text-sm text-gray-400">Use the slash (/) command to access formatting options like headings, lists, and more.</p>
                  </div>
                </div>
              </div>

              <div className="flex items-start">
                <div className="bg-blue-600 rounded-full p-2 mr-4 mt-1">
                  <span className="text-white font-bold">3</span>
                </div>
                <div>
                  <h3 className="text-xl font-medium text-white mb-2">Add Tags and Properties</h3>
                  <p className="text-gray-300 mb-3">Use tags to categorize your entries and make them easier to find later. Add properties like mood to track your emotional state during trading.</p>
                  <div className="bg-[#202020] p-4 rounded-md border border-[#313131]">
                    <p className="text-sm text-gray-400">Example tags: #breakout, #forex, #successful, #risk-management</p>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Journal Features Section */}
          <section id="journal-features" className="mb-16">
            <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
              <FileText className="h-6 w-6 mr-3 text-blue-400" />
              Journal Features
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="bg-[#202020] p-6 rounded-lg border border-[#313131]">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 rounded-full bg-blue-600/20 flex items-center justify-center mr-3">
                    <Tag className="h-5 w-5 text-blue-400" />
                  </div>
                  <h3 className="text-lg font-medium text-white">Tags</h3>
                </div>
                <p className="text-gray-300 mb-3">Categorize your entries with tags to easily filter and find related content later.</p>
                <p className="text-sm text-gray-400">Use tags like #strategy, #mistake, #success to organize your journal.</p>
              </div>

              <div className="bg-[#202020] p-6 rounded-lg border border-[#313131]">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 rounded-full bg-blue-600/20 flex items-center justify-center mr-3">
                    <Star className="h-5 w-5 text-blue-400" />
                  </div>
                  <h3 className="text-lg font-medium text-white">Favorites</h3>
                </div>
                <p className="text-gray-300 mb-3">Mark important entries as favorites for quick access to key insights and strategies.</p>
                <p className="text-sm text-gray-400">Star entries that contain breakthrough insights or important lessons.</p>
              </div>

              <div className="bg-[#202020] p-6 rounded-lg border border-[#313131]">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 rounded-full bg-blue-600/20 flex items-center justify-center mr-3">
                    <Pencil className="h-5 w-5 text-blue-400" />
                  </div>
                  <h3 className="text-lg font-medium text-white">Rich Editor</h3>
                </div>
                <p className="text-gray-300 mb-3">Format your entries with headings, lists, code blocks, and more using the slash (/) command.</p>
                <p className="text-sm text-gray-400">Type "/" anywhere in your entry to see available formatting options.</p>
              </div>
            </div>

            <div className="bg-[#202020] p-6 rounded-lg border border-[#313131] mb-8">
              <h3 className="text-xl font-medium text-white mb-4">Keyboard Shortcuts</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center">
                  <div className="bg-[#2a2a2a] px-2 py-1 rounded border border-[#3a3a3a] text-sm text-gray-300 w-20 text-center mr-3">
                    Ctrl + K
                  </div>
                  <span className="text-gray-300">Search your journal</span>
                </div>
                <div className="flex items-center">
                  <div className="bg-[#2a2a2a] px-2 py-1 rounded border border-[#3a3a3a] text-sm text-gray-300 w-20 text-center mr-3">
                    Ctrl + N
                  </div>
                  <span className="text-gray-300">Create new entry</span>
                </div>
                <div className="flex items-center">
                  <div className="bg-[#2a2a2a] px-2 py-1 rounded border border-[#3a3a3a] text-sm text-gray-300 w-20 text-center mr-3">
                    Ctrl + S
                  </div>
                  <span className="text-gray-300">Save entry</span>
                </div>
                <div className="flex items-center">
                  <div className="bg-[#2a2a2a] px-2 py-1 rounded border border-[#3a3a3a] text-sm text-gray-300 w-20 text-center mr-3">
                    /
                  </div>
                  <span className="text-gray-300">Open formatting menu</span>
                </div>
              </div>
            </div>
          </section>

          {/* Templates Section */}
          <section id="templates" className="mb-16">
            <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
              <FileText className="h-6 w-6 mr-3 text-blue-400" />
              Journal Templates
            </h2>

            <p className="text-gray-300 mb-6">Use these templates to structure your journal entries for different purposes:</p>

            <div className="space-y-6">
              <div className="bg-[#202020] p-6 rounded-lg border border-[#313131]">
                <h3 className="text-xl font-medium text-white mb-3">Trade Analysis Template</h3>
                <div className="bg-[#252525] p-4 rounded-md border border-[#313131] text-gray-300 mb-4">
                  <p className="font-medium mb-2"># Trade Analysis: [Symbol] on [Date]</p>
                  <p className="mb-2">## Setup</p>
                  <p className="text-gray-400 mb-2">- Entry price:</p>
                  <p className="text-gray-400 mb-2">- Stop loss:</p>
                  <p className="text-gray-400 mb-2">- Take profit:</p>
                  <p className="text-gray-400 mb-2">- Risk/reward ratio:</p>
                  <p className="mb-2">## Analysis</p>
                  <p className="text-gray-400 mb-2">- Why I entered this trade:</p>
                  <p className="text-gray-400 mb-2">- Market conditions:</p>
                  <p className="text-gray-400 mb-2">- Supporting indicators:</p>
                  <p className="mb-2">## Outcome</p>
                  <p className="text-gray-400 mb-2">- Result (win/loss):</p>
                  <p className="text-gray-400 mb-2">- Profit/Loss:</p>
                  <p className="text-gray-400 mb-2">- What went well:</p>
                  <p className="text-gray-400 mb-2">- What could be improved:</p>
                </div>
                <Button className="bg-blue-600 hover:bg-blue-500 text-white transition-colors duration-150">
                  Use This Template
                </Button>
              </div>

              <div className="bg-[#202020] p-6 rounded-lg border border-[#313131]">
                <h3 className="text-xl font-medium text-white mb-3">Weekly Review Template</h3>
                <div className="bg-[#252525] p-4 rounded-md border border-[#313131] text-gray-300 mb-4">
                  <p className="font-medium mb-2"># Weekly Trading Review: [Date Range]</p>
                  <p className="mb-2">## Performance Summary</p>
                  <p className="text-gray-400 mb-2">- Number of trades:</p>
                  <p className="text-gray-400 mb-2">- Win rate:</p>
                  <p className="text-gray-400 mb-2">- Total P/L:</p>
                  <p className="text-gray-400 mb-2">- Largest win/loss:</p>
                  <p className="mb-2">## What Worked Well</p>
                  <p className="text-gray-400 mb-2">- Strategies that performed:</p>
                  <p className="text-gray-400 mb-2">- Good decisions made:</p>
                  <p className="mb-2">## Areas for Improvement</p>
                  <p className="text-gray-400 mb-2">- Mistakes made:</p>
                  <p className="text-gray-400 mb-2">- Patterns to address:</p>
                  <p className="mb-2">## Next Week's Focus</p>
                  <p className="text-gray-400 mb-2">- Goals for next week:</p>
                  <p className="text-gray-400 mb-2">- Specific improvements to make:</p>
                </div>
                <Button className="bg-blue-600 hover:bg-blue-500 text-white transition-colors duration-150">
                  Use This Template
                </Button>
              </div>
            </div>
          </section>

          {/* Best Practices Section */}
          <section id="best-practices" className="mb-16">
            <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
              <CheckCircle className="h-6 w-6 mr-3 text-blue-400" />
              Best Practices
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-xl font-medium text-white mb-4">Do's</h3>
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-300">Be consistent with your journaling habit</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-300">Include both technical and emotional aspects of your trades</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-300">Use screenshots or charts to visualize your trades</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-300">Review your journal regularly to identify patterns</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-300">Tag entries for easy filtering and organization</span>
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="text-xl font-medium text-white mb-4">Don'ts</h3>
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <X className="h-5 w-5 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-300">Skip journaling after losing trades</span>
                  </li>
                  <li className="flex items-start">
                    <X className="h-5 w-5 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-300">Focus only on the outcome without analyzing the process</span>
                  </li>
                  <li className="flex items-start">
                    <X className="h-5 w-5 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-300">Be vague or too brief in your entries</span>
                  </li>
                  <li className="flex items-start">
                    <X className="h-5 w-5 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-300">Let your journal become disorganized</span>
                  </li>
                  <li className="flex items-start">
                    <X className="h-5 w-5 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-300">Forget to include your emotional state during trades</span>
                  </li>
                </ul>
              </div>
            </div>
          </section>

          {/* Examples Section */}
          <section id="examples" className="mb-16">
            <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
              <Bookmark className="h-6 w-6 mr-3 text-blue-400" />
              Example Entries
            </h2>

            <div className="space-y-8">
              <div className="bg-[#202020] p-6 rounded-lg border border-[#313131]">
                <div className="flex justify-between items-start mb-4">
                  <h3 className="text-xl font-medium text-white">Successful Breakout Trade</h3>
                  <div className="flex items-center">
                    <span className="text-xs text-gray-400 mr-2">May 15, 2023</span>
                    <div className="bg-green-500/20 text-green-400 px-2 py-0.5 rounded text-xs">
                      Success
                    </div>
                  </div>
                </div>

                <div className="bg-[#252525] p-4 rounded-md border border-[#313131] text-gray-300 mb-4">
                  <p className="mb-3">I entered a long position on EUR/USD after it broke above a key resistance level at 1.0850 that had been tested multiple times over the past week.</p>
                  <p className="mb-3">The breakout was supported by increasing volume and a bullish MACD crossover. I set my stop loss at 1.0830 (just below the previous resistance) and take profit at 1.0900.</p>
                  <p className="mb-3">The trade worked out perfectly, hitting my take profit within 3 hours. What I did well was waiting for confirmation of the breakout with volume and not just entering at the first sign of price movement above resistance.</p>
                  <p>Next time, I might consider scaling out of the position to let some profits run even further, as the price continued to climb after hitting my take profit.</p>
                </div>

                <div className="flex flex-wrap gap-2">
                  <div className="bg-[#2a2a2a] px-2 py-1 rounded text-xs text-gray-300 flex items-center">
                    <div className="h-1.5 w-1.5 rounded-full bg-blue-400 mr-1.5"></div>
                    breakout
                  </div>
                  <div className="bg-[#2a2a2a] px-2 py-1 rounded text-xs text-gray-300 flex items-center">
                    <div className="h-1.5 w-1.5 rounded-full bg-blue-400 mr-1.5"></div>
                    forex
                  </div>
                  <div className="bg-[#2a2a2a] px-2 py-1 rounded text-xs text-gray-300 flex items-center">
                    <div className="h-1.5 w-1.5 rounded-full bg-blue-400 mr-1.5"></div>
                    success
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Call to Action */}
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg p-8 text-center">
            <h2 className="text-2xl font-bold text-white mb-4">Ready to Improve Your Trading?</h2>
            <p className="text-xl text-blue-100 mb-6 max-w-2xl mx-auto">Start journaling today and take your trading to the next level with consistent reflection and analysis.</p>
            <Link to="/dashboard/journal">
              <Button className="bg-white text-blue-600 hover:bg-blue-50 text-lg px-6 py-3 transition-colors duration-150">
                Go to Your Journal
                <ChevronRight className="h-5 w-5 ml-2" />
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default JournalGuidePage;
