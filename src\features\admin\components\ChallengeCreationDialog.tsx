/**
 * Challenge Creation Dialog Component
 * @description Dialog for creating new challenges with pre-defined templates
 * @version 1.0.0
 * @status stable
 */

import React, { useState, useEffect } from 'react';
import { format, addDays, addWeeks, addMonths } from 'date-fns';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/common/components/ui/dialog';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/common/components/ui/tabs';
import { Switch } from '@/common/components/ui/switch';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/common/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/common/components/ui/form';
import { Input } from '@/common/components/ui/input';
import { Label } from '@/common/components/ui/label';
import { Button } from '@/common/components/ui/button';
import { Calendar } from '@/common/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/common/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/common/components/ui/select';
import { CalendarIcon, Check, Clock, DollarSign, Trophy } from 'lucide-react';
import { cn } from '@/common/utils';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { adminApiService } from '../services/adminApiService';
import { ChallengeType } from '@/features/challenges/types';
import { useToast } from '@/common/components/ui/use-toast';

// Challenge template types
interface ChallengeTemplate {
  name: string;
  type: ChallengeType;
  duration: string;
  entryFee: number;
  prizePool: number;
  startDate: Date;
  endDate: Date;
  description: string;
  ruleset: any;
}

// Form schema
const formSchema = z.object({
  name: z.string().min(3, { message: 'Name must be at least 3 characters' }),
  type: z.enum(['daily', 'weekly', 'monthly']),
  startDate: z.date(),
  endDate: z.date(),
  entryFee: z.number().min(0, { message: 'Entry fee must be at least 0' }),
  prizePool: z.number().min(1, { message: 'Prize pool must be at least 1' }),
});

// Challenge creation dialog props
interface ChallengeCreationDialogProps {
  onChallengeCreated?: (challenge: any) => void;
  trigger?: React.ReactNode;
}

/**
 * Challenge creation dialog component
 */
const ChallengeCreationDialog: React.FC<ChallengeCreationDialogProps> = ({
  onChallengeCreated,
  trigger
}) => {
  const [open, setOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<ChallengeTemplate | null>(null);
  const [activeTab, setActiveTab] = useState('daily');
  const [loading, setLoading] = useState(false);
  const [isFreeChallenge, setIsFreeChallenge] = useState(false);
  const [selectedFreeType, setSelectedFreeType] = useState<'daily' | 'weekly' | 'monthly'>('daily');
  const { toast } = useToast();

  // Create form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: 'Daily Trading Sprint',
      type: 'daily',
      startDate: new Date(),
      endDate: addDays(new Date(), 1),
      entryFee: 25,
      prizePool: 100,
    },
  });

  // Daily challenge template
  const dailyTemplate: ChallengeTemplate = {
    name: 'Daily Trading Sprint',
    type: ChallengeType.DAILY,
    duration: '24 hours',
    entryFee: 25,
    prizePool: 100,
    startDate: new Date(),
    endDate: addDays(new Date(), 1),
    description: 'A fast-paced 24-hour trading challenge to test your quick decision-making skills.',
    ruleset: {
      initialBalance: 10000,
      maxDrawdownPercent: 4,
      maxRiskPerTradePercent: 2,
      noHedging: true,
      noMartingale: true,
      minTradeDurationMinutes: 2,
      minTrades: 1
    }
  };

  // Weekly challenge template
  const weeklyTemplate: ChallengeTemplate = {
    name: 'Weekly Trading Challenge',
    type: ChallengeType.WEEKLY,
    duration: '7 days',
    entryFee: 50,
    prizePool: 250,
    startDate: new Date(),
    endDate: addDays(new Date(), 7),
    description: 'A week-long trading challenge to test your consistency and strategy.',
    ruleset: {
      initialBalance: 50000,
      maxDrawdownPercent: 8,
      maxDailyDrawdownPercent: 4,
      maxRiskPerTradePercent: 2,
      noHedging: true,
      noMartingale: true,
      minTradeDurationMinutes: 2,
      minTrades: 3,
      minSwingTradeDays: 2
    }
  };

  // Monthly challenge template
  const monthlyTemplate: ChallengeTemplate = {
    name: 'Monthly Trading Championship',
    type: ChallengeType.MONTHLY,
    duration: '30 days',
    entryFee: 100,
    prizePool: 500,
    startDate: new Date(),
    endDate: addDays(new Date(), 30),
    description: 'A month-long trading championship to test your long-term strategy and discipline.',
    ruleset: {
      initialBalance: 100000,
      maxDrawdownPercent: 10,
      maxDailyDrawdownPercent: 4,
      maxRiskPerTradePercent: 2,
      noHedging: true,
      noMartingale: true,
      minTradeDurationMinutes: 2,
      minTrades: 6,
      minTradingDays: 6,
      minSwingTradeDays: 2
    }
  };

  // Free challenge templates
  const freeDailyTemplate: ChallengeTemplate = {
    name: 'Free Daily Trading Challenge',
    type: ChallengeType.DAILY,
    duration: '24 hours',
    entryFee: 0,
    prizePool: 50,
    startDate: new Date(),
    endDate: addDays(new Date(), 1),
    description: 'A free 24-hour trading challenge to attract new users and let them experience the platform.',
    ruleset: {
      initialBalance: 10000,
      maxDrawdownPercent: 4,
      maxRiskPerTradePercent: 2,
      noHedging: true,
      noMartingale: true,
      minTradeDurationMinutes: 2,
      minTrades: 1
    }
  };

  const freeWeeklyTemplate: ChallengeTemplate = {
    name: 'Free Weekly Trading Challenge',
    type: ChallengeType.WEEKLY,
    duration: '7 days',
    entryFee: 0,
    prizePool: 150,
    startDate: new Date(),
    endDate: addDays(new Date(), 7),
    description: 'A free 7-day trading challenge to showcase your consistency and attract new users.',
    ruleset: {
      initialBalance: 25000,
      maxDrawdownPercent: 6,
      maxDailyDrawdownPercent: 3,
      maxRiskPerTradePercent: 2,
      noHedging: true,
      noMartingale: true,
      minTradeDurationMinutes: 2,
      minTrades: 2,
      minSwingTradeDays: 1
    }
  };

  const freeMonthlyTemplate: ChallengeTemplate = {
    name: 'Free Monthly Trading Championship',
    type: ChallengeType.MONTHLY,
    duration: '30 days',
    entryFee: 0,
    prizePool: 300,
    startDate: new Date(),
    endDate: addDays(new Date(), 30),
    description: 'A free 30-day trading championship to demonstrate long-term strategy and attract serious traders.',
    ruleset: {
      initialBalance: 50000,
      maxDrawdownPercent: 8,
      maxDailyDrawdownPercent: 3,
      maxRiskPerTradePercent: 2,
      noHedging: true,
      noMartingale: true,
      minTradeDurationMinutes: 2,
      minTrades: 4,
      minTradingDays: 4,
      minSwingTradeDays: 1
    }
  };

  // Free templates collection
  const freeTemplates = {
    daily: freeDailyTemplate,
    weekly: freeWeeklyTemplate,
    monthly: freeMonthlyTemplate
  };

  // Update form when template changes
  useEffect(() => {
    if (selectedTemplate) {
      form.reset({
        name: selectedTemplate.name,
        type: selectedTemplate.type,
        startDate: selectedTemplate.startDate,
        endDate: selectedTemplate.endDate,
        entryFee: selectedTemplate.entryFee,
        prizePool: selectedTemplate.prizePool,
      });

      // Set free challenge state
      setIsFreeChallenge(selectedTemplate.entryFee === 0);
    }
  }, [selectedTemplate, form]);

  // Handle template selection
  const handleSelectTemplate = (template: ChallengeTemplate) => {
    setSelectedTemplate(template);
  };

  // Handle reset to templates
  const handleBackToTemplates = () => {
    setSelectedTemplate(null);
    setSelectedFreeType('daily');
  };

  // Handle form submission
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      setLoading(true);

      // Get ruleset based on challenge type
      let ruleset;
      try {
        if (selectedTemplate?.ruleset) {
          ruleset = selectedTemplate.ruleset;
        } else {
          const rulesetResponse = await adminApiService.challenge.getDefaultRuleset(values.type);
          ruleset = rulesetResponse.data;
        }
      } catch (rulesetError) {
        console.error('Error getting ruleset:', rulesetError);
        // Use a default ruleset if we can't get one from the API
        ruleset = {
          initialBalance: 10000,
          maxDrawdownPercent: 5,
          maxRiskPerTradePercent: 2,
          noHedging: true,
          noMartingale: true,
          minTradeDurationMinutes: 2,
          minTrades: 2
        };
      }

      // Create challenge data
      const challengeData = {
        ...values,
        ruleset,
        status: 'upcoming',
        // Ensure entryFee is 0 for free challenges
        entryFee: isFreeChallenge ? 0 : values.entryFee
      };

      console.log('Creating challenge with data:', challengeData);

      // Create challenge
      const response = await adminApiService.challenge.createChallenge(challengeData);

      console.log('Challenge created successfully:', response);

      // Check if the response indicates success
      if (response.success === false) {
        toast({
          title: 'Warning',
          description: 'Challenge may not have been created properly. Please check the challenges list.',
          variant: 'destructive',
        });
        return;
      }

      // Call onChallengeCreated callback
      if (onChallengeCreated) {
        // Handle different response formats
        const challengeData = response.data || response;
        onChallengeCreated(challengeData);

        toast({
          title: 'Success',
          description: 'Challenge created successfully',
        });
      }

      // Close dialog
      setOpen(false);
      setSelectedTemplate(null);
      setSelectedFreeType('daily');
      form.reset();
    } catch (error: any) {
      console.error('Error creating challenge:', error);

      // Show more detailed error information
      const errorMessage = error.message || 'Failed to create challenge';
      const errorStatus = error.status || '';

      // Use toast to show error message
      toast({
        title: `Error (${errorStatus})`,
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={(newOpen) => {
      setOpen(newOpen);
      if (!newOpen) {
        // Reset state when dialog closes
        setSelectedTemplate(null);
        setSelectedFreeType('daily');
        form.reset();
      }
    }}>
      <DialogTrigger asChild>
        {trigger || <Button>Create Challenge</Button>}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[800px] bg-forex-darker border-forex-border text-white">
        <DialogHeader>
          <DialogTitle className="text-white">Create New Challenge</DialogTitle>
          <DialogDescription className="text-white/80">
            Select a template or customize your own challenge.
          </DialogDescription>
        </DialogHeader>

        {!selectedTemplate ? (
          <Tabs defaultValue="daily" value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-4 mb-4">
              <TabsTrigger value="daily">Daily</TabsTrigger>
              <TabsTrigger value="weekly">Weekly</TabsTrigger>
              <TabsTrigger value="monthly">Monthly</TabsTrigger>
              <TabsTrigger value="free">Free</TabsTrigger>
            </TabsList>

            <TabsContent value="daily" className="mt-0">
              <TemplateCard
                template={dailyTemplate}
                onSelect={handleSelectTemplate}
              />
            </TabsContent>

            <TabsContent value="weekly" className="mt-0">
              <TemplateCard
                template={weeklyTemplate}
                onSelect={handleSelectTemplate}
              />
            </TabsContent>

            <TabsContent value="monthly" className="mt-0">
              <TemplateCard
                template={monthlyTemplate}
                onSelect={handleSelectTemplate}
              />
            </TabsContent>

            <TabsContent value="free" className="mt-0">
              <div className="space-y-4">
                <div className="mb-4">
                  <Label className="text-white text-sm font-medium mb-2 block">
                    Select Free Challenge Type
                  </Label>
                  <Select
                    value={selectedFreeType}
                    onValueChange={(value: 'daily' | 'weekly' | 'monthly') => setSelectedFreeType(value)}
                  >
                    <SelectTrigger className="w-full text-white bg-forex-dark border-forex-border">
                      <SelectValue placeholder="Choose challenge duration" />
                    </SelectTrigger>
                    <SelectContent className="bg-forex-darker border-forex-border">
                      <SelectItem value="daily" className="text-white hover:bg-forex-card">
                        Daily (24 hours) - $50 Prize Pool
                      </SelectItem>
                      <SelectItem value="weekly" className="text-white hover:bg-forex-card">
                        Weekly (7 days) - $150 Prize Pool
                      </SelectItem>
                      <SelectItem value="monthly" className="text-white hover:bg-forex-card">
                        Monthly (30 days) - $300 Prize Pool
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <TemplateCard
                  template={freeTemplates[selectedFreeType]}
                  onSelect={handleSelectTemplate}
                />
              </div>
            </TabsContent>
          </Tabs>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-white">Challenge Name</FormLabel>
                    <FormControl>
                      <Input {...field} className="text-white bg-forex-dark border-forex-border" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="startDate"
                  render={({ field }) => {
                    const [startDateOpen, setStartDateOpen] = React.useState(false);

                    return (
                      <FormItem className="flex flex-col">
                        <FormLabel className="text-white">Start Date</FormLabel>
                        <Popover open={startDateOpen} onOpenChange={setStartDateOpen}>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="outline"
                                className={cn(
                                  "pl-3 text-left font-normal text-white bg-forex-dark border-forex-border",
                                  !field.value && "text-white/70"
                                )}
                                onClick={() => setStartDateOpen(true)}
                              >
                                {field.value ? (
                                  format(field.value, "PPP")
                                ) : (
                                  <span>Pick a date</span>
                                )}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0 bg-forex-darker border-forex-border z-50" align="start">
                            <div className="p-3 bg-forex-darker" onClick={(e) => e.stopPropagation()}>
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={(date) => {
                                  if (date) {
                                    field.onChange(date);
                                    // If end date is before start date, update end date
                                    const endDate = form.getValues().endDate;
                                    if (endDate && date > endDate) {
                                      // Set end date based on challenge type
                                      if (form.getValues().type === 'daily') {
                                        // Daily challenge: 24 hours (1 day)
                                        form.setValue('endDate', addDays(date, 1));
                                      }
                                      else if (form.getValues().type === 'weekly') {
                                        // Weekly challenge: 7 days
                                        form.setValue('endDate', addDays(date, 7));
                                      }
                                      else if (form.getValues().type === 'monthly') {
                                        // Monthly challenge: 30 days
                                        form.setValue('endDate', addDays(date, 30));
                                      }
                                    }
                                    // Don't close the popover automatically
                                  }
                                }}
                                disabled={(date) => date < new Date()}
                                initialFocus
                                fromDate={new Date()}
                                captionLayout="buttons"
                                fixedWeeks
                              />
                              <div className="mt-4 flex justify-end">
                                <Button
                                  type="button"
                                  variant="outline"
                                  size="sm"
                                  onClick={() => setStartDateOpen(false)}
                                  className="bg-forex-primary text-white hover:bg-forex-hover"
                                >
                                  Done
                                </Button>
                              </div>
                            </div>
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />

                <FormField
                  control={form.control}
                  name="endDate"
                  render={({ field }) => {
                    const [endDateOpen, setEndDateOpen] = React.useState(false);

                    return (
                      <FormItem className="flex flex-col">
                        <FormLabel className="text-white">End Date</FormLabel>
                        <Popover open={endDateOpen} onOpenChange={setEndDateOpen}>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="outline"
                                className={cn(
                                  "pl-3 text-left font-normal text-white bg-forex-dark border-forex-border",
                                  !field.value && "text-white/70"
                                )}
                                onClick={() => setEndDateOpen(true)}
                              >
                                {field.value ? (
                                  format(field.value, "PPP")
                                ) : (
                                  <span>Pick a date</span>
                                )}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0 bg-forex-darker border-forex-border z-50" align="start">
                            <div className="p-3 bg-forex-darker" onClick={(e) => e.stopPropagation()}>
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={(date) => {
                                  if (date) {
                                    field.onChange(date);
                                    // Don't close the popover automatically
                                  }
                                }}
                                disabled={(date) =>
                                  date < new Date() ||
                                  date < form.getValues().startDate
                                }
                                initialFocus
                                fromDate={form.getValues().startDate || new Date()}
                                captionLayout="buttons"
                                fixedWeeks
                              />
                              <div className="mt-4 flex justify-end">
                                <Button
                                  type="button"
                                  variant="outline"
                                  size="sm"
                                  onClick={() => setEndDateOpen(false)}
                                  className="bg-forex-primary text-white hover:bg-forex-hover"
                                >
                                  Done
                                </Button>
                              </div>
                            </div>
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />
              </div>

              <div className="flex items-center space-x-2 mb-4">
                <Switch
                  id="free-challenge"
                  checked={isFreeChallenge}
                  onCheckedChange={(checked) => {
                    setIsFreeChallenge(checked);
                    if (checked) {
                      form.setValue('entryFee', 0);
                    } else {
                      // Set to default entry fee based on type
                      const type = form.getValues('type');
                      if (type === 'daily') form.setValue('entryFee', 25);
                      else if (type === 'weekly') form.setValue('entryFee', 50);
                      else if (type === 'monthly') form.setValue('entryFee', 100);
                    }
                  }}
                />
                <label
                  htmlFor="free-challenge"
                  className="text-sm font-medium leading-none text-white peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Free Challenge (No Entry Fee)
                </label>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="entryFee"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">Entry Fee ($)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          {...field}
                          onChange={e => field.onChange(parseFloat(e.target.value))}
                          disabled={isFreeChallenge}
                          value={isFreeChallenge ? 0 : field.value}
                          className="text-white bg-forex-dark border-forex-border"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="prizePool"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">Prize Pool ($)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          {...field}
                          onChange={e => field.onChange(parseFloat(e.target.value))}
                          className="text-white bg-forex-dark border-forex-border"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <DialogFooter className="mt-6">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleBackToTemplates}
                >
                  Back to Templates
                </Button>
                <Button type="submit" disabled={loading}>
                  {loading ? 'Creating...' : 'Create Challenge'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        )}
      </DialogContent>
    </Dialog>
  );
};

/**
 * Template card component
 */
interface TemplateCardProps {
  template: ChallengeTemplate;
  onSelect: (template: ChallengeTemplate) => void;
}

const TemplateCard: React.FC<TemplateCardProps> = ({ template, onSelect }) => {
  return (
    <Card className="border-forex-border bg-forex-card text-white">
      <CardHeader>
        <CardTitle className="text-white">{template.name}</CardTitle>
        <CardDescription className="text-white/80">{template.description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="flex items-center">
            <Trophy className="h-4 w-4 mr-2 text-forex-accent" />
            <span className="text-white">Type: {template.type}</span>
          </div>
          <div className="flex items-center">
            <Clock className="h-4 w-4 mr-2 text-forex-accent" />
            <span className="text-white">Duration: {template.duration}</span>
          </div>
          <div className="flex items-center">
            <DollarSign className="h-4 w-4 mr-2 text-forex-accent" />
            <span className="text-white">Entry Fee: ${template.entryFee}</span>
          </div>
          <div className="flex items-center">
            <DollarSign className="h-4 w-4 mr-2 text-forex-accent" />
            <span className="text-white">Prize Pool: ${template.prizePool}</span>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button
          className="w-full"
          onClick={() => onSelect(template)}
        >
          Use This Template
        </Button>
      </CardFooter>
    </Card>
  );
};

export default ChallengeCreationDialog;
