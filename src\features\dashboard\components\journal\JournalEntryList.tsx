import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ChevronDown,
  ChevronRight,
  Calendar,
  Tag,
  MoreHorizontal,
  MessageSquare,
  Image as ImageIcon,
  Edit,
  Trash2,
  Copy,
  Link,
  ExternalLink,
  Clock,
  TrendingUp,
  TrendingDown,
  Minus,
  DollarSign
} from 'lucide-react';
import {
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/common/components/ui';
import { JournalEntry } from '../../services/journalService';
import { format, formatDistanceToNow } from 'date-fns';
import ImageGallery from './ImageGallery';

interface JournalEntryListProps {
  entries: JournalEntry[];
}

/**
 * Journal Entry List Component
 *
 * Displays a list of journal entries in a Notion-style format
 *
 * @component
 * @version 2.0.0
 */
const JournalEntryList: React.FC<JournalEntryListProps> = ({ entries }) => {
  const [expandedEntries, setExpandedEntries] = useState<string[]>([]);
  const [hoveredEntry, setHoveredEntry] = useState<string | null>(null);

  const toggleExpand = (id: string) => {
    setExpandedEntries(prev =>
      prev.includes(id)
        ? prev.filter(entryId => entryId !== id)
        : [...prev, id]
    );
  };

  const isExpanded = (id: string) => expandedEntries.includes(id);

  // Get mood emoji
  const getMoodEmoji = (mood?: 'positive' | 'neutral' | 'negative') => {
    switch (mood) {
      case 'positive': return '😊';
      case 'neutral': return '😐';
      case 'negative': return '😔';
      default: return '';
    }
  };

  // Get profitability icon and color
  const getProfitabilityInfo = (profitability?: 'profitable' | 'unprofitable' | 'break-even') => {
    switch (profitability) {
      case 'profitable':
        return {
          icon: TrendingUp,
          color: 'text-green-400',
          bgColor: 'bg-green-500/20',
          borderColor: 'border-green-500/30',
          label: 'Profitable day'
        };
      case 'unprofitable':
        return {
          icon: TrendingDown,
          color: 'text-red-400',
          bgColor: 'bg-red-500/20',
          borderColor: 'border-red-500/30',
          label: 'Unprofitable day'
        };
      case 'break-even':
        return {
          icon: Minus,
          color: 'text-blue-400',
          bgColor: 'bg-blue-500/20',
          borderColor: 'border-blue-500/30',
          label: 'Break-even day'
        };
      default:
        return null;
    }
  };

  // Format date in Notion style
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return format(date, 'MMM d, yyyy');
    } catch (error) {
      return 'Invalid date';
    }
  };

  // Format relative time
  const formatRelativeTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, { addSuffix: true });
    } catch (error) {
      return 'Invalid date';
    }
  };

  if (entries.length === 0) {
    return null; // Empty state is handled by the parent component
  }

  return (
    <div className="rounded-md border border-[#313131] overflow-hidden">
      {/* Table header - Notion-style dark mode */}
      <div className="bg-[#252525] border-b border-[#313131] py-2 px-3 flex items-center text-xs font-medium text-gray-400 tracking-wider">
        <div className="w-7"></div>
        <div className="flex-1">NAME</div>
        <div className="w-32 text-right">CREATED</div>
        <div className="w-8"></div>
      </div>

      {/* Table body - Notion-style dark mode */}
      <div>
        {entries.map(entry => (
          <div
            key={entry.id}
            className="group border-b border-[#313131] last:border-b-0"
            onMouseEnter={() => setHoveredEntry(entry.id)}
            onMouseLeave={() => setHoveredEntry(null)}
          >
            {/* Entry Row - Notion-style dark mode */}
            <div
              className={`py-2 px-3 flex items-center hover:bg-[#2e2e2e] transition-colors duration-150 cursor-pointer ${
                isExpanded(entry.id) ? 'bg-[#2e2e2e]' : ''
              }`}
              onClick={() => toggleExpand(entry.id)}
            >
              {/* Expand/Collapse icon */}
              <div className="w-7 flex items-center justify-center text-gray-500">
                {isExpanded(entry.id) ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </div>

              {/* Title and emoji */}
              <div className="flex-1 flex items-center gap-2 min-w-0">
                <span className="font-medium text-gray-200 truncate">{entry.title}</span>
                {entry.mood && (
                  <span className="text-base flex-shrink-0" title={`Mood: ${entry.mood}`}>
                    {getMoodEmoji(entry.mood)}
                  </span>
                )}

                {/* Profitability indicator */}
                {entry.dayProfitability && (
                  <div className="flex-shrink-0">
                    {(() => {
                      const profitInfo = getProfitabilityInfo(entry.dayProfitability);
                      if (!profitInfo) return null;

                      const ProfitIcon = profitInfo.icon;
                      return (
                        <div
                          className={`flex items-center gap-1 px-1.5 py-0.5 rounded text-xs ${profitInfo.color} ${profitInfo.bgColor} border ${profitInfo.borderColor}`}
                          title={profitInfo.label}
                        >
                          <ProfitIcon className="h-3 w-3" />
                          {entry.pnlAmount !== undefined && (
                            <span>{entry.pnlAmount >= 0 ? '+' : ''}{entry.pnlAmount}</span>
                          )}
                        </div>
                      );
                    })()}
                  </div>
                )}

                {/* Tags (only show on hover or when expanded) - Notion-style dark mode */}
                {entry.tags && entry.tags.length > 0 && (hoveredEntry === entry.id || isExpanded(entry.id)) && (
                  <div className="flex items-center gap-1 ml-2 flex-shrink-0">
                    {entry.tags.slice(0, 2).map(tag => (
                      <div
                        key={tag}
                        className="flex items-center gap-1 px-1.5 py-0.5 bg-[#2a2a2a] rounded text-xs text-gray-300 border border-[#3a3a3a]"
                      >
                        <div className="h-1.5 w-1.5 rounded-full bg-blue-500"></div>
                        <span>{tag}</span>
                      </div>
                    ))}
                    {entry.tags.length > 2 && (
                      <span className="text-xs text-gray-500">+{entry.tags.length - 2}</span>
                    )}
                  </div>
                )}
              </div>

              {/* Date - Notion-style dark mode */}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="w-32 text-right text-xs text-gray-500">
                      {formatDate(entry.createdAt)}
                    </div>
                  </TooltipTrigger>
                  <TooltipContent className="bg-[#2a2a2a] text-white text-xs py-1 px-2 border border-[#3a3a3a]">
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {formatRelativeTime(entry.createdAt)}
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              {/* Actions menu (only visible on hover) - Notion-style dark mode */}
              <div className={`w-8 flex justify-center ${hoveredEntry === entry.id ? 'opacity-100' : 'opacity-0'} transition-opacity duration-150`}>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                    <Button variant="ghost" size="sm" className="h-6 w-6 p-0 text-gray-500 hover:text-gray-300 hover:bg-[#3a3a3a] transition-colors duration-150">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="bg-[#202020] border border-[#3a3a3a] shadow-md rounded-md w-48">
                    <DropdownMenuItem className="text-sm text-gray-200 cursor-pointer focus:bg-[#2e2e2e]">
                      <Edit className="h-3.5 w-3.5 mr-2 text-gray-400" />
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuItem className="text-sm text-gray-200 cursor-pointer focus:bg-[#2e2e2e]">
                      <Copy className="h-3.5 w-3.5 mr-2 text-gray-400" />
                      Duplicate
                    </DropdownMenuItem>
                    <DropdownMenuItem className="text-sm text-gray-200 cursor-pointer focus:bg-[#2e2e2e]">
                      <Link className="h-3.5 w-3.5 mr-2 text-gray-400" />
                      Copy link
                    </DropdownMenuItem>
                    <DropdownMenuSeparator className="bg-[#3a3a3a]" />
                    <DropdownMenuItem className="text-sm text-red-400 cursor-pointer focus:bg-[#2e2e2e]">
                      <Trash2 className="h-3.5 w-3.5 mr-2 text-red-400" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>

            {/* Expanded Content - Notion-style dark mode */}
            <AnimatePresence>
              {isExpanded(entry.id) && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="overflow-hidden bg-[#252525] border-t border-[#313131]"
                >
                  <div className="pl-10 pr-4 py-3">
                    {/* Content - Notion-style dark mode */}
                    <div className="text-gray-300 space-y-2">
                      {entry.content.split('\n\n').map((paragraph, i) => (
                        <p key={i} className="text-sm">
                          {paragraph.split('\n').map((line, j) => (
                            <React.Fragment key={j}>
                              {line}
                              {j < paragraph.split('\n').length - 1 && <br />}
                            </React.Fragment>
                          ))}
                        </p>
                      ))}
                    </div>

                    {/* Tags (full list) - Notion-style dark mode */}
                    {entry.tags && entry.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-4">
                        <div className="flex items-center text-xs text-gray-400 mr-1">
                          <Tag className="h-3 w-3 mr-1" />
                          Tags:
                        </div>
                        {entry.tags.map(tag => (
                          <div
                            key={tag}
                            className="flex items-center gap-1 px-2 py-0.5 bg-[#2a2a2a] rounded text-xs text-gray-300 border border-[#3a3a3a]"
                          >
                            <div className="h-1.5 w-1.5 rounded-full bg-blue-500"></div>
                            <span>{tag}</span>
                          </div>
                        ))}
                      </div>
                    )}

                    {/* Attachments - Notion-style dark mode */}
                    {entry.attachments && entry.attachments.length > 0 && (
                      <div className="mt-4">
                        <div className="flex items-center text-xs text-gray-400 mb-2">
                          <ImageIcon className="h-3 w-3 mr-1" />
                          Attachments:
                        </div>

                        {/* Images */}
                        {entry.attachments.some(a => a.type === 'image') && (
                          <ImageGallery
                            images={entry.attachments
                              .filter(a => a.type === 'image')
                              .map(a => a.url)}
                          />
                        )}

                        {/* Other attachments */}
                        {entry.attachments.some(a => a.type !== 'image') && (
                          <div className="grid grid-cols-2 gap-2 mt-2">
                            {entry.attachments
                              .filter(a => a.type !== 'image')
                              .map((attachment, index) => (
                                <div
                                  key={index}
                                  className="flex items-center gap-2 text-xs text-gray-300 p-2 bg-[#2a2a2a] rounded border border-[#3a3a3a] hover:bg-[#333333] cursor-pointer transition-colors duration-150"
                                >
                                  <div className="w-8 h-8 bg-[#333333] rounded flex items-center justify-center flex-shrink-0">
                                    <ImageIcon className="h-4 w-4 text-gray-400" />
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <div className="font-medium truncate">{attachment.name}</div>
                                    <div className="text-gray-500">{attachment.type}</div>
                                  </div>
                                  <ExternalLink className="h-3 w-3 text-gray-400" />
                                </div>
                            ))}
                          </div>
                        )}
                      </div>
                    )}

                    {/* Day Profitability - Notion-style dark mode */}
                    {entry.dayProfitability && (
                      <div className="mt-4 flex items-center gap-2 text-xs text-gray-400">
                        <div className="flex items-center gap-1">
                          <DollarSign className="h-3.5 w-3.5 text-gray-400" />
                          <span>Day Performance:</span>
                        </div>
                        {(() => {
                          const profitInfo = getProfitabilityInfo(entry.dayProfitability);
                          if (!profitInfo) return null;

                          const ProfitIcon = profitInfo.icon;
                          return (
                            <div
                              className={`flex items-center gap-1 px-2 py-1 rounded ${profitInfo.color} ${profitInfo.bgColor} border ${profitInfo.borderColor}`}
                            >
                              <ProfitIcon className="h-3.5 w-3.5" />
                              <span>{profitInfo.label}</span>
                              {entry.pnlAmount !== undefined && (
                                <span className="font-medium ml-1">
                                  ({entry.pnlAmount >= 0 ? '+' : ''}{entry.pnlAmount})
                                </span>
                              )}
                            </div>
                          );
                        })()}
                      </div>
                    )}

                    {/* Trade references - Notion-style dark mode */}
                    {entry.tradeIds && entry.tradeIds.length > 0 && (
                      <div className="mt-4 flex items-center text-xs text-gray-400">
                        <div className="flex items-center gap-1 px-2 py-1 bg-[#2a2a2a] text-blue-400 rounded border border-[#3a3a3a]">
                          <div className="h-1.5 w-1.5 rounded-full bg-blue-500"></div>
                          <span>Related: {entry.tradeIds.length} trade{entry.tradeIds.length !== 1 ? 's' : ''}</span>
                        </div>
                      </div>
                    )}

                    {/* Last updated info - Notion-style dark mode */}
                    <div className="mt-4 text-xs text-gray-500 flex items-center">
                      <Clock className="h-3 w-3 mr-1" />
                      Last edited {formatRelativeTime(entry.updatedAt)}
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        ))}
      </div>
    </div>
  );
};

export default JournalEntryList;
