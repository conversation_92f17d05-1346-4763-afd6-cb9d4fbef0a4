import React, { useState, useRef, useEffect } from 'react';
import {
  Bold,
  Italic,
  List,
  ListOrdered,
  Image as ImageIcon,
  Link as LinkIcon,
  Tag as TagIcon,
  Smile,
  Plus,
  MoreHorizontal,
  Check,
  Code,
  Quote,
  Heading1,
  Heading2,
  Heading3,
  AlignLeft,
  Table,
  FileText,
  Calendar,
  Clock,
  ChevronDown,
  Command,
  Search,
  CheckSquare,
  ArrowUpRight,
  Bookmark,
  FileCode,
  Sparkles,
  Zap,
  X,
  Trash2,
  AlertCircle,
  TrendingUp,
  TrendingDown,
  Minus,
  DollarSign
} from 'lucide-react';
import {
  Button,
  Input,
  Textarea,
  Popover,
  PopoverContent,
  PopoverTrigger,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
  Alert,
  AlertDescription
} from '@/common/components/ui';
import ImageUploader from './ImageUploader';
import ImageGallery from './ImageGallery';
import { createJournalEntry, updateJournalEntry } from '../../services/journalService';

interface JournalEntryEditorProps {
  onCancel: () => void;
  onSaveSuccess?: () => void;
  entryId?: number;
  initialTitle?: string;
  initialContent?: string;
  initialTags?: string[];
  initialImages?: string[];
  initialMood?: 'positive' | 'neutral' | 'negative';
  initialDayProfitability?: 'profitable' | 'unprofitable' | 'break-even';
  initialPnlAmount?: number;
}

/**
 * Journal Entry Editor Component
 *
 * A Notion-style editor for creating and editing journal entries
 *
 * @component
 * @version 3.0.0
 */
const JournalEntryEditor: React.FC<JournalEntryEditorProps> = ({
  onCancel,
  onSaveSuccess,
  entryId,
  initialTitle = '',
  initialContent = '',
  initialTags = [],
  initialImages = [],
  initialMood = undefined,
  initialDayProfitability = undefined,
  initialPnlAmount = undefined
}) => {
  const titleRef = useRef<HTMLInputElement>(null);
  const contentRef = useRef<HTMLTextAreaElement>(null);
  const [title, setTitle] = useState(initialTitle);
  const [content, setContent] = useState(initialContent);
  const [tags, setTags] = useState<string[]>(initialTags);
  const [newTag, setNewTag] = useState('');
  const [images, setImages] = useState<string[]>(initialImages);
  const [mood, setMood] = useState<'positive' | 'neutral' | 'negative' | undefined>(initialMood);
  const [dayProfitability, setDayProfitability] = useState<'profitable' | 'unprofitable' | 'break-even' | undefined>(initialDayProfitability);
  const [pnlAmount, setPnlAmount] = useState<number | undefined>(initialPnlAmount);
  const [showPlaceholder, setShowPlaceholder] = useState(!initialContent);
  const [showBlockMenu, setShowBlockMenu] = useState(false);
  const [blockMenuPosition, setBlockMenuPosition] = useState({ top: 0, left: 0 });
  const [blockMenuSearch, setBlockMenuSearch] = useState('');
  const [selectedBlockIndex, setSelectedBlockIndex] = useState(0);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Focus title input on mount
  useEffect(() => {
    if (titleRef.current) {
      titleRef.current.focus();
    }
  }, []);

  // Define block types for the command menu
  const blockTypes = [
    { id: 'heading1', name: 'Heading 1', description: 'Big section heading', icon: Heading1 },
    { id: 'heading2', name: 'Heading 2', description: 'Medium section heading', icon: Heading2 },
    { id: 'heading3', name: 'Heading 3', description: 'Small section heading', icon: Heading3 },
    { id: 'list', name: 'Bulleted list', description: 'Create a simple bulleted list', icon: List },
    { id: 'ordered-list', name: 'Numbered list', description: 'Create a list with numbering', icon: ListOrdered },
    { id: 'quote', name: 'Quote', description: 'Capture a quote', icon: Quote },
    { id: 'divider', name: 'Divider', description: 'Visual divider between sections', icon: () => <div className="w-4 h-0.5 bg-gray-500"></div> },
    { id: 'code', name: 'Code', description: 'Inline code snippet', icon: Code },
    { id: 'codeblock', name: 'Code block', description: 'Block of code with syntax', icon: FileText },
    { id: 'table', name: 'Table', description: 'Add a simple table', icon: Table },
    { id: 'image', name: 'Image', description: 'Upload or embed an image', icon: ImageIcon },
    { id: 'todo', name: 'To-do list', description: 'Track tasks with a to-do list', icon: CheckSquare },
    { id: 'link', name: 'Link', description: 'Add a web link', icon: ArrowUpRight },
    { id: 'bookmark', name: 'Bookmark', description: 'Save content for later', icon: Bookmark },
    { id: 'callout', name: 'Callout', description: 'Add a colorful callout block', icon: Sparkles },
    { id: 'date', name: 'Date', description: 'Insert current date', icon: Calendar },
    { id: 'time', name: 'Time', description: 'Insert current time', icon: Clock }
  ];

  // Filter block types based on search
  const filteredBlockTypes = blockMenuSearch.trim() === ''
    ? blockTypes
    : blockTypes.filter(block =>
        block.name.toLowerCase().includes(blockMenuSearch.toLowerCase()) ||
        block.description.toLowerCase().includes(blockMenuSearch.toLowerCase())
      );

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Cmd/Ctrl + S to save
      if ((e.metaKey || e.ctrlKey) && e.key === 's') {
        e.preventDefault();
        handleSave();
      }

      // Cmd/Ctrl + / to show block menu
      if ((e.metaKey || e.ctrlKey) && e.key === '/') {
        e.preventDefault();
        handleShowBlockMenu();
      }

      // Block menu navigation
      if (showBlockMenu) {
        switch (e.key) {
          case 'Escape':
            e.preventDefault();
            setShowBlockMenu(false);
            setBlockMenuSearch('');
            setSelectedBlockIndex(0);
            break;
          case 'ArrowDown':
            e.preventDefault();
            setSelectedBlockIndex(prev =>
              prev < filteredBlockTypes.length - 1 ? prev + 1 : prev
            );
            break;
          case 'ArrowUp':
            e.preventDefault();
            setSelectedBlockIndex(prev => prev > 0 ? prev - 1 : 0);
            break;
          case 'Enter':
            e.preventDefault();
            if (filteredBlockTypes.length > 0) {
              insertFormat(filteredBlockTypes[selectedBlockIndex].id);
              setBlockMenuSearch('');
              setSelectedBlockIndex(0);
            }
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [showBlockMenu, selectedBlockIndex, filteredBlockTypes]);

  // Show block menu
  const handleShowBlockMenu = () => {
    if (!contentRef.current) return;

    const { selectionStart } = contentRef.current;
    const textBeforeCursor = content.substring(0, selectionStart);
    const lineStartIndex = textBeforeCursor.lastIndexOf('\n') + 1;

    // Get cursor position
    const cursorPosition = contentRef.current.getBoundingClientRect();
    const lineHeight = parseInt(window.getComputedStyle(contentRef.current).lineHeight);

    // Calculate lines before cursor
    const linesBefore = textBeforeCursor.split('\n').length - 1;

    setBlockMenuPosition({
      top: cursorPosition.top + linesBefore * lineHeight,
      left: cursorPosition.left
    });

    setShowBlockMenu(true);
  };

  // Format options
  const insertFormat = (format: string) => {
    // Get textarea element
    const textarea = contentRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = content.substring(start, end);
    let formattedText = '';
    let cursorOffset = 0;

    // Remove the slash command from the content
    let newContent = content;
    if (showBlockMenu) {
      const textBeforeCursor = content.substring(0, start);
      const lineStartIndex = textBeforeCursor.lastIndexOf('\n') + 1;
      const currentLine = textBeforeCursor.substring(lineStartIndex);

      if (currentLine.startsWith('/')) {
        // Remove the slash command
        newContent = content.substring(0, lineStartIndex) + content.substring(start);
        setContent(newContent);
      }
    }

    switch (format) {
      case 'bold':
        formattedText = `**${selectedText}**`;
        cursorOffset = selectedText.length ? 0 : -2;
        break;
      case 'italic':
        formattedText = `*${selectedText}*`;
        cursorOffset = selectedText.length ? 0 : -1;
        break;
      case 'heading1':
        formattedText = `# ${selectedText}`;
        cursorOffset = selectedText.length ? 0 : 0;
        break;
      case 'heading2':
        formattedText = `## ${selectedText}`;
        cursorOffset = selectedText.length ? 0 : 0;
        break;
      case 'heading3':
        formattedText = `### ${selectedText}`;
        cursorOffset = selectedText.length ? 0 : 0;
        break;
      case 'list':
        formattedText = `- ${selectedText}`;
        cursorOffset = selectedText.length ? 0 : 0;
        break;
      case 'ordered-list':
        formattedText = `1. ${selectedText}`;
        cursorOffset = selectedText.length ? 0 : 0;
        break;
      case 'code':
        formattedText = `\`${selectedText}\``;
        cursorOffset = selectedText.length ? 0 : -1;
        break;
      case 'codeblock':
        formattedText = `\`\`\`\n${selectedText}\n\`\`\``;
        cursorOffset = selectedText.length ? 0 : -4;
        break;
      case 'quote':
        formattedText = `> ${selectedText}`;
        cursorOffset = selectedText.length ? 0 : 0;
        break;
      case 'link':
        formattedText = `[${selectedText || 'Link text'}](url)`;
        cursorOffset = selectedText.length ? -1 : -6;
        break;
      case 'divider':
        formattedText = `---\n`;
        cursorOffset = 0;
        break;
      case 'table':
        formattedText = `| Header 1 | Header 2 | Header 3 |\n| --- | --- | --- |\n| Cell 1 | Cell 2 | Cell 3 |\n| Cell 4 | Cell 5 | Cell 6 |`;
        cursorOffset = 0;
        break;
      case 'image':
        // Instead of inserting markdown, we'll trigger the image upload UI
        // Just close the block menu, the actual upload will be handled separately
        setTimeout(() => {
          const imageUploadSection = document.getElementById('image-upload-section');
          if (imageUploadSection) {
            imageUploadSection.scrollIntoView({ behavior: 'smooth' });
          }
        }, 100);
        formattedText = selectedText; // Keep the selected text as is
        cursorOffset = 0;
        break;
      case 'todo':
        formattedText = `- [ ] ${selectedText}`;
        cursorOffset = selectedText.length ? 0 : 0;
        break;
      case 'bookmark':
        formattedText = `> [!bookmark] ${selectedText || 'Bookmark title'}\n> Bookmark description`;
        cursorOffset = selectedText.length ? 0 : 0;
        break;
      case 'callout':
        formattedText = `> [!note] ${selectedText || 'Note title'}\n> This is a callout block for important information.`;
        cursorOffset = selectedText.length ? 0 : 0;
        break;
      case 'date':
        formattedText = new Date().toLocaleDateString();
        cursorOffset = 0;
        break;
      case 'time':
        formattedText = new Date().toLocaleTimeString();
        cursorOffset = 0;
        break;
      default:
        formattedText = selectedText;
        cursorOffset = 0;
    }

    // Create the final content with the formatted text
    const finalContent = newContent.substring(0, start) + formattedText + newContent.substring(end);
    setContent(finalContent);
    setShowPlaceholder(false);
    setShowBlockMenu(false);

    // Focus back on textarea and set cursor position
    setTimeout(() => {
      textarea.focus();
      const newCursorPos = start + formattedText.length + cursorOffset;
      textarea.setSelectionRange(newCursorPos, newCursorPos);
    }, 0);
  };

  // Handle content change
  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newContent = e.target.value;
    setContent(newContent);
    setShowPlaceholder(newContent === '');

    // Check for '/' at the beginning of a line to show block menu
    const cursorPosition = e.target.selectionStart;
    const textBeforeCursor = newContent.substring(0, cursorPosition);
    const lineStartIndex = textBeforeCursor.lastIndexOf('\n') + 1;
    const currentLine = textBeforeCursor.substring(lineStartIndex);

    // If block menu is already shown, update search term
    if (showBlockMenu && currentLine.startsWith('/')) {
      const searchTerm = currentLine.substring(1); // Remove the '/'
      setBlockMenuSearch(searchTerm);
      setSelectedBlockIndex(0); // Reset selection when search changes
    }
    // If slash is typed at the beginning of a line, show block menu
    else if (currentLine === '/') {
      handleShowBlockMenu();
      setBlockMenuSearch('');
      setSelectedBlockIndex(0);
    }
    // If we're not at the beginning of a line with a slash, hide block menu
    else if (showBlockMenu && !currentLine.startsWith('/')) {
      setShowBlockMenu(false);
      setBlockMenuSearch('');
      setSelectedBlockIndex(0);
    }
  };

  // Add tag
  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag('');
    }
  };

  // Remove tag
  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  // Handle tag input keydown
  const handleTagKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addTag();
    }
  };

  // Set mood
  const handleSetMood = (newMood: 'positive' | 'neutral' | 'negative') => {
    setMood(mood === newMood ? undefined : newMood);
  };

  // Get mood emoji
  const getMoodEmoji = (moodType: 'positive' | 'neutral' | 'negative') => {
    switch (moodType) {
      case 'positive': return '😊';
      case 'neutral': return '😐';
      case 'negative': return '😔';
    }
  };

  // Handle image upload
  const handleImageUploaded = (imageUrl: string) => {
    setImages(prev => [...prev, imageUrl]);
  };

  // Handle image removal
  const handleRemoveImage = (index: number) => {
    setImages(prev => prev.filter((_, i) => i !== index));
  };

  // Handle save
  const handleSave = async () => {
    if (!title.trim()) return;

    setIsSaving(true);
    setError(null);

    try {
      console.log('Preparing to save journal entry...');
      const entryData = {
        title,
        content,
        tags,
        mood,
        dayProfitability,
        pnlAmount,
        attachments: images.map(url => ({
          type: 'image' as const,
          url,
          name: 'Journal image'
        }))
      };

      console.log('Saving journal entry...');

      let result;
      if (entryId) {
        // Update existing entry
        console.log('Updating existing entry with ID:', entryId);
        result = await updateJournalEntry(entryId, entryData);
        console.log('Update result:', result);
      } else {
        // Create new entry
        console.log('Creating new journal entry');
        result = await createJournalEntry(entryData);
        console.log('Create result:', result);
      }

      // Check if we got a valid result
      if (!result) {
        console.warn('No result returned from journal service');
      } else {
        console.log('Journal entry saved successfully:', result);
      }

      setIsSaving(false);

      // Always call onSaveSuccess to refresh the entries list
      if (onSaveSuccess) {
        console.log('Calling onSaveSuccess callback');
        onSaveSuccess();
      }

      // Close the editor
      onCancel();
    } catch (err) {
      console.error('Error saving journal entry:', err);

      // Try to get a meaningful error message
      let errorMessage = 'Failed to save journal entry. Please try again.';
      if (err instanceof Error) {
        errorMessage = err.message || errorMessage;
      }

      setError(errorMessage);
      setIsSaving(false);
    }
  };

  return (
    <div className="max-w-3xl mx-auto">
      {/* Top toolbar - Notion-style dark mode */}
      <div className="flex items-center justify-between mb-4 sticky top-0 bg-[#202020] z-10 py-2">
        <div className="flex items-center gap-2 text-gray-400 text-sm">
          <span>Editing</span>
          <div className="h-4 border-r border-[#3a3a3a] mx-1"></div>
          <div className="flex items-center gap-1">
            <Clock className="h-3.5 w-3.5" />
            <span>Last edited just now</span>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-7 text-xs text-gray-400 hover:bg-[#2e2e2e] transition-colors duration-150"
                  onClick={() => insertFormat('date')}
                >
                  <Calendar className="h-3.5 w-3.5 mr-1" />
                  Today
                </Button>
              </TooltipTrigger>
              <TooltipContent className="bg-[#2a2a2a] text-white text-xs py-1 px-2 border border-[#3a3a3a]">
                Insert current date
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <Button
            variant="outline"
            size="sm"
            className="h-7 text-xs border-[#3a3a3a] text-gray-300 hover:bg-[#2e2e2e] transition-colors duration-150"
            onClick={onCancel}
          >
            Cancel
          </Button>

          <Button
            onClick={handleSave}
            disabled={!title.trim() || isSaving}
            size="sm"
            className="h-7 text-xs bg-blue-600 hover:bg-blue-500 text-white flex items-center gap-1 transition-colors duration-150"
          >
            {isSaving ? (
              <>
                <div className="h-3 w-3 rounded-full border-2 border-white border-t-transparent animate-spin"></div>
                <span>Saving...</span>
              </>
            ) : (
              <span>Save</span>
            )}
          </Button>
        </div>
      </div>

      {/* Error message */}
      {error && (
        <Alert variant="destructive" className="mb-4 bg-red-900/20 border border-red-800">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Notion-style title input - dark mode */}
      <Input
        ref={titleRef}
        placeholder="Untitled"
        value={title}
        onChange={(e) => setTitle(e.target.value)}
        className="text-4xl font-bold border-none focus-visible:ring-0 px-0 text-gray-100 mb-4 w-full placeholder-gray-500 bg-transparent"
      />

      {/* Content area - Notion-style dark mode */}
      <div className="relative mb-8">
        {/* Content textarea */}
        <div className="relative">
          <Textarea
            ref={contentRef}
            id="journal-content"
            placeholder=""
            value={content}
            onChange={handleContentChange}
            className="min-h-[400px] resize-none border-none focus-visible:ring-0 p-0 text-gray-300 placeholder-transparent text-base leading-relaxed bg-transparent"
          />

          {/* Notion-style placeholder - dark mode */}
          {showPlaceholder && (
            <div className="absolute top-0 left-0 text-gray-500 pointer-events-none flex items-center gap-1">
              Type <kbd className="bg-[#2e2e2e] px-1.5 py-0.5 rounded text-xs font-mono border border-[#3a3a3a]">/</kbd> for commands
            </div>
          )}
        </div>

        {/* Block menu (Notion-style slash commands) - dark mode */}
        {showBlockMenu && (
          <div
            className="absolute bg-[#202020] border border-[#3a3a3a] rounded-md shadow-lg z-20 w-72"
            style={{
              top: `${blockMenuPosition.top + 24}px`,
              left: `${blockMenuPosition.left}px`
            }}
          >
            {/* Search header */}
            <div className="p-2 border-b border-[#3a3a3a] bg-[#252525]">
              <div className="relative">
                <Search className="absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 text-gray-500" />
                <input
                  type="text"
                  value={blockMenuSearch}
                  onChange={(e) => {
                    // This is just for direct interaction with the search box
                    // Most search updates will come from the textarea
                    setBlockMenuSearch(e.target.value);
                    setSelectedBlockIndex(0);
                  }}
                  placeholder="Search for a block type..."
                  className="w-full bg-[#2a2a2a] border border-[#3a3a3a] rounded-md py-1 pl-8 pr-2 text-xs text-gray-300 placeholder-gray-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
              </div>
            </div>

            {/* Block types */}
            <div className="max-h-[300px] overflow-y-auto py-1 scrollbar-thin scrollbar-thumb-[#3a3a3a] scrollbar-track-transparent">
              {filteredBlockTypes.length === 0 ? (
                <div className="px-3 py-2 text-sm text-gray-400 text-center">
                  No blocks found
                </div>
              ) : (
                filteredBlockTypes.map((block, index) => (
                  <div
                    key={block.id}
                    className={`px-2 py-1.5 cursor-pointer flex items-center gap-2 transition-colors duration-150 ${
                      index === selectedBlockIndex ? 'bg-[#2e2e2e]' : 'hover:bg-[#2e2e2e]'
                    }`}
                    onClick={() => insertFormat(block.id)}
                    onMouseEnter={() => setSelectedBlockIndex(index)}
                  >
                    <div className="w-6 h-6 flex items-center justify-center text-gray-400">
                      {typeof block.icon === 'function' ? (
                        <block.icon className="h-4 w-4" />
                      ) : (
                        React.createElement(block.icon, { className: "h-4 w-4" })
                      )}
                    </div>
                    <div>
                      <div className="text-sm font-medium text-gray-200">{block.name}</div>
                      <div className="text-xs text-gray-500">{block.description}</div>
                    </div>
                  </div>
                ))
              )}
            </div>

            {/* Footer with keyboard shortcuts */}
            <div className="p-2 border-t border-[#3a3a3a] bg-[#252525] text-xs text-gray-400">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="flex items-center gap-1">
                    <span className="bg-[#3a3a3a] px-1 py-0.5 rounded">↑</span>
                    <span className="bg-[#3a3a3a] px-1 py-0.5 rounded">↓</span>
                    <span>to navigate</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <span className="bg-[#3a3a3a] px-1 py-0.5 rounded">Enter</span>
                    <span>to select</span>
                  </div>
                </div>
                <div className="flex items-center gap-1">
                  <span className="bg-[#3a3a3a] px-1 py-0.5 rounded">Esc</span>
                  <span>to close</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Formatting toolbar (appears when text is selected) - Notion-style dark mode */}
      <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-[#202020] border border-[#3a3a3a] rounded-md shadow-lg flex items-center p-1 z-20 opacity-0 pointer-events-none">
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0 text-gray-300 hover:bg-[#2e2e2e] transition-colors duration-150"
          onClick={() => insertFormat('bold')}
        >
          <Bold className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0 text-gray-300 hover:bg-[#2e2e2e] transition-colors duration-150"
          onClick={() => insertFormat('italic')}
        >
          <Italic className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0 text-gray-300 hover:bg-[#2e2e2e] transition-colors duration-150"
          onClick={() => insertFormat('link')}
        >
          <LinkIcon className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0 text-gray-300 hover:bg-[#2e2e2e] transition-colors duration-150"
          onClick={() => insertFormat('code')}
        >
          <Code className="h-4 w-4" />
        </Button>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 text-gray-300 hover:bg-[#2e2e2e] transition-colors duration-150"
            >
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="center" className="bg-[#202020] border border-[#3a3a3a] shadow-md rounded-md w-48">
            <DropdownMenuItem
              className="text-sm text-gray-200 cursor-pointer focus:bg-[#2e2e2e]"
              onClick={() => insertFormat('heading1')}
            >
              <Heading1 className="h-3.5 w-3.5 mr-2 text-gray-400" />
              Heading 1
            </DropdownMenuItem>
            <DropdownMenuItem
              className="text-sm text-gray-200 cursor-pointer focus:bg-[#2e2e2e]"
              onClick={() => insertFormat('heading2')}
            >
              <Heading2 className="h-3.5 w-3.5 mr-2 text-gray-400" />
              Heading 2
            </DropdownMenuItem>
            <DropdownMenuItem
              className="text-sm text-gray-200 cursor-pointer focus:bg-[#2e2e2e]"
              onClick={() => insertFormat('heading3')}
            >
              <Heading3 className="h-3.5 w-3.5 mr-2 text-gray-400" />
              Heading 3
            </DropdownMenuItem>
            <DropdownMenuSeparator className="bg-[#3a3a3a]" />
            <DropdownMenuItem
              className="text-sm text-gray-200 cursor-pointer focus:bg-[#2e2e2e]"
              onClick={() => insertFormat('quote')}
            >
              <Quote className="h-3.5 w-3.5 mr-2 text-gray-400" />
              Quote
            </DropdownMenuItem>
            <DropdownMenuItem
              className="text-sm text-gray-200 cursor-pointer focus:bg-[#2e2e2e]"
              onClick={() => insertFormat('codeblock')}
            >
              <FileText className="h-3.5 w-3.5 mr-2 text-gray-400" />
              Code block
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Images section - Notion-style dark mode */}
      <div id="image-upload-section" className="mt-8 border-t border-[#3a3a3a] pt-4">
        <div className="flex items-center gap-2 mb-4">
          <ImageIcon className="h-4 w-4 text-gray-400" />
          <span className="text-sm font-medium text-gray-300">Images</span>
          <div className="text-xs text-gray-500 ml-2">
            {images.length}/5 images
          </div>
        </div>

        {/* Image Gallery */}
        {images.length > 0 && (
          <ImageGallery
            images={images}
            onRemoveImage={handleRemoveImage}
            isEditable={true}
          />
        )}

        {/* Image Uploader */}
        {images.length < 5 && (
          <ImageUploader
            onImageUploaded={handleImageUploaded}
            maxImages={5}
            currentImagesCount={images.length}
          />
        )}
      </div>

      {/* Properties section - Notion-style dark mode */}
      <div className="mt-8 border-t border-[#3a3a3a] pt-4">
        <div className="flex items-center gap-2 mb-4">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="h-7 text-xs border-[#3a3a3a] text-gray-300 gap-1 hover:bg-[#2e2e2e] transition-colors duration-150"
              >
                <Plus className="h-3.5 w-3.5" />
                Add property
                <ChevronDown className="h-3.5 w-3.5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" className="bg-[#202020] border border-[#3a3a3a] shadow-md rounded-md w-48">
              <DropdownMenuItem className="text-sm text-gray-200 cursor-pointer focus:bg-[#2e2e2e]">
                <TagIcon className="h-3.5 w-3.5 mr-2 text-gray-400" />
                Tags
              </DropdownMenuItem>
              <DropdownMenuItem className="text-sm text-gray-200 cursor-pointer focus:bg-[#2e2e2e]">
                <Calendar className="h-3.5 w-3.5 mr-2 text-gray-400" />
                Date
              </DropdownMenuItem>
              <DropdownMenuItem className="text-sm text-gray-200 cursor-pointer focus:bg-[#2e2e2e]">
                <Smile className="h-3.5 w-3.5 mr-2 text-gray-400" />
                Mood
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Tags property - Notion-style dark mode */}
        <div className="mb-6">
          <div className="flex items-center gap-2 mb-2">
            <TagIcon className="h-4 w-4 text-gray-400" />
            <span className="text-sm font-medium text-gray-300">Tags</span>
          </div>

          <div className="flex flex-wrap gap-2 items-center">
            {tags.map(tag => (
              <div
                key={tag}
                className="bg-[#2e2e2e] text-gray-200 px-2 py-1 rounded-md text-sm flex items-center gap-1 border border-[#3a3a3a]"
              >
                <div className="h-2 w-2 rounded-full bg-blue-500 mr-1"></div>
                <span>{tag}</span>
                <button
                  onClick={() => removeTag(tag)}
                  className="text-gray-400 hover:text-gray-200 ml-1 transition-colors duration-150"
                >
                  ×
                </button>
              </div>
            ))}

            <div className="relative">
              <Input
                placeholder="Add a tag..."
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                onKeyDown={handleTagKeyDown}
                className="w-[150px] h-8 text-sm border-[#3a3a3a] bg-[#2a2a2a] focus:border-[#4a4a4a] focus:ring-0 text-gray-200 placeholder-gray-500"
              />
              <Button
                size="sm"
                className="absolute right-0 top-0 h-8 bg-blue-600 hover:bg-blue-500 transition-colors duration-150"
                onClick={addTag}
                disabled={!newTag.trim() || tags.includes(newTag.trim())}
              >
                <Plus className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>

        {/* Mood property - Notion-style dark mode */}
        <div className="mb-6">
          <div className="flex items-center gap-2 mb-2">
            <Smile className="h-4 w-4 text-gray-400" />
            <span className="text-sm font-medium text-gray-300">Mood</span>
          </div>

          <div className="flex gap-2">
            <Button
              variant={mood === 'positive' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleSetMood('positive')}
              className={`text-lg ${mood === 'positive' ? 'bg-blue-600 hover:bg-blue-500' : 'border-[#3a3a3a] text-gray-300 hover:bg-[#2e2e2e]'} transition-colors duration-150`}
            >
              😊 {mood === 'positive' && <Check className="h-3 w-3 ml-1" />}
            </Button>
            <Button
              variant={mood === 'neutral' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleSetMood('neutral')}
              className={`text-lg ${mood === 'neutral' ? 'bg-blue-600 hover:bg-blue-500' : 'border-[#3a3a3a] text-gray-300 hover:bg-[#2e2e2e]'} transition-colors duration-150`}
            >
              😐 {mood === 'neutral' && <Check className="h-3 w-3 ml-1" />}
            </Button>
            <Button
              variant={mood === 'negative' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleSetMood('negative')}
              className={`text-lg ${mood === 'negative' ? 'bg-blue-600 hover:bg-blue-500' : 'border-[#3a3a3a] text-gray-300 hover:bg-[#2e2e2e]'} transition-colors duration-150`}
            >
              😔 {mood === 'negative' && <Check className="h-3 w-3 ml-1" />}
            </Button>
          </div>
        </div>

        {/* Day Profitability property - Notion-style dark mode */}
        <div className="mb-6">
          <div className="flex items-center gap-2 mb-2">
            <DollarSign className="h-4 w-4 text-gray-400" />
            <span className="text-sm font-medium text-gray-300">Day Performance</span>
          </div>

          <div className="flex gap-2 mb-3">
            <Button
              variant={dayProfitability === 'profitable' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setDayProfitability(dayProfitability === 'profitable' ? undefined : 'profitable')}
              className={`flex items-center ${dayProfitability === 'profitable' ? 'bg-green-600 hover:bg-green-500' : 'border-[#3a3a3a] text-gray-300 hover:bg-[#2e2e2e]'} transition-colors duration-150`}
            >
              <TrendingUp className="h-4 w-4 mr-1" /> Profitable
              {dayProfitability === 'profitable' && <Check className="h-3 w-3 ml-1" />}
            </Button>
            <Button
              variant={dayProfitability === 'break-even' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setDayProfitability(dayProfitability === 'break-even' ? undefined : 'break-even')}
              className={`flex items-center ${dayProfitability === 'break-even' ? 'bg-blue-600 hover:bg-blue-500' : 'border-[#3a3a3a] text-gray-300 hover:bg-[#2e2e2e]'} transition-colors duration-150`}
            >
              <Minus className="h-4 w-4 mr-1" /> Break-even
              {dayProfitability === 'break-even' && <Check className="h-3 w-3 ml-1" />}
            </Button>
            <Button
              variant={dayProfitability === 'unprofitable' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setDayProfitability(dayProfitability === 'unprofitable' ? undefined : 'unprofitable')}
              className={`flex items-center ${dayProfitability === 'unprofitable' ? 'bg-red-600 hover:bg-red-500' : 'border-[#3a3a3a] text-gray-300 hover:bg-[#2e2e2e]'} transition-colors duration-150`}
            >
              <TrendingDown className="h-4 w-4 mr-1" /> Unprofitable
              {dayProfitability === 'unprofitable' && <Check className="h-3 w-3 ml-1" />}
            </Button>
          </div>

          {/* Optional P&L amount input */}
          {dayProfitability && (
            <div className="mt-2">
              <div className="text-xs text-gray-400 mb-2">P&L Amount (optional)</div>
              <Input
                type="number"
                placeholder="Enter P&L amount"
                value={pnlAmount || ''}
                onChange={(e) => setPnlAmount(e.target.value ? parseFloat(e.target.value) : undefined)}
                className="bg-[#1a1a1a] border-[#3a3a3a] text-gray-200 w-full max-w-xs"
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default JournalEntryEditor;
