---
description: 
globs: 
alwaysApply: true
---
# Project Guidelines for TradeChampionX

When working on the TradeChampionX project, follow these specific guidelines:

- Always create JSDoc comments for each component or code module
- Create README files for directories where needed
- Use the established component structure pattern from src/docs/ComponentTemplate.tsx
- Add @status and @version tags to all components (experimental, beta, stable, or deprecated)
- Follow the style guide in src/docs/STYLE_GUIDE.md
- Maintain feature-based organization with proper index files
- Remove AI-related content from the project as long as it doesn't affect functionality
- Focus on text visibility issues, ensuring good contrast (dark text should not be on dark backgrounds)
- Prefer the logo icon without an underline
- Only create backend files when they are immediately useful
- Use proper syntax for running commands rather than chaining with &&

When suggesting improvements, focus on keeping the codebase organized and easy to read, with consistent component structure, index files for features, adherence to the style guide, and proper component status indicators.

# Deployment Documentation

When working on the TradeChampionX project, maintain and update the Instructions/DEPLOYMENT_CHECKLIST.md document with any new deployment-related information. This includes:

- Adding new environment variables that will need to be updated in production
- Documenting configuration changes for third-party services like Clerk
- Adding new server setup requirements
- Updating deployment steps for new features
- Adding security considerations
- Documenting any database changes that require special handling during deployment

Whenever implementing a feature that would affect deployment, proactively update the checklist without being asked. Treat the DEPLOYMENT_CHECKLIST.md as a living document that should always reflect the current state of deployment requirements.


. Design comprehensive, well-documented RESTful APIs with consistent formats
2. Build modular components with separation of concerns
3. Structure data with future UI needs in mind
4. Optimize for performance with efficient queries and caching
5. Implement robust authentication and fine-grained permissions
6. Support real-time capabilities for trading features
7. Document all APIs thoroughly with examples
8. Consider UI components when designing data models
9. Implement feature flags for gradual rollout



# Cursor AI Rules and Guidelines

## General Rules
- Always read and understand all documentation files before starting implementation
- Follow the PRD step by step
- Keep the project structure organized
- Update status.md after completing each step
- Ask for clarification when requirements are unclear

## File Purposes
- requirements.md: Source of truth for project requirements
- prd.md: Product specification and features
- techstack.md: Technical decisions and architecture
- backend.md: Backend implementation guide
- frontend.md: Frontend implementation guide
- flow.md: System and user flow documentation
- status.md: Progress tracking and milestones

## Best Practices
- Maintain consistent code style
- Write clear comments and documentation
- Follow the defined architecture
- Test thoroughly before marking tasks complete
- Keep the status.md file updateda

## hey even if using placeholder or mock data for now but build the project in a way that it's easy to implement the real backend and api later on 

