import React, { useState } from 'react';
import { X, Maximize2, ExternalLink } from 'lucide-react';
import { Button } from '@/common/components/ui';

interface ImageGalleryProps {
  images: string[];
  onRemoveImage?: (index: number) => void;
  isEditable?: boolean;
}

/**
 * Image gallery component for displaying images in journal entries
 */
const ImageGallery: React.FC<ImageGalleryProps> = ({
  images,
  onRemoveImage,
  isEditable = false,
}) => {
  const [expandedImage, setExpandedImage] = useState<string | null>(null);

  if (!images || images.length === 0) {
    return null;
  }

  return (
    <div className="my-4">
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
        {images.map((imageUrl, index) => (
          <div
            key={`${imageUrl}-${index}`}
            className="relative group rounded-md overflow-hidden border border-[#3a3a3a] bg-[#252525]"
          >
            <div className="aspect-w-16 aspect-h-12">
              <img
                src={imageUrl}
                alt={`Journal entry image ${index + 1}`}
                className="object-cover w-full h-full cursor-pointer hover:opacity-90 transition-opacity"
                onClick={() => setExpandedImage(imageUrl)}
              />
            </div>
            
            {isEditable && onRemoveImage && (
              <Button
                variant="destructive"
                size="sm"
                className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                onClick={(e) => {
                  e.stopPropagation();
                  onRemoveImage(index);
                }}
              >
                <X className="h-3 w-3" />
              </Button>
            )}
            
            <Button
              variant="secondary"
              size="sm"
              className="absolute bottom-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
              onClick={() => setExpandedImage(imageUrl)}
            >
              <Maximize2 className="h-3 w-3" />
            </Button>
          </div>
        ))}
      </div>

      {/* Image Modal */}
      {expandedImage && (
        <div
          className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4"
          onClick={() => setExpandedImage(null)}
        >
          <div className="relative max-w-4xl max-h-[90vh] w-full">
            <div className="absolute top-2 right-2 flex gap-2 z-10">
              <Button
                variant="secondary"
                size="sm"
                className="h-8 w-8 p-0 bg-black/50 hover:bg-black/70 border-0"
                onClick={(e) => {
                  e.stopPropagation();
                  window.open(expandedImage, '_blank');
                }}
              >
                <ExternalLink className="h-4 w-4" />
              </Button>
              <Button
                variant="destructive"
                size="sm"
                className="h-8 w-8 p-0 bg-black/50 hover:bg-black/70 border-0"
                onClick={() => setExpandedImage(null)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            <img
              src={expandedImage}
              alt="Expanded view"
              className="max-h-[90vh] max-w-full object-contain mx-auto rounded-md"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageGallery;
