import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Award, Clock, TrendingUp, Target, Zap, ChevronDown, Trophy, CheckCircle2 } from 'lucide-react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  Button,
  Badge,
  Progress
} from '@/common/components/ui';
import { useChallengeContext } from '../context/ChallengeContext';

// Achievement interface
interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: React.ElementType;
  progress: number;
  maxProgress: number;
  completed: boolean;
  date?: Date;
  rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary';
}

/**
 * Achievement Milestones component
 *
 * Displays trading achievements and milestones with visual styling.
 *
 * @component
 * @status experimental
 * @version 1.0.0
 * @example
 * <AchievementMilestones />
 */
const AchievementMilestones: React.FC = () => {
  const [expanded, setExpanded] = useState(false);

  // Mock achievement data
  const achievements: Achievement[] = [
    {
      id: '1',
      name: 'First Blood',
      description: 'Complete your first profitable trade',
      icon: TrendingUp,
      progress: 1,
      maxProgress: 1,
      completed: true,
      date: new Date(2023, 5, 1),
      rarity: 'common'
    },
    {
      id: '2',
      name: 'Winning Streak',
      description: 'Complete 5 profitable trades in a row',
      icon: Zap,
      progress: 3,
      maxProgress: 5,
      completed: false,
      rarity: 'uncommon'
    },
    {
      id: '3',
      name: 'Diamond Hands',
      description: 'Hold a profitable position for more than 24 hours',
      icon: Clock,
      progress: 1,
      maxProgress: 1,
      completed: true,
      date: new Date(2023, 5, 3),
      rarity: 'rare'
    },
    {
      id: '4',
      name: 'Risk Manager',
      description: 'Complete 10 trades with less than 1% risk per trade',
      icon: Target,
      progress: 7,
      maxProgress: 10,
      completed: false,
      rarity: 'uncommon'
    },
    {
      id: '5',
      name: 'Challenge Master',
      description: 'Win 3 trading challenges',
      icon: Trophy,
      progress: 1,
      maxProgress: 3,
      completed: false,
      rarity: 'legendary'
    },
  ];

  // Get rarity style
  const getRarityStyle = (rarity: string) => {
    switch (rarity) {
      case 'common':
        return 'border-blue-500/40 bg-blue-500/10';
      case 'uncommon':
        return 'border-green-500/40 bg-green-500/10';
      case 'rare':
        return 'border-purple-500/40 bg-purple-500/10';
      case 'epic':
        return 'border-amber-500/40 bg-amber-500/10';
      case 'legendary':
        return 'border-amber-500/40 bg-gradient-to-b from-amber-500/20 to-amber-800/10';
      default:
        return 'border-gray-500/40 bg-gray-500/10';
    }
  };

  // Get rarity text color
  const getRarityTextColor = (rarity: string) => {
    switch (rarity) {
      case 'common':
        return 'text-blue-400';
      case 'uncommon':
        return 'text-green-400';
      case 'rare':
        return 'text-purple-400';
      case 'epic':
        return 'text-amber-400';
      case 'legendary':
        return 'text-amber-300';
      default:
        return 'text-gray-400';
    }
  };

  // Get rarity badge style
  const getRarityBadgeStyle = (rarity: string) => {
    switch (rarity) {
      case 'common':
        return 'bg-blue-500/20 text-blue-300 border-blue-500/30';
      case 'uncommon':
        return 'bg-green-500/20 text-green-300 border-green-500/30';
      case 'rare':
        return 'bg-purple-500/20 text-purple-300 border-purple-500/30';
      case 'epic':
        return 'bg-amber-500/20 text-amber-300 border-amber-500/30';
      case 'legendary':
        return 'bg-gradient-to-r from-amber-500/20 to-amber-700/20 text-amber-300 border-amber-500/30';
      default:
        return 'bg-gray-500/20 text-gray-300 border-gray-500/30';
    }
  };

  // Calculate progress percentage
  const getProgressPercentage = (achievement: Achievement) => {
    return (achievement.progress / achievement.maxProgress) * 100;
  };

  // Format date to relative time
  const formatRelativeTime = (date?: Date) => {
    if (!date) return '';
    
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
    
    return date.toLocaleDateString();
  };

  // Sort achievements: completed first, then by rarity
  const sortedAchievements = [...achievements].sort((a, b) => {
    if (a.completed && !b.completed) return -1;
    if (!a.completed && b.completed) return 1;
    
    const rarityOrder = { legendary: 0, epic: 1, rare: 2, uncommon: 3, common: 4 };
    return rarityOrder[a.rarity] - rarityOrder[b.rarity];
  });

  // Filter achievements to show only recent or in progress ones when not expanded
  const displayedAchievements = expanded 
    ? sortedAchievements 
    : sortedAchievements.filter(a => a.completed || a.progress > 0).slice(0, 3);

  // Calculate achievements completion
  const completedCount = achievements.filter(a => a.completed).length;
  const completionPercentage = (completedCount / achievements.length) * 100;

  return (
    <Card className="border-blue-500/20 bg-[#0f1c2e]/60 backdrop-blur-sm">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg font-medium">Achievements</CardTitle>
            <CardDescription className="text-xs text-gray-400">
              Your trading milestones and achievements
            </CardDescription>
          </div>
          <div className="flex items-center">
            <Badge className="bg-blue-500/20 text-blue-300 border border-blue-500/30 flex items-center">
              <span>{completedCount}/{achievements.length}</span>
            </Badge>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-2">
        {/* Achievement progress bar */}
        <div className="mb-3">
          <div className="flex items-center justify-between mb-1.5">
            <span className="text-xs text-gray-400">Completion</span>
            <span className="text-xs text-white font-medium">{Math.round(completionPercentage)}%</span>
          </div>
          <Progress value={completionPercentage} className="h-2 bg-blue-900/50" />
        </div>
        
        {/* Achievements list */}
        <div className="space-y-3">
          {displayedAchievements.map((achievement) => (
            <motion.div
              key={achievement.id}
              className={`rounded-lg border ${getRarityStyle(achievement.rarity)} overflow-hidden`}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <div className="p-3">
                <div className="flex items-start">
                  <div className={`rounded-full p-2 ${getRarityStyle(achievement.rarity)} mr-3`}>
                    <achievement.icon className={`h-5 w-5 ${getRarityTextColor(achievement.rarity)}`} />
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center mb-1">
                      <h3 className="text-sm font-medium text-white mr-2">{achievement.name}</h3>
                      <Badge className={`${getRarityBadgeStyle(achievement.rarity)} text-xs capitalize`}>
                        {achievement.rarity}
                      </Badge>
                    </div>
                    
                    <p className="text-xs text-gray-400 mb-2">{achievement.description}</p>
                    
                    {achievement.completed ? (
                      <div className="flex items-center">
                        <CheckCircle2 className="h-3.5 w-3.5 text-green-400 mr-1.5" />
                        <span className="text-xs text-green-400">Completed {formatRelativeTime(achievement.date)}</span>
                      </div>
                    ) : (
                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-xs text-gray-400">Progress</span>
                          <span className="text-xs text-white">{achievement.progress}/{achievement.maxProgress}</span>
                        </div>
                        <Progress value={getProgressPercentage(achievement)} className="h-1.5 bg-blue-900/50" />
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
        
        {/* Show more/less button */}
        {achievements.length > 3 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setExpanded(!expanded)}
            className="w-full mt-3 text-blue-300 hover:text-blue-200 hover:bg-blue-500/10"
          >
            {expanded ? 'Show Less' : `Show ${achievements.length - Math.min(3, displayedAchievements.length)} More`}
            <ChevronDown className={`ml-2 h-4 w-4 transition-transform ${expanded ? 'rotate-180' : ''}`} />
          </Button>
        )}
        
        {/* Empty state */}
        {achievements.length === 0 && (
          <div className="flex flex-col items-center justify-center py-6">
            <Award className="h-12 w-12 text-blue-500/50 mb-3" />
            <p className="text-gray-400 text-sm text-center max-w-xs">
              Complete trading milestones to earn achievements and track your progress.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default AchievementMilestones; 