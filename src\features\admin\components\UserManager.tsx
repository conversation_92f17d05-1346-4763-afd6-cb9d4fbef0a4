/**
 * User Manager Component
 * @description Component for managing users in the admin panel
 * @version 1.0.0
 * @status stable
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Search,
  Filter,
  RefreshCw,
  UserPlus,
  Edit,
  Trash2,
  Ban,
  CheckCircle,
  Wallet,
  Eye,
  ArrowUpDown,
  ChevronDown,
  MoreHorizontal,
  Users
} from 'lucide-react';
import { Checkbox } from '@/common/components/ui/checkbox';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/common/components/ui/card';
import { Button } from '@/common/components/ui/button';
import { Input } from '@/common/components/ui/input';
import { Badge } from '@/common/components/ui/badge';
import { Skeleton } from '@/common/components/ui/skeleton';
import { useToast } from '@/common/components/ui/use-toast';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/common/components/ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/common/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/common/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/common/components/ui/select';
import { Label } from '@/common/components/ui/label';
import { useSimpleDialogCleanup } from '@/common/hooks/useDialogCleanup';
import { useUsers } from '../hooks/useAdminData';
import BatchActionDialog from './BatchActionDialog';

// User type definition
interface User {
  id: string;
  email: string;
  username: string;
  discordUsername?: string;
  walletCredit: number;
  status: 'active' | 'banned' | 'deleted';
  signupDate: string;
  cryptoAddress?: string;
}

// Wallet credit adjustment dialog props
interface WalletCreditDialogProps {
  userId: string;
  username: string;
  currentCredit: number;
  onCreditAdjusted: () => void;
}

/**
 * Wallet credit adjustment dialog component
 */
const WalletCreditDialog: React.FC<WalletCreditDialogProps> = ({
  userId,
  username,
  currentCredit,
  onCreditAdjusted
}) => {
  const { toast } = useToast();
  const [amount, setAmount] = useState<number>(0);
  const [reason, setReason] = useState<string>('');
  const [operation, setOperation] = useState<'add' | 'deduct'>('add');
  const [loading, setLoading] = useState<boolean>(false);
  const [open, setOpen] = useState<boolean>(false);

  // Use cleanup hook to prevent UI freezing
  const handleOpenChange = useSimpleDialogCleanup(setOpen);

  const handleSubmit = async () => {
    if (!amount || amount <= 0) {
      toast({
        title: 'Invalid amount',
        description: 'Please enter a valid amount greater than 0',
        variant: 'destructive',
      });
      return;
    }

    if (!reason) {
      toast({
        title: 'Reason required',
        description: 'Please provide a reason for this adjustment',
        variant: 'destructive',
      });
      return;
    }

    try {
      setLoading(true);

      // Call the API to adjust credits
      const success = await adjustUserCredits(userId, amount, reason, operation);

      if (success) {
        handleOpenChange(false);
        onCreditAdjusted();
      }
    } catch (error) {
      console.error(`Error ${operation === 'add' ? 'adding' : 'deducting'} credits:`, error);
      toast({
        title: 'Error',
        description: `Failed to ${operation === 'add' ? 'add' : 'deduct'} credits`,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="flex items-center gap-2">
          <Wallet className="h-4 w-4" />
          Adjust Credits
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px] bg-forex-darker border-forex-border">
        <DialogHeader>
          <DialogTitle>Adjust Wallet Credits</DialogTitle>
          <DialogDescription>
            Current balance: ${currentCredit}
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="operation" className="text-right">
              Operation
            </Label>
            <Select
              value={operation}
              onValueChange={(value) => setOperation(value as 'add' | 'deduct')}
            >
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="Select operation" />
              </SelectTrigger>
              <SelectContent className="bg-forex-darker border-forex-border">
                <SelectItem value="add">Add Credits</SelectItem>
                <SelectItem value="deduct">Deduct Credits</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="amount" className="text-right">
              Amount
            </Label>
            <Input
              id="amount"
              type="number"
              min="0"
              step="0.01"
              value={amount || ''}
              onChange={(e) => setAmount(parseFloat(e.target.value) || 0)}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="reason" className="text-right">
              Reason
            </Label>
            <Input
              id="reason"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              className="col-span-3"
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => handleOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              'Confirm'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

/**
 * User manager component
 */
const UserManager: React.FC = () => {
  const { toast } = useToast();
  const [loading, setLoading] = useState<boolean>(true);
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('signupDate');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [batchActionOpen, setBatchActionOpen] = useState<boolean>(false);

  // Use the users hook
  const {
    users: apiUsers,
    loading: apiLoading,
    error,
    fetchUsers,
    updateUserStatus,
    adjustUserCredits,
    setParams
  } = useUsers();

  // Set users when API data is loaded
  useEffect(() => {
    if (apiUsers && apiUsers.length > 0) {
      setUsers(apiUsers);
      setFilteredUsers(apiUsers);
    }
  }, [apiUsers]);

  // Set loading state from API
  useEffect(() => {
    setLoading(apiLoading);
  }, [apiLoading]);

  // Filter and sort users
  useEffect(() => {
    let result = [...users];

    // Apply status filter
    if (statusFilter !== 'all') {
      result = result.filter(user => user.status === statusFilter);
    }

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      result = result.filter(user =>
        user.username.toLowerCase().includes(term) ||
        user.email.toLowerCase().includes(term) ||
        (user.discordUsername && user.discordUsername.toLowerCase().includes(term))
      );
    }

    // Apply sorting
    result.sort((a, b) => {
      let valueA, valueB;

      switch (sortBy) {
        case 'username':
          valueA = a.username.toLowerCase();
          valueB = b.username.toLowerCase();
          break;
        case 'email':
          valueA = a.email.toLowerCase();
          valueB = b.email.toLowerCase();
          break;
        case 'walletCredit':
          valueA = a.walletCredit;
          valueB = b.walletCredit;
          break;
        case 'signupDate':
        default:
          valueA = new Date(a.signupDate).getTime();
          valueB = new Date(b.signupDate).getTime();
          break;
      }

      if (sortOrder === 'asc') {
        return valueA > valueB ? 1 : -1;
      } else {
        return valueA < valueB ? 1 : -1;
      }
    });

    setFilteredUsers(result);
  }, [users, searchTerm, statusFilter, sortBy, sortOrder]);

  // Toggle sort order
  const toggleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  // Handle user status change
  const handleStatusChange = async (userId: string, newStatus: 'active' | 'banned') => {
    await updateUserStatus(userId, newStatus);
  };

  // Handle credit adjustment
  const handleCreditAdjusted = async () => {
    // Refresh user data
    await fetchUsers();
  };

  // Handle user selection for batch operations
  const handleUserSelection = (userId: string) => {
    setSelectedUsers(prev => {
      if (prev.includes(userId)) {
        return prev.filter(id => id !== userId);
      } else {
        return [...prev, userId];
      }
    });
  };

  // Handle select all users
  const handleSelectAll = () => {
    if (selectedUsers.length === filteredUsers.length) {
      // If all are selected, deselect all
      setSelectedUsers([]);
    } else {
      // Otherwise, select all
      setSelectedUsers(filteredUsers.map(user => user.id));
    }
  };

  // Handle batch status change
  const handleBatchStatusChange = async (newStatus: 'active' | 'banned') => {
    if (selectedUsers.length === 0) {
      toast({
        title: 'No users selected',
        description: 'Please select at least one user to perform this action',
        variant: 'destructive',
      });
      return;
    }

    try {
      // Show loading toast
      toast({
        title: 'Processing',
        description: `Updating status for ${selectedUsers.length} users...`,
      });

      // Process each user in sequence
      let successCount = 0;
      for (const userId of selectedUsers) {
        const success = await updateUserStatus(userId, newStatus);
        if (success) successCount++;
      }

      // Show success toast
      toast({
        title: 'Success',
        description: `Updated status for ${successCount} out of ${selectedUsers.length} users`,
      });

      // Clear selection
      setSelectedUsers([]);

      // Refresh user data
      await fetchUsers();
    } catch (error) {
      console.error('Error updating batch user status:', error);
      toast({
        title: 'Error',
        description: 'Failed to update user status',
        variant: 'destructive',
      });
    }
  };

  // Handle batch credit adjustment
  const handleBatchCreditAdjustment = async (amount: number, reason: string, operation: 'add' | 'deduct') => {
    if (selectedUsers.length === 0) {
      toast({
        title: 'No users selected',
        description: 'Please select at least one user to perform this action',
        variant: 'destructive',
      });
      return;
    }

    if (!amount || amount <= 0) {
      toast({
        title: 'Invalid amount',
        description: 'Please enter a valid amount greater than 0',
        variant: 'destructive',
      });
      return;
    }

    if (!reason) {
      toast({
        title: 'Reason required',
        description: 'Please provide a reason for this adjustment',
        variant: 'destructive',
      });
      return;
    }

    try {
      // Show loading toast
      toast({
        title: 'Processing',
        description: `Adjusting credits for ${selectedUsers.length} users...`,
      });

      // Process each user in sequence
      let successCount = 0;
      for (const userId of selectedUsers) {
        const success = await adjustUserCredits(userId, amount, `${reason} (Batch operation)`, operation);
        if (success) successCount++;
      }

      // Show success toast
      toast({
        title: 'Success',
        description: `Adjusted credits for ${successCount} out of ${selectedUsers.length} users`,
      });

      // Clear selection
      setSelectedUsers([]);

      // Close batch action dialog
      setBatchActionOpen(false);

      // Refresh user data
      await fetchUsers();
    } catch (error) {
      console.error('Error adjusting batch credits:', error);
      toast({
        title: 'Error',
        description: 'Failed to adjust credits',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">User Management</h1>
        <div className="flex items-center gap-2">
          {selectedUsers.length > 0 && (
            <Button
              variant="default"
              size="sm"
              className="flex items-center gap-2"
              onClick={() => setBatchActionOpen(true)}
            >
              <Users className="h-4 w-4" />
              Batch Actions ({selectedUsers.length})
            </Button>
          )}
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
            onClick={() => fetchUsers()}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            {loading ? 'Loading...' : 'Refresh'}
          </Button>
        </div>
      </div>

      {/* Batch Action Dialog */}
      <BatchActionDialog
        selectedCount={selectedUsers.length}
        onStatusChange={handleBatchStatusChange}
        onCreditAdjustment={handleBatchCreditAdjustment}
        open={batchActionOpen}
        onOpenChange={setBatchActionOpen}
      />

      {/* Filters */}
      <Card className="bg-forex-darker border-forex-border">
        <CardHeader className="pb-3">
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-forex-muted" />
                <Input
                  placeholder="Search by username, email, or Discord..."
                  className="pl-9"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            <div>
              <Select
                value={statusFilter}
                onValueChange={setStatusFilter}
              >
                <SelectTrigger className="w-[180px]">
                  <div className="flex items-center gap-2">
                    <Filter className="h-4 w-4" />
                    <SelectValue placeholder="Filter by status" />
                  </div>
                </SelectTrigger>
                <SelectContent className="bg-forex-darker border-forex-border">
                  <SelectItem value="all">All Users</SelectItem>
                  <SelectItem value="active">Active Users</SelectItem>
                  <SelectItem value="banned">Banned Users</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Users Table */}
      <Card className="bg-forex-darker border-forex-border">
        <CardHeader className="pb-3">
          <CardTitle>Users</CardTitle>
          <CardDescription>
            {filteredUsers.length} {filteredUsers.length === 1 ? 'user' : 'users'} found
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, index) => (
                <div key={index} className="flex items-center space-x-4">
                  <Skeleton className="h-12 w-12 rounded-full" />
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-[250px]" />
                    <Skeleton className="h-4 w-[200px]" />
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="rounded-md border border-forex-border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[50px]">
                      <Checkbox
                        checked={selectedUsers.length > 0 && selectedUsers.length === filteredUsers.length}
                        onCheckedChange={handleSelectAll}
                        aria-label="Select all"
                      />
                    </TableHead>
                    <TableHead className="w-[250px]">
                      <Button
                        variant="ghost"
                        onClick={() => toggleSort('username')}
                        className="flex items-center gap-1 p-0 hover:bg-transparent"
                      >
                        Username
                        <ArrowUpDown className="h-4 w-4" />
                      </Button>
                    </TableHead>
                    <TableHead>
                      <Button
                        variant="ghost"
                        onClick={() => toggleSort('email')}
                        className="flex items-center gap-1 p-0 hover:bg-transparent"
                      >
                        Email
                        <ArrowUpDown className="h-4 w-4" />
                      </Button>
                    </TableHead>
                    <TableHead>
                      <Button
                        variant="ghost"
                        onClick={() => toggleSort('walletCredit')}
                        className="flex items-center gap-1 p-0 hover:bg-transparent"
                      >
                        Wallet Credit
                        <ArrowUpDown className="h-4 w-4" />
                      </Button>
                    </TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>
                      <Button
                        variant="ghost"
                        onClick={() => toggleSort('signupDate')}
                        className="flex items-center gap-1 p-0 hover:bg-transparent"
                      >
                        Signup Date
                        <ArrowUpDown className="h-4 w-4" />
                      </Button>
                    </TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredUsers.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8 text-forex-muted">
                        No users found
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredUsers.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell>
                          <Checkbox
                            checked={selectedUsers.includes(user.id)}
                            onCheckedChange={() => handleUserSelection(user.id)}
                            aria-label={`Select ${user.username}`}
                          />
                        </TableCell>
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            <div className="h-8 w-8 rounded-full bg-forex-primary flex items-center justify-center text-white">
                              {user.username.charAt(0).toUpperCase()}
                            </div>
                            <div>
                              <div>{user.username}</div>
                              {user.discordUsername && (
                                <div className="text-xs text-forex-muted">{user.discordUsername}</div>
                              )}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{user.email}</TableCell>
                        <TableCell>${user.walletCredit}</TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              user.status === 'active' ? 'success' :
                              user.status === 'banned' ? 'destructive' : 'outline'
                            }
                          >
                            {user.status.charAt(0).toUpperCase() + user.status.slice(1)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {new Date(user.signupDate).toLocaleDateString()}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <WalletCreditDialog
                              userId={user.id}
                              username={user.username}
                              currentCredit={user.walletCredit}
                              onCreditAdjusted={handleCreditAdjusted}
                            />
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent className="bg-forex-darker border-forex-border">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                  <Eye className="h-4 w-4 mr-2" />
                                  View Details
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Edit className="h-4 w-4 mr-2" />
                                  Edit User
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                {user.status === 'active' ? (
                                  <DropdownMenuItem onClick={() => handleStatusChange(user.id, 'banned')}>
                                    <Ban className="h-4 w-4 mr-2" />
                                    Ban User
                                  </DropdownMenuItem>
                                ) : (
                                  <DropdownMenuItem onClick={() => handleStatusChange(user.id, 'active')}>
                                    <CheckCircle className="h-4 w-4 mr-2" />
                                    Activate User
                                  </DropdownMenuItem>
                                )}
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default UserManager;
