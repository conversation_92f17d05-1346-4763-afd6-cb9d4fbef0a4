/**
 * Dashboard Wallet Page
 * 
 * Displays wallet information, credit history, and transaction details within the dashboard layout.
 * 
 * @status experimental
 * @version 1.0.0
 */

import React from 'react';
import { DashboardLayout } from '@/features/dashboard';
import { WalletCreditHistory } from '@/features/wallet';
import { Toaster } from '@/common/components/ui/sonner';
import { motion } from 'framer-motion';

/**
 * Dashboard Wallet Page component
 * Shows wallet management within dashboard layout
 * 
 * @returns DashboardWalletPage component
 */
const WalletPage: React.FC = () => {
  return (
    <DashboardLayout>
      <motion.div 
        className="space-y-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-3xl font-bold tracking-tight">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-blue-600">
              Wallet
            </span>
          </h1>
        </div>
        
        <WalletCreditHistory />
      </motion.div>
      <Toaster position="top-right" />
    </DashboardLayout>
  );
};

export default WalletPage; 