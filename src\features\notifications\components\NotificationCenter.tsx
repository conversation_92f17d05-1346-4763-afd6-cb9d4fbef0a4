/**
 * Notification Center Component
 * @description Component for displaying user notifications
 * @version 1.0.0
 * @status stable
 */

import React, { useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from '@/app/store';
import {
  <PERSON>,
  Check,
  Trash2,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  AlertTriangle,
  Info
} from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
  Button,
  ScrollArea,
  Badge,
  Separator
} from '@/common/components/ui';
import {
  fetchNotifications,
  markAsRead,
  markAllAsRead,
  deleteNotification,
  selectNotifications,
  selectUnreadCount,
  selectNotificationsLoading
} from '../notificationsSlice';
import { formatDistanceToNow } from 'date-fns';

/**
 * Notification center component
 * @returns JSX element
 */
const NotificationCenter: React.FC = () => {
  const dispatch = useAppDispatch();
  const notifications = useAppSelector(selectNotifications);
  const unreadCount = useAppSelector(selectUnreadCount);
  const loading = useAppSelector(selectNotificationsLoading);
  const [open, setOpen] = useState(false);

  // Fetch notifications when popover opens
  useEffect(() => {
    if (open) {
      dispatch(fetchNotifications({ limit: 10 }));
    }
  }, [open, dispatch]);

  // Handle mark as read
  const handleMarkAsRead = (id: number) => {
    dispatch(markAsRead(id));
  };

  // Handle mark all as read
  const handleMarkAllAsRead = () => {
    dispatch(markAllAsRead());
  };

  // Handle delete notification
  const handleDelete = (id: number) => {
    dispatch(deleteNotification(id));
  };

  // Get priority icon
  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'normal':
        return <Info className="h-4 w-4 text-blue-500" />;
      case 'low':
        return <Info className="h-4 w-4 text-gray-500" />;
      default:
        return <Info className="h-4 w-4 text-blue-500" />;
    }
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge
              variant="destructive"
              className="absolute -top-1 -right-1 px-1 min-w-[18px] h-[18px] text-[10px] flex items-center justify-center"
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="end">
        <div className="flex items-center justify-between p-4 bg-forex-darker text-white">
          <h3 className="font-medium">Notifications</h3>
          {unreadCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleMarkAllAsRead}
              className="h-8 text-xs text-forex-muted hover:text-white"
            >
              <CheckCheck className="h-3.5 w-3.5 mr-1" />
              Mark all as read
            </Button>
          )}
        </div>
        <Separator />
        <ScrollArea className="h-[300px]">
          {loading ? (
            <div className="flex items-center justify-center h-20">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-forex-primary"></div>
            </div>
          ) : notifications.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-20 text-forex-muted">
              <Bell className="h-5 w-5 mb-2 opacity-50" />
              <p className="text-sm">No notifications</p>
            </div>
          ) : (
            <div className="divide-y divide-forex-border">
              {notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-4 relative ${notification.seen ? 'bg-forex-dark' : 'bg-forex-darker'}`}
                >
                  <div className="flex items-start gap-3">
                    <div className="mt-0.5">
                      {getPriorityIcon(notification.priority)}
                    </div>
                    <div className="flex-1 space-y-1">
                      <div className="flex items-start justify-between">
                        <p className={`text-sm font-medium ${notification.seen ? 'text-forex-muted' : 'text-white'}`}>
                          {notification.title}
                        </p>
                        <div className="flex items-center gap-1">
                          {!notification.seen && (
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6"
                              onClick={() => handleMarkAsRead(notification.id)}
                            >
                              <Check className="h-3.5 w-3.5 text-forex-muted hover:text-white" />
                            </Button>
                          )}
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6"
                            onClick={() => handleDelete(notification.id)}
                          >
                            <Trash2 className="h-3.5 w-3.5 text-forex-muted hover:text-red-500" />
                          </Button>
                        </div>
                      </div>
                      <p className="text-xs text-forex-muted">
                        {notification.description}
                      </p>
                      <div className="flex items-center text-[10px] text-forex-muted mt-2">
                        <Clock className="h-3 w-3 mr-1" />
                        {formatDistanceToNow(new Date(notification.timestamp), { addSuffix: true })}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </PopoverContent>
    </Popover>
  );
};

export default NotificationCenter;
