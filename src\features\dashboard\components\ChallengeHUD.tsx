import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Trophy, Clock, TrendingUp, Target, AlertTriangle, ChevronRight, Loader2 } from 'lucide-react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  Button,
  Badge,
  Progress,
  Skeleton
} from '@/common/components/ui';
import { useChallengeContext, ChallengeType } from '../context/ChallengeContext';
import ErrorBoundary from '@/common/components/ErrorBoundary';

/**
 * Challenge HUD component
 *
 * Displays a gaming-inspired HUD for the current challenge with countdown timers,
 * rank indicators, and milestone tracking.
 *
 * @component
 * @status experimental
 * @version 1.0.0
 * @example
 * <ChallengeHUD />
 */
const ChallengeHUD: React.FC = () => {
  const { selectedChallenge, isAggregatedView, challenges, isLoading, error } = useChallengeContext();
  const [showDetails, setShowDetails] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState({ days: 0, hours: 0, minutes: 0, seconds: 0 });

  // Set up countdown timer
  useEffect(() => {
    if (!selectedChallenge) return;

    // Update timer
    const updateTimer = () => {
      const now = new Date();
      const endDate = selectedChallenge.endDate;
      const diffMs = endDate.getTime() - now.getTime();

      if (diffMs <= 0) {
        setTimeRemaining({ days: 0, hours: 0, minutes: 0, seconds: 0 });
        return;
      }

      const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
      const hours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((diffMs % (1000 * 60)) / 1000);

      setTimeRemaining({ days, hours, minutes, seconds });
    };

    // Initial update
    updateTimer();

    // Set interval for updates
    const interval = setInterval(updateTimer, 1000);

    // Clean up
    return () => clearInterval(interval);
  }, [selectedChallenge]);



  // Calculate rank movement
  const getRankMovement = (): number => {
    if (!selectedChallenge) return 0;

    // Mock data - in a real implementation, this would come from the backend
    // Positive number means rank improved (moved up), negative means rank worsened
    return 3;
  };

  // Get challenge type badge color
  const getChallengeTypeColor = (type: ChallengeType): string => {
    switch (type) {
      case 'daily':
        return 'bg-blue-500/20 text-blue-300 border-blue-500/30';
      case 'weekly':
        return 'bg-green-500/20 text-green-300 border-green-500/30';
      case 'monthly':
        return 'bg-purple-500/20 text-purple-300 border-purple-500/30';
      default:
        return 'bg-gray-500/20 text-gray-300 border-gray-500/30';
    }
  };

  // Format time digits with leading zero
  const formatTimeDigit = (digit: number): string => {
    return digit < 10 ? `0${digit}` : `${digit}`;
  };

  // If loading, show skeleton
  if (isLoading) {
    return (
      <Card className="border-blue-500/20 bg-[#0f1c2e]/60 backdrop-blur-sm">
        <CardHeader className="pb-2">
          <div className="flex justify-between">
            <div>
              <Skeleton className="h-6 w-32 mb-2" />
              <Skeleton className="h-4 w-48" />
            </div>
            <Skeleton className="h-6 w-16" />
          </div>
        </CardHeader>
        <CardContent className="pt-2">
          <Skeleton className="h-20 w-full mb-4" />
          <div className="grid grid-cols-2 gap-3 mb-3">
            <Skeleton className="h-24 w-full" />
            <Skeleton className="h-24 w-full" />
          </div>
          <div className="grid grid-cols-3 gap-2">
            <Skeleton className="h-16 w-full" />
            <Skeleton className="h-16 w-full" />
            <Skeleton className="h-16 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  // If error, show error state
  if (error) {
    return (
      <Card className="border-blue-500/20 bg-[#0f1c2e]/60 backdrop-blur-sm">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg font-medium">Challenge HUD</CardTitle>
          <CardDescription className="text-xs text-red-400">
            Error loading challenge data
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-2">
          <div className="flex flex-col items-center justify-center py-6">
            <AlertTriangle className="h-12 w-12 text-red-500/50 mb-3" />
            <p className="text-red-400 text-sm text-center max-w-xs">
              There was an error loading the challenge data. Please try again later.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // If no challenge selected or in aggregated view, show empty state
  if (isAggregatedView || !selectedChallenge) {
    return (
      <Card className="border-blue-500/20 bg-[#0f1c2e]/60 backdrop-blur-sm">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg font-medium">Challenge HUD</CardTitle>
          <CardDescription className="text-xs text-gray-400">
            Select a challenge to view detailed metrics
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-2">
          <div className="flex flex-col items-center justify-center py-6">
            <Trophy className="h-12 w-12 text-blue-500/50 mb-3" />
            <p className="text-gray-400 text-sm text-center max-w-xs">
              Select a specific challenge from the dropdown to see detailed challenge metrics and countdown.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const rankMovement = getRankMovement();

  return (
    <Card className="border-blue-500/20 bg-[#0f1c2e]/60 backdrop-blur-sm overflow-hidden">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg font-medium">Challenge HUD</CardTitle>
            <CardDescription className="text-xs text-gray-400">
              {selectedChallenge.name} dashboard
            </CardDescription>
          </div>
          <Badge className={`${getChallengeTypeColor(selectedChallenge.type)} border text-xs`}>
            {selectedChallenge.type}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="pt-2">
        {/* Countdown timer with HUD styling */}
        <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-3 mb-4">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center">
              <Clock className="h-4 w-4 text-blue-300 mr-2" />
              <span className="text-sm font-medium text-blue-300">Challenge Countdown</span>
            </div>
          </div>

          <div className="grid grid-cols-4 gap-2 text-center">
            <div className="bg-blue-500/10 rounded-md p-2">
              <div className="text-lg font-mono font-bold text-white">
                {formatTimeDigit(timeRemaining.days)}
              </div>
              <div className="text-xs text-blue-300">DAYS</div>
            </div>
            <div className="bg-blue-500/10 rounded-md p-2">
              <div className="text-lg font-mono font-bold text-white">
                {formatTimeDigit(timeRemaining.hours)}
              </div>
              <div className="text-xs text-blue-300">HOURS</div>
            </div>
            <div className="bg-blue-500/10 rounded-md p-2">
              <div className="text-lg font-mono font-bold text-white">
                {formatTimeDigit(timeRemaining.minutes)}
              </div>
              <div className="text-xs text-blue-300">MINUTES</div>
            </div>
            <div className="bg-blue-500/10 rounded-md p-2">
              <div className="text-lg font-mono font-bold text-white">
                {formatTimeDigit(timeRemaining.seconds)}
              </div>
              <div className="text-xs text-blue-300">SECONDS</div>
            </div>
          </div>
        </div>

        {/* Rank and performance section */}
        <div className="grid grid-cols-2 gap-3 mb-3">
          <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-3">
            <div className="flex items-center mb-2">
              <Trophy className="h-4 w-4 text-amber-400 mr-2" />
              <span className="text-sm font-medium text-blue-300">Current Rank</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="text-xl font-bold text-white">
                #{selectedChallenge.currentRank}
              </div>
              <div className="flex items-center">
                {rankMovement > 0 ? (
                  <Badge className="bg-green-500/20 text-green-300 border-green-500/30 flex items-center">
                    <ChevronRight className="h-3 w-3 mr-1 rotate-[270deg]" />
                    +{rankMovement}
                  </Badge>
                ) : rankMovement < 0 ? (
                  <Badge className="bg-red-500/20 text-red-300 border-red-500/30 flex items-center">
                    <ChevronRight className="h-3 w-3 mr-1 rotate-90" />
                    {rankMovement}
                  </Badge>
                ) : (
                  <Badge className="bg-blue-500/20 text-blue-300 border-blue-500/30">
                    No Change
                  </Badge>
                )}
              </div>
            </div>
            <div className="text-xs text-gray-400 mt-1">
              of {selectedChallenge.totalParticipants} participants
            </div>
          </div>

          <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-3">
            <div className="flex items-center mb-2">
              <TrendingUp className="h-4 w-4 text-green-400 mr-2" />
              <span className="text-sm font-medium text-blue-300">P&L</span>
            </div>
            <div className="text-xl font-bold text-white">
              {selectedChallenge.metrics && selectedChallenge.metrics.pnlPercent >= 0 ? (
                <span className="text-green-400">+{selectedChallenge.metrics.pnlPercent.toFixed(2)}%</span>
              ) : (
                <span className="text-red-400">{selectedChallenge.metrics?.pnlPercent.toFixed(2)}%</span>
              )}
            </div>
            <div className="text-xs text-gray-400 mt-1">
              ${selectedChallenge.metrics?.currentEquity?.toLocaleString()}
            </div>
          </div>
        </div>



        {/* Stats section */}
        <div className="grid grid-cols-3 gap-2">
          <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-2 text-center">
            <div className="text-xs text-gray-400 mb-1">Trades</div>
            <div className="text-md font-bold text-white">
              {selectedChallenge.metrics?.tradesClosed || 0}
            </div>
          </div>
          <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-2 text-center">
            <div className="text-xs text-gray-400 mb-1">Drawdown</div>
            <div className="text-md font-bold text-white">
              {selectedChallenge.metrics?.drawdownPercent.toFixed(2)}%
            </div>
          </div>
          <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-2 text-center">
            <div className="text-xs text-gray-400 mb-1">Risk/Trade</div>
            <div className="text-md font-bold text-white">
              {selectedChallenge.metrics?.riskPerTrade.toFixed(2)}%
            </div>
          </div>
        </div>

        {/* Toggle details button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowDetails(!showDetails)}
          className="w-full mt-3 text-blue-300 hover:text-blue-200 hover:bg-blue-500/10"
        >
          {showDetails ? 'Hide Details' : 'Show Challenge Rules'}
        </Button>

        {/* Expandable details section */}
        <AnimatePresence>
          {showDetails && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="overflow-hidden"
            >
              <div className="mt-3 pt-3 border-t border-blue-500/20">
                <div className="text-sm font-medium text-blue-300 mb-2">Challenge Rules:</div>
                <ul className="space-y-2 text-sm text-gray-300">
                  <li className="flex items-start">
                    <AlertTriangle className="h-4 w-4 text-amber-400 mr-2 mt-0.5 flex-shrink-0" />
                    <span>Maximum drawdown: <span className="text-white font-medium">5%</span></span>
                  </li>
                  <li className="flex items-start">
                    <Target className="h-4 w-4 text-green-400 mr-2 mt-0.5 flex-shrink-0" />
                    <span>Profit target: <span className="text-white font-medium">10%</span></span>
                  </li>
                  <li className="flex items-start">
                    <Clock className="h-4 w-4 text-blue-400 mr-2 mt-0.5 flex-shrink-0" />
                    <span>Minimum trading days: <span className="text-white font-medium">5</span></span>
                  </li>
                </ul>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </CardContent>
    </Card>
  );
};

// Wrap the component with an error boundary
const ChallengeHUDWithErrorBoundary = () => {
  return (
    <ErrorBoundary
      componentName="ChallengeHUD"
      containError={true}
    >
      <ChallengeHUD />
    </ErrorBoundary>
  );
};

export default ChallengeHUDWithErrorBoundary;