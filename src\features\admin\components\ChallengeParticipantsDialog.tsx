/**
 * Challenge Participants Dialog Component
 * @description Dialog for viewing challenge participants in the admin panel
 */

import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/common/components/ui/dialog';
import { useSimpleDialogCleanup } from '@/common/hooks/useDialogCleanup';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/common/components/ui/table';
import { Button } from '@/common/components/ui/button';
import { Badge } from '@/common/components/ui/badge';
import { ScrollArea } from '@/common/components/ui/scroll-area';
import { Input } from '@/common/components/ui/input';
import {
  Users,
  Trophy,
  Search,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  User,
} from 'lucide-react';
import { Challenge } from '@/features/challenges/types';
import { formatDate } from '@/common/utils/formatters';

interface ChallengeParticipantsDialogProps {
  challenge: Challenge | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

/**
 * Challenge participants dialog component
 */
const ChallengeParticipantsDialog: React.FC<ChallengeParticipantsDialogProps> = ({
  challenge,
  open,
  onOpenChange,
}) => {
  const [searchQuery, setSearchQuery] = useState('');

  // Use cleanup hook to prevent UI freezing
  const handleOpenChange = useSimpleDialogCleanup(onOpenChange);

  if (!challenge) return null;

  // Filter participants by search query
  const filteredParticipants = challenge.challengeEntries?.filter(entry => {
    if (!searchQuery) return true;
    return entry.user?.username.toLowerCase().includes(searchQuery.toLowerCase());
  }) || [];

  // Get connection status badge
  const getConnectionStatusBadge = (status: string) => {
    switch (status) {
      case 'connected':
        return (
          <Badge variant="outline" className="bg-green-500/20 text-green-400 border-green-500/30">
            <CheckCircle className="h-3.5 w-3.5 mr-1.5" />
            Connected
          </Badge>
        );
      case 'payment_complete':
        return (
          <Badge variant="outline" className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">
            <Clock className="h-3.5 w-3.5 mr-1.5" />
            Payment Complete
          </Badge>
        );
      case 'pending':
        return (
          <Badge variant="outline" className="bg-blue-500/20 text-blue-400 border-blue-500/30">
            <Clock className="h-3.5 w-3.5 mr-1.5" />
            Pending
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="bg-gray-500/20 text-gray-400 border-gray-500/30">
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </Badge>
        );
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[800px] bg-forex-darker border-forex-border text-white">
        <DialogHeader>
          <DialogTitle className="text-white flex items-center gap-2">
            <Users className="h-5 w-5 text-blue-400" />
            Challenge Participants
          </DialogTitle>
          <DialogDescription className="text-white/80">
            {challenge.name} - {filteredParticipants.length} participants
          </DialogDescription>
        </DialogHeader>

        <div className="flex items-center mb-4">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              placeholder="Search participants..."
              className="pl-9 bg-forex-dark border-forex-border text-white"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        <ScrollArea className="h-[400px]">
          <Table>
            <TableHeader>
              <TableRow className="border-forex-border hover:bg-transparent">
                <TableHead className="text-gray-400">User</TableHead>
                <TableHead className="text-gray-400">Enrollment Date</TableHead>
                <TableHead className="text-gray-400">Status</TableHead>
                <TableHead className="text-gray-400 text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredParticipants.length === 0 ? (
                <TableRow className="border-forex-border">
                  <TableCell colSpan={4} className="text-center py-8 text-gray-400">
                    {searchQuery ? (
                      <>
                        <Search className="h-8 w-8 mx-auto mb-2 text-gray-500" />
                        No participants match your search
                      </>
                    ) : (
                      <>
                        <Users className="h-8 w-8 mx-auto mb-2 text-gray-500" />
                        No participants have joined this challenge yet
                      </>
                    )}
                  </TableCell>
                </TableRow>
              ) : (
                filteredParticipants.map((entry) => (
                  <TableRow key={entry.id} className="border-forex-border hover:bg-forex-dark/50">
                    <TableCell className="font-medium flex items-center gap-2">
                      <User className="h-4 w-4 text-blue-400" />
                      {entry.user?.username || 'Unknown User'}
                    </TableCell>
                    <TableCell>
                      {formatDate(entry.enrollmentTime)}
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col gap-1">
                        {getConnectionStatusBadge(entry.connectStatus)}
                        {entry.disqualified && (
                          <Badge variant="outline" className="bg-red-500/20 text-red-400 border-red-500/30">
                            <AlertTriangle className="h-3.5 w-3.5 mr-1.5" />
                            Disqualified
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-blue-400 hover:text-blue-300 hover:bg-blue-500/10"
                        onClick={() => {
                          // Navigate to challenge entry detail page
                          window.open(`/challenge-entries/${entry.id}`, '_blank');
                        }}
                      >
                        View Details
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </ScrollArea>

        <DialogFooter className="flex justify-end">
          <Button variant="outline" onClick={() => handleOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ChallengeParticipantsDialog;
