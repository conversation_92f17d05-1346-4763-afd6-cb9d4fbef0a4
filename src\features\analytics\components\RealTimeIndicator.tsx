import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Wifi, WifiOff, RefreshCw } from 'lucide-react';
import { socketService } from '@/common/services/socketService';
import { SocketEvent } from '@/types/socketEvents';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/common/components/ui/tooltip';

interface RealTimeIndicatorProps {
  /**
   * The type of data being monitored
   */
  dataType: 'trades' | 'metrics' | 'drawdown' | 'all';
  
  /**
   * Optional className for styling
   */
  className?: string;
  
  /**
   * Optional position for the indicator
   */
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'inline';
  
  /**
   * Optional size for the indicator
   */
  size?: 'sm' | 'md' | 'lg';
  
  /**
   * Optional callback for when data is updated
   */
  onUpdate?: () => void;
}

/**
 * Real-time data update indicator component
 * 
 * Shows when data is being updated in real-time and indicates connection status
 * 
 * @component
 * @example
 * <RealTimeIndicator dataType="trades" position="top-right" />
 */
const RealTimeIndicator: React.FC<RealTimeIndicatorProps> = ({
  dataType,
  className = '',
  position = 'top-right',
  size = 'md',
  onUpdate
}) => {
  const [isConnected, setIsConnected] = useState<boolean>(socketService.isConnected());
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [isUpdating, setIsUpdating] = useState<boolean>(false);
  const [updateCount, setUpdateCount] = useState<number>(0);
  
  // Get position classes
  const getPositionClasses = () => {
    if (position === 'inline') return '';
    
    switch (position) {
      case 'top-right':
        return 'absolute top-2 right-2';
      case 'top-left':
        return 'absolute top-2 left-2';
      case 'bottom-right':
        return 'absolute bottom-2 right-2';
      case 'bottom-left':
        return 'absolute bottom-2 left-2';
      default:
        return 'absolute top-2 right-2';
    }
  };
  
  // Get size classes
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'h-3 w-3';
      case 'md':
        return 'h-4 w-4';
      case 'lg':
        return 'h-5 w-5';
      default:
        return 'h-4 w-4';
    }
  };
  
  // Format time since last update
  const getTimeSinceLastUpdate = () => {
    if (!lastUpdate) return 'No updates yet';
    
    const now = new Date();
    const diffMs = now.getTime() - lastUpdate.getTime();
    
    if (diffMs < 1000) return 'Just now';
    if (diffMs < 60000) return `${Math.floor(diffMs / 1000)}s ago`;
    if (diffMs < 3600000) return `${Math.floor(diffMs / 60000)}m ago`;
    
    return `${Math.floor(diffMs / 3600000)}h ago`;
  };
  
  // Handle connection status changes
  useEffect(() => {
    const handleConnect = () => setIsConnected(true);
    const handleDisconnect = () => setIsConnected(false);
    
    socketService.on(SocketEvent.CONNECT, handleConnect);
    socketService.on(SocketEvent.DISCONNECT, handleDisconnect);
    
    // Set initial connection status
    setIsConnected(socketService.isConnected());
    
    return () => {
      socketService.off(SocketEvent.CONNECT, handleConnect);
      socketService.off(SocketEvent.DISCONNECT, handleDisconnect);
    };
  }, []);
  
  // Handle data updates
  useEffect(() => {
    const handleUpdate = () => {
      setLastUpdate(new Date());
      setIsUpdating(true);
      setUpdateCount(prev => prev + 1);
      
      // Call onUpdate callback if provided
      if (onUpdate) onUpdate();
      
      // Reset updating state after animation
      setTimeout(() => {
        setIsUpdating(false);
      }, 1000);
    };
    
    // Register event handlers based on dataType
    if (dataType === 'trades' || dataType === 'all') {
      socketService.on(SocketEvent.TRADE_CREATED, handleUpdate);
      socketService.on(SocketEvent.TRADE_UPDATED, handleUpdate);
    }
    
    if (dataType === 'metrics' || dataType === 'all') {
      socketService.on(SocketEvent.USER_METRICS_UPDATED, handleUpdate);
    }
    
    if (dataType === 'drawdown' || dataType === 'all') {
      socketService.on(SocketEvent.REAL_TIME_DRAWDOWN_UPDATE, handleUpdate);
    }
    
    return () => {
      // Cleanup event handlers
      if (dataType === 'trades' || dataType === 'all') {
        socketService.off(SocketEvent.TRADE_CREATED, handleUpdate);
        socketService.off(SocketEvent.TRADE_UPDATED, handleUpdate);
      }
      
      if (dataType === 'metrics' || dataType === 'all') {
        socketService.off(SocketEvent.USER_METRICS_UPDATED, handleUpdate);
      }
      
      if (dataType === 'drawdown' || dataType === 'all') {
        socketService.off(SocketEvent.REAL_TIME_DRAWDOWN_UPDATE, handleUpdate);
      }
    };
  }, [dataType, onUpdate]);
  
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className={`${getPositionClasses()} ${className}`}>
            <AnimatePresence mode="wait">
              {!isConnected ? (
                <motion.div
                  key="disconnected"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  className="text-red-400"
                >
                  <WifiOff className={getSizeClasses()} />
                </motion.div>
              ) : isUpdating ? (
                <motion.div
                  key="updating"
                  initial={{ opacity: 0, scale: 0.8, rotate: 0 }}
                  animate={{ opacity: 1, scale: 1, rotate: 360 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  transition={{ duration: 0.5 }}
                  className="text-blue-400"
                >
                  <RefreshCw className={getSizeClasses()} />
                </motion.div>
              ) : (
                <motion.div
                  key="connected"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  className="text-green-400"
                >
                  <Wifi className={getSizeClasses()} />
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </TooltipTrigger>
        <TooltipContent side="bottom" className="bg-gray-900 border-gray-700 text-xs">
          <div className="space-y-1">
            <div className="flex items-center gap-1">
              <span className="font-medium">Status:</span>
              <span className={isConnected ? 'text-green-400' : 'text-red-400'}>
                {isConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
            {lastUpdate && (
              <div className="flex items-center gap-1">
                <span className="font-medium">Last update:</span>
                <span>{getTimeSinceLastUpdate()}</span>
              </div>
            )}
            <div className="flex items-center gap-1">
              <span className="font-medium">Updates:</span>
              <span>{updateCount}</span>
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default RealTimeIndicator;
