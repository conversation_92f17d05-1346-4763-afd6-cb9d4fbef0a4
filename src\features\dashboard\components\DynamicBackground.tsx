import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface DynamicBackgroundProps {
  children: React.ReactNode;
}

/**
 * Dynamic animated background component with trading-inspired design
 *
 * Creates an immersive background with animated particles, trading chart lines,
 * and interactive visual elements inspired by the landing page design.
 *
 * @component
 * @example
 * <DynamicBackground>
 *   <DashboardContent />
 * </DynamicBackground>
 */
const DynamicBackground: React.FC<DynamicBackgroundProps> = ({ children }) => {
  // Enhanced theme colors with more vibrant options
  const themeColors = {
    primary: '#00ccff',
    secondary: '#0077ff',
    accent: '#00ffff',
    bgFrom: '#0c1a2a',
    bgTo: '#0a0f14',
    glow: 'rgba(0, 204, 255, 0.5)'
  };

  // Generate random particles for the background
  const generateParticles = (count: number) => {
    return Array.from({ length: count }).map((_, i) => ({
      id: i,
      size: Math.random() * 6 + 2,
      x: Math.random() * 100,
      y: Math.random() * 100,
      opacity: Math.random() * 0.5 + 0.1,
      animationDuration: Math.random() * 20 + 10,
      animationDelay: Math.random() * 5,
    }));
  };

  // State for particles
  const [particles] = useState(() => generateParticles(15));

  return (
    <div className="relative min-h-screen overflow-hidden">
      {/* Enhanced animated background with gradient */}
      <div
        className="fixed inset-0 transition-colors duration-1000 ease-in-out"
        style={{
          background: `linear-gradient(135deg, ${themeColors.bgFrom} 0%, ${themeColors.bgTo} 100%)`,
          boxShadow: `inset 0 0 100px rgba(0, 0, 0, 0.9)`
        }}
      >
        {/* Animated gradient overlay */}
        <div
          className="absolute inset-0 opacity-30 animate-gradient-x"
          style={{
            background: `linear-gradient(90deg, rgba(0,119,255,0.05) 0%, rgba(0,204,255,0.1) 50%, rgba(0,119,255,0.05) 100%)`,
            backgroundSize: '200% 100%'
          }}
        />

        {/* Noise texture overlay for more depth */}
        <div
          className="absolute inset-0 opacity-10"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
            backgroundSize: '200px 200px'
          }}
        />

        {/* Scan lines effect */}
        <div
          className="absolute inset-0 opacity-5"
          style={{
            backgroundImage: `linear-gradient(transparent 50%, rgba(0, 0, 0, 0.5) 50%)`,
            backgroundSize: '100% 4px'
          }}
        />

        {/* Vignette effect */}
        <div
          className="absolute inset-0"
          style={{
            boxShadow: `inset 0 0 150px rgba(0, 0, 0, 0.7)`
          }}
        />

        {/* Animated particles */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {particles.map((particle) => (
            <motion.div
              key={particle.id}
              className="absolute rounded-full"
              style={{
                width: `${particle.size}px`,
                height: `${particle.size}px`,
                top: `${particle.y}%`,
                left: `${particle.x}%`,
                opacity: particle.opacity,
                background: `radial-gradient(circle at center, ${themeColors.primary}, transparent)`,
              }}
              animate={{
                y: [`${particle.y}%`, `${particle.y - 10}%`, `${particle.y}%`],
                x: [`${particle.x}%`, `${particle.x + 5}%`, `${particle.x}%`],
                opacity: [particle.opacity, particle.opacity * 1.5, particle.opacity],
              }}
              transition={{
                duration: particle.animationDuration,
                repeat: Infinity,
                ease: "easeInOut",
                delay: particle.animationDelay,
              }}
            />
          ))}
        </div>

        {/* Trading chart line animation */}
        <div className="absolute bottom-0 left-0 right-0 h-24 opacity-20">
          <svg width="100%" height="100%" viewBox="0 0 1200 200" preserveAspectRatio="none">
            <defs>
              <linearGradient id="chartGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="0%" stopColor={themeColors.primary} stopOpacity="0.3" />
                <stop offset="100%" stopColor={themeColors.primary} stopOpacity="0" />
              </linearGradient>
            </defs>
            <path
              d="M0,100 C150,20 350,150 500,80 C650,10 800,120 1000,60 C1100,20 1200,80 1200,80 L1200,200 L0,200 Z"
              fill="url(#chartGradient)"
              stroke={themeColors.primary}
              strokeWidth="1.5"
            >
              <animate
                attributeName="d"
                dur="20s"
                repeatCount="indefinite"
                values="
                  M0,100 C150,20 350,150 500,80 C650,10 800,120 1000,60 C1100,20 1200,80 1200,80 L1200,200 L0,200 Z;
                  M0,80 C150,120 350,60 500,100 C650,140 800,60 1000,100 C1100,130 1200,100 1200,100 L1200,200 L0,200 Z;
                  M0,110 C150,60 350,120 500,70 C650,40 800,90 1000,50 C1100,30 1200,60 1200,60 L1200,200 L0,200 Z;
                  M0,100 C150,20 350,150 500,80 C650,10 800,120 1000,60 C1100,20 1200,80 1200,80 L1200,200 L0,200 Z"
              />
            </path>
          </svg>
        </div>
      </div>

      {/* Animated HUD-like corner elements */}
      <motion.div
        className="fixed top-0 left-0 w-[100px] h-[100px] pointer-events-none z-0"
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <svg width="100" height="100" viewBox="0 0 100 100">
          <motion.path
            d="M0,30 L0,0 L30,0"
            fill="none"
            stroke={themeColors.accent}
            strokeWidth="2"
            initial={{ pathLength: 0 }}
            animate={{ pathLength: 1 }}
            transition={{ duration: 1, ease: "easeInOut" }}
          />
          <motion.circle
            cx="30"
            cy="0"
            r="3"
            fill={themeColors.accent}
            initial={{ opacity: 0 }}
            animate={{ opacity: [0, 1, 0] }}
            transition={{ duration: 2, repeat: Infinity, repeatType: "reverse", delay: 1 }}
          />
        </svg>
      </motion.div>

      <motion.div
        className="fixed top-0 right-0 w-[100px] h-[100px] pointer-events-none z-0"
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <svg width="100" height="100" viewBox="0 0 100 100">
          <motion.path
            d="M100,30 L100,0 L70,0"
            fill="none"
            stroke={themeColors.accent}
            strokeWidth="2"
            initial={{ pathLength: 0 }}
            animate={{ pathLength: 1 }}
            transition={{ duration: 1, ease: "easeInOut" }}
          />
          <motion.circle
            cx="70"
            cy="0"
            r="3"
            fill={themeColors.accent}
            initial={{ opacity: 0 }}
            animate={{ opacity: [0, 1, 0] }}
            transition={{ duration: 2, repeat: Infinity, repeatType: "reverse", delay: 1.2 }}
          />
        </svg>
      </motion.div>

      <motion.div
        className="fixed bottom-0 left-0 w-[100px] h-[100px] pointer-events-none z-0"
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        <svg width="100" height="100" viewBox="0 0 100 100">
          <motion.path
            d="M0,70 L0,100 L30,100"
            fill="none"
            stroke={themeColors.accent}
            strokeWidth="2"
            initial={{ pathLength: 0 }}
            animate={{ pathLength: 1 }}
            transition={{ duration: 1, ease: "easeInOut" }}
          />
          <motion.circle
            cx="30"
            cy="100"
            r="3"
            fill={themeColors.accent}
            initial={{ opacity: 0 }}
            animate={{ opacity: [0, 1, 0] }}
            transition={{ duration: 2, repeat: Infinity, repeatType: "reverse", delay: 1.4 }}
          />
        </svg>
      </motion.div>

      <motion.div
        className="fixed bottom-0 right-0 w-[100px] h-[100px] pointer-events-none z-0"
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5, delay: 0.5 }}
      >
        <svg width="100" height="100" viewBox="0 0 100 100">
          <motion.path
            d="M100,70 L100,100 L70,100"
            fill="none"
            stroke={themeColors.accent}
            strokeWidth="2"
            initial={{ pathLength: 0 }}
            animate={{ pathLength: 1 }}
            transition={{ duration: 1, ease: "easeInOut" }}
          />
          <motion.circle
            cx="70"
            cy="100"
            r="3"
            fill={themeColors.accent}
            initial={{ opacity: 0 }}
            animate={{ opacity: [0, 1, 0] }}
            transition={{ duration: 2, repeat: Infinity, repeatType: "reverse", delay: 1.6 }}
          />
        </svg>
      </motion.div>

      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  );
};

export default DynamicBackground;
