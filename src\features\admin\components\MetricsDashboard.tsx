import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from '@/common/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/common/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/common/components/ui/table';
import { Button } from '@/common/components/ui/button';
import { Badge } from '@/common/components/ui/badge';
import { apiService } from '@/common/services/api';
import { toast } from 'sonner';
import { RefreshCw, AlertTriangle, CheckCircle, Clock, Activity } from 'lucide-react';

interface SystemMetrics {
  uptime: number;
  activeConnections: number;
  peakConnections: number;
  totalConnections: number;
  totalDisconnections: number;
  totalErrors: number;
  errorsByType: { [key: string]: number };
}

interface ConnectionMetrics {
  challengeEntryId: number;
  status: string;
  connectionDuration: number;
  reconnectionAttempts: number;
  reconnectionSuccessRate: number;
  messagesSent: number;
  messagesReceived: number;
  errors: { [key: string]: number };
  averagePingLatency: number;
}

interface TokenRefreshMetrics {
  challengeEntryId: number;
  lastRefreshTime: string;
  refreshAttempts: number;
  refreshSuccessRate: number;
  averageRefreshLatency: number;
  tokenExpiryCount: number;
}

interface AllMetrics {
  system: SystemMetrics;
  connections: ConnectionMetrics[];
  tokenRefreshes: TokenRefreshMetrics[];
}

const formatDuration = (ms: number): string => {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) {
    return `${days}d ${hours % 24}h ${minutes % 60}m`;
  } else if (hours > 0) {
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
};

const getStatusColor = (status: string): string => {
  switch (status) {
    case 'CONNECTED':
      return 'bg-green-500';
    case 'CONNECTING':
      return 'bg-blue-500';
    case 'RECONNECTING':
      return 'bg-yellow-500';
    case 'DISCONNECTED':
      return 'bg-gray-500';
    case 'ERROR':
      return 'bg-red-500';
    default:
      return 'bg-gray-500';
  }
};

const MetricsDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<AllMetrics | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshInterval, setRefreshInterval] = useState<number | null>(null);

  const fetchMetrics = async () => {
    try {
      setLoading(true);
      const response = await apiService.getMetrics();
      setMetrics(response);
      setError(null);
    } catch (err) {
      setError('Failed to fetch metrics');
      toast.error('Failed to fetch metrics');
      console.error('Error fetching metrics:', err);
    } finally {
      setLoading(false);
    }
  };

  const resetMetrics = async (challengeEntryId?: number) => {
    try {
      if (challengeEntryId) {
        await apiService.resetMetrics(challengeEntryId);
        toast.success(`Metrics for challenge entry ${challengeEntryId} reset successfully`);
      } else {
        await apiService.resetSystemMetrics();
        toast.success('System metrics reset successfully');
      }
      fetchMetrics();
    } catch (err) {
      toast.error('Failed to reset metrics');
      console.error('Error resetting metrics:', err);
    }
  };

  const startAutoRefresh = () => {
    if (refreshInterval) {
      clearInterval(refreshInterval);
    }
    const interval = window.setInterval(() => {
      // Only refresh if the component is still mounted and visible
      if (document.visibilityState === 'visible') {
        fetchMetrics();
      }
    }, 10000); // Increased interval to 10 seconds to prevent performance issues
    setRefreshInterval(interval);
    toast.success('Auto-refresh enabled (10s)');
  };

  const stopAutoRefresh = () => {
    if (refreshInterval) {
      clearInterval(refreshInterval);
      setRefreshInterval(null);
      toast.info('Auto-refresh disabled');
    }
  };

  useEffect(() => {
    fetchMetrics();
    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, []);

  if (loading && !metrics) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-forex-primary"></div>
      </div>
    );
  }

  if (error && !metrics) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <AlertTriangle className="h-12 w-12 text-red-500 mb-4" />
        <p className="text-red-500">{error}</p>
        <Button onClick={fetchMetrics} className="mt-4">
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">System Monitoring Dashboard</h2>
        <div className="space-x-2">
          <Button onClick={fetchMetrics} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          {refreshInterval ? (
            <Button onClick={stopAutoRefresh} variant="outline" size="sm">
              Stop Auto-refresh
            </Button>
          ) : (
            <Button onClick={startAutoRefresh} variant="outline" size="sm">
              Enable Auto-refresh
            </Button>
          )}
        </div>
      </div>

      <Tabs defaultValue="system">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="system">System Metrics</TabsTrigger>
          <TabsTrigger value="connections">WebSocket Connections</TabsTrigger>
          <TabsTrigger value="tokens">Token Refresh</TabsTrigger>
        </TabsList>

        <TabsContent value="system">
          {metrics?.system && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Uptime</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatDuration(metrics.system.uptime)}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Active Connections</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{metrics.system.activeConnections}</div>
                  <p className="text-xs text-muted-foreground">Peak: {metrics.system.peakConnections}</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Total Connections</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{metrics.system.totalConnections}</div>
                  <p className="text-xs text-muted-foreground">Disconnections: {metrics.system.totalDisconnections}</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Total Errors</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{metrics.system.totalErrors}</div>
                </CardContent>
              </Card>
            </div>
          )}

          {metrics?.system && metrics.system.totalErrors > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Error Distribution</CardTitle>
                <CardDescription>Breakdown of errors by type</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Error Type</TableHead>
                      <TableHead className="text-right">Count</TableHead>
                      <TableHead className="text-right">Percentage</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {Object.entries(metrics.system.errorsByType).map(([type, count]) => (
                      <TableRow key={type}>
                        <TableCell>{type}</TableCell>
                        <TableCell className="text-right">{count}</TableCell>
                        <TableCell className="text-right">
                          {((count / metrics.system.totalErrors) * 100).toFixed(1)}%
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )}

          <div className="mt-4">
            <Button onClick={() => resetMetrics()} variant="destructive" size="sm">
              Reset System Metrics
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="connections">
          <Card>
            <CardHeader>
              <CardTitle>WebSocket Connections</CardTitle>
              <CardDescription>Status and metrics for all WebSocket connections</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Challenge Entry ID</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Duration</TableHead>
                    <TableHead>Messages</TableHead>
                    <TableHead>Reconnections</TableHead>
                    <TableHead>Latency</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {metrics?.connections.map((conn) => (
                    <TableRow key={conn.challengeEntryId}>
                      <TableCell>{conn.challengeEntryId}</TableCell>
                      <TableCell>
                        <Badge className={getStatusColor(conn.status)}>{conn.status}</Badge>
                      </TableCell>
                      <TableCell>
                        {conn.status === 'CONNECTED' ? formatDuration(conn.connectionDuration) : 'N/A'}
                      </TableCell>
                      <TableCell>
                        {conn.messagesSent} sent / {conn.messagesReceived} received
                      </TableCell>
                      <TableCell>
                        {conn.reconnectionAttempts} attempts
                        <br />
                        {conn.reconnectionSuccessRate.toFixed(1)}% success
                      </TableCell>
                      <TableCell>{conn.averagePingLatency.toFixed(2)} ms</TableCell>
                      <TableCell>
                        <Button
                          onClick={() => resetMetrics(conn.challengeEntryId)}
                          variant="ghost"
                          size="sm"
                        >
                          Reset
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                  {(!metrics?.connections || metrics.connections.length === 0) && (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-4">
                        No active connections
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tokens">
          <Card>
            <CardHeader>
              <CardTitle>Token Refresh Metrics</CardTitle>
              <CardDescription>Metrics for token refresh operations</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Challenge Entry ID</TableHead>
                    <TableHead>Last Refresh</TableHead>
                    <TableHead>Refresh Attempts</TableHead>
                    <TableHead>Success Rate</TableHead>
                    <TableHead>Avg Latency</TableHead>
                    <TableHead>Token Expiry Count</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {metrics?.tokenRefreshes.map((token) => (
                    <TableRow key={token.challengeEntryId}>
                      <TableCell>{token.challengeEntryId}</TableCell>
                      <TableCell>
                        {token.lastRefreshTime
                          ? new Date(token.lastRefreshTime).toLocaleString()
                          : 'Never'}
                      </TableCell>
                      <TableCell>{token.refreshAttempts}</TableCell>
                      <TableCell>{token.refreshSuccessRate.toFixed(1)}%</TableCell>
                      <TableCell>{token.averageRefreshLatency.toFixed(2)} ms</TableCell>
                      <TableCell>{token.tokenExpiryCount}</TableCell>
                      <TableCell>
                        <Button
                          onClick={() => resetMetrics(token.challengeEntryId)}
                          variant="ghost"
                          size="sm"
                        >
                          Reset
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                  {(!metrics?.tokenRefreshes || metrics.tokenRefreshes.length === 0) && (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-4">
                        No token refresh metrics available
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default MetricsDashboard;
