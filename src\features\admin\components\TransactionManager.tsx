/**
 * Transaction Manager Component
 * @description Component for managing wallet transactions in the admin panel
 * @version 1.0.0
 * @status stable
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Search,
  Filter,
  RefreshCw,
  ArrowUpDown,
  MoreHorizontal,
  Calendar,
  Wallet,
  ArrowUp,
  ArrowDown,
  Clock,
  User,
  DollarSign,
  FileText,
  CalendarRange
} from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/common/components/ui/card';
import { Button } from '@/common/components/ui/button';
import { Input } from '@/common/components/ui/input';
import { Badge } from '@/common/components/ui/badge';
import { Skeleton } from '@/common/components/ui/skeleton';
import { useToast } from '@/common/components/ui/use-toast';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/common/components/ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/common/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/common/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/common/components/ui/tabs';
import { DateRangePicker } from '@/common/components/ui/date-range-picker';
import { useTransactions } from '../hooks/useAdminData';

// Transaction type definition
interface Transaction {
  id: number;
  userId: string;
  username: string;
  amount: number;
  type: 'credit' | 'debit';
  reason: string;
  timestamp: string;
}

/**
 * Transaction manager component
 */
const TransactionManager: React.FC = () => {
  const { toast } = useToast();
  const [loading, setLoading] = useState<boolean>(true);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [filteredTransactions, setFilteredTransactions] = useState<Transaction[]>([]);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('timestamp');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [activeTab, setActiveTab] = useState<string>('all');
  const [dateRange, setDateRange] = useState<{ from: Date | undefined; to: Date | undefined }>({
    from: undefined,
    to: undefined,
  });

  // Use the transactions hook
  const {
    transactions: apiTransactions,
    transactionStats,
    loading: apiLoading,
    statsLoading,
    error,
    pagination,
    fetchTransactions,
    fetchTransactionStats,
    setParams,
    changePage
  } = useTransactions();

  // Set transactions when API data is loaded
  useEffect(() => {
    if (apiTransactions && Array.isArray(apiTransactions) && apiTransactions.length > 0) {
      setTransactions(apiTransactions);
      setFilteredTransactions(apiTransactions);
    } else if (apiTransactions && Array.isArray(apiTransactions)) {
      // Handle empty array case
      setTransactions([]);
      setFilteredTransactions([]);
    }
  }, [apiTransactions]);

  // Set loading state from API
  useEffect(() => {
    setLoading(apiLoading);
  }, [apiLoading]);

  // Update params when date range changes
  useEffect(() => {
    if (dateRange.from && dateRange.to) {
      setParams({
        startDate: dateRange.from.toISOString(),
        endDate: dateRange.to.toISOString(),
        type: typeFilter !== 'all' ? typeFilter : undefined,
        search: searchTerm || undefined
      });
    }
  }, [dateRange, setParams, typeFilter, searchTerm]);

  // Filter and sort transactions
  useEffect(() => {
    let result = [...transactions];

    // Apply tab filter
    if (activeTab !== 'all') {
      result = result.filter(transaction => transaction.type === activeTab);
    }

    // Apply type filter
    if (typeFilter !== 'all') {
      result = result.filter(transaction => transaction.type === typeFilter);
    }

    // Apply date range filter
    if (dateRange.from && dateRange.to) {
      result = result.filter(transaction => {
        const transactionDate = new Date(transaction.timestamp);
        return (
          transactionDate >= dateRange.from! &&
          transactionDate <= new Date(dateRange.to!.setHours(23, 59, 59, 999))
        );
      });
    }

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      result = result.filter(transaction =>
        transaction.username.toLowerCase().includes(term) ||
        transaction.reason.toLowerCase().includes(term) ||
        transaction.userId.includes(term)
      );
    }

    // Apply sorting
    result.sort((a, b) => {
      let valueA, valueB;

      switch (sortBy) {
        case 'username':
          valueA = a.username.toLowerCase();
          valueB = b.username.toLowerCase();
          break;
        case 'amount':
          valueA = a.amount;
          valueB = b.amount;
          break;
        case 'type':
          valueA = a.type;
          valueB = b.type;
          break;
        case 'reason':
          valueA = a.reason.toLowerCase();
          valueB = b.reason.toLowerCase();
          break;
        case 'timestamp':
        default:
          valueA = new Date(a.timestamp).getTime();
          valueB = new Date(b.timestamp).getTime();
          break;
      }

      if (sortOrder === 'asc') {
        return valueA > valueB ? 1 : -1;
      } else {
        return valueA < valueB ? 1 : -1;
      }
    });

    setFilteredTransactions(result);
  }, [transactions, searchTerm, typeFilter, sortBy, sortOrder, activeTab, dateRange]);

  // Toggle sort order
  const toggleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Get transaction type badge variant
  const getTransactionTypeBadge = (type: string) => {
    return (
      <Badge
        variant={type === 'credit' ? 'success' : 'destructive'}
        className="flex items-center gap-1"
      >
        {type === 'credit' ? (
          <>
            <ArrowUp className="h-3 w-3" />
            Credit
          </>
        ) : (
          <>
            <ArrowDown className="h-3 w-3" />
            Debit
          </>
        )}
      </Badge>
    );
  };

  // Calculate total credits and debits
  const totalCredits = filteredTransactions
    .filter(t => t.type === 'credit')
    .reduce((sum, t) => sum + t.amount, 0);

  const totalDebits = filteredTransactions
    .filter(t => t.type === 'debit')
    .reduce((sum, t) => sum + t.amount, 0);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Wallet Transactions</h1>
        <Button
          variant="outline"
          size="sm"
          className="flex items-center gap-2"
          onClick={() => {
            fetchTransactions();
            fetchTransactionStats();
          }}
          disabled={loading || statsLoading}
        >
          <RefreshCw className={`h-4 w-4 ${(loading || statsLoading) ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-forex-darker border-forex-border">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-forex-muted">Total Transactions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              {statsLoading ? (
                <Skeleton className="h-8 w-20" />
              ) : (
                <div className="text-2xl font-bold">
                  {(transactionStats?.creditCount || 0) + (transactionStats?.debitCount || 0)}
                </div>
              )}
              <FileText className="h-5 w-5 text-forex-muted" />
            </div>
            {!statsLoading && (
              <div className="text-xs text-forex-muted mt-2">
                Recent (30d): {transactionStats?.recentTransactions || 0}
              </div>
            )}
          </CardContent>
        </Card>

        <Card className="bg-forex-darker border-forex-border">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-forex-muted">Total Credits</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              {statsLoading ? (
                <Skeleton className="h-8 w-20" />
              ) : (
                <div className="text-2xl font-bold text-green-500">${transactionStats?.totalCredits || 0}</div>
              )}
              <ArrowUp className="h-5 w-5 text-green-500" />
            </div>
            {!statsLoading && (
              <div className="text-xs text-forex-muted mt-2">
                Count: {transactionStats?.creditCount || 0}
              </div>
            )}
          </CardContent>
        </Card>

        <Card className="bg-forex-darker border-forex-border">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-forex-muted">Total Debits</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              {statsLoading ? (
                <Skeleton className="h-8 w-20" />
              ) : (
                <div className="text-2xl font-bold text-red-500">${transactionStats?.totalDebits || 0}</div>
              )}
              <ArrowDown className="h-5 w-5 text-red-500" />
            </div>
            {!statsLoading && (
              <div className="text-xs text-forex-muted mt-2">
                Count: {transactionStats?.debitCount || 0}
              </div>
            )}
          </CardContent>
        </Card>

        <Card className="bg-forex-darker border-forex-border">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-forex-muted">Net Balance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              {statsLoading ? (
                <Skeleton className="h-8 w-20" />
              ) : (
                <div className={`text-2xl font-bold ${(transactionStats?.netBalance || 0) >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                  ${transactionStats?.netBalance || 0}
                </div>
              )}
              {!statsLoading && (transactionStats?.netBalance || 0) >= 0 ? (
                <DollarSign className="h-5 w-5 text-green-500" />
              ) : (
                <DollarSign className="h-5 w-5 text-red-500" />
              )}
            </div>
            {!statsLoading && (
              <div className="text-xs text-forex-muted mt-2">
                Filtered: ${totalCredits - totalDebits}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="bg-forex-darker border-forex-border">
        <CardHeader className="pb-3">
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-forex-muted" />
                <Input
                  placeholder="Search by username or reason..."
                  className="pl-9"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Select
                value={typeFilter}
                onValueChange={setTypeFilter}
              >
                <SelectTrigger className="w-[150px]">
                  <div className="flex items-center gap-2">
                    <Filter className="h-4 w-4" />
                    <SelectValue placeholder="Transaction type" />
                  </div>
                </SelectTrigger>
                <SelectContent className="bg-forex-darker border-forex-border">
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="credit">Credits</SelectItem>
                  <SelectItem value="debit">Debits</SelectItem>
                </SelectContent>
              </Select>
              <DateRangePicker
                date={dateRange}
                onDateChange={setDateRange}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Transactions Tabs */}
      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-3 mb-4">
          <TabsTrigger value="all">All Transactions</TabsTrigger>
          <TabsTrigger value="credit">Credits</TabsTrigger>
          <TabsTrigger value="debit">Debits</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="mt-0">
          {renderTransactionsTable()}
        </TabsContent>

        <TabsContent value="credit" className="mt-0">
          {renderTransactionsTable()}
        </TabsContent>

        <TabsContent value="debit" className="mt-0">
          {renderTransactionsTable()}
        </TabsContent>
      </Tabs>
    </div>
  );

  // Helper function to render transactions table
  function renderTransactionsTable() {
    return (
      <Card className="bg-forex-darker border-forex-border">
        <CardHeader className="pb-3">
          <CardTitle>Transactions</CardTitle>
          <CardDescription>
            {filteredTransactions.length} {filteredTransactions.length === 1 ? 'transaction' : 'transactions'} found
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, index) => (
                <div key={index} className="flex items-center space-x-4">
                  <Skeleton className="h-12 w-12 rounded-full" />
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-[250px]" />
                    <Skeleton className="h-4 w-[200px]" />
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="rounded-md border border-forex-border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[80px]">ID</TableHead>
                    <TableHead>
                      <Button
                        variant="ghost"
                        onClick={() => toggleSort('username')}
                        className="flex items-center gap-1 p-0 hover:bg-transparent"
                      >
                        User
                        <ArrowUpDown className="h-4 w-4" />
                      </Button>
                    </TableHead>
                    <TableHead>
                      <Button
                        variant="ghost"
                        onClick={() => toggleSort('amount')}
                        className="flex items-center gap-1 p-0 hover:bg-transparent"
                      >
                        Amount
                        <ArrowUpDown className="h-4 w-4" />
                      </Button>
                    </TableHead>
                    <TableHead>
                      <Button
                        variant="ghost"
                        onClick={() => toggleSort('type')}
                        className="flex items-center gap-1 p-0 hover:bg-transparent"
                      >
                        Type
                        <ArrowUpDown className="h-4 w-4" />
                      </Button>
                    </TableHead>
                    <TableHead>
                      <Button
                        variant="ghost"
                        onClick={() => toggleSort('reason')}
                        className="flex items-center gap-1 p-0 hover:bg-transparent"
                      >
                        Reason
                        <ArrowUpDown className="h-4 w-4" />
                      </Button>
                    </TableHead>
                    <TableHead>
                      <Button
                        variant="ghost"
                        onClick={() => toggleSort('timestamp')}
                        className="flex items-center gap-1 p-0 hover:bg-transparent"
                      >
                        Timestamp
                        <ArrowUpDown className="h-4 w-4" />
                      </Button>
                    </TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredTransactions.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8 text-forex-muted">
                        No transactions found
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredTransactions.map((transaction) => (
                      <TableRow key={transaction.id}>
                        <TableCell className="font-medium">{transaction.id}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <div className="h-8 w-8 rounded-full bg-forex-primary flex items-center justify-center text-white">
                              {transaction.username.charAt(0).toUpperCase()}
                            </div>
                            <div>
                              <div>{transaction.username}</div>
                              <div className="text-xs text-forex-muted">ID: {transaction.userId}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className={transaction.type === 'credit' ? 'text-green-500' : 'text-red-500'}>
                          ${transaction.amount}
                        </TableCell>
                        <TableCell>
                          {getTransactionTypeBadge(transaction.type)}
                        </TableCell>
                        <TableCell>{transaction.reason}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3 text-forex-muted" />
                            <span>{formatDate(transaction.timestamp)}</span>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent className="bg-forex-darker border-forex-border">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>
                                <User className="h-4 w-4 mr-2" />
                                View User
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Wallet className="h-4 w-4 mr-2" />
                                View User Wallet
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex items-center justify-between">
          <div className="text-sm text-forex-muted">
            Showing {filteredTransactions.length > 0 ? (pagination.page - 1) * pagination.limit + 1 : 0} to {Math.min(pagination.page * pagination.limit, pagination.totalCount)} of {pagination.totalCount} transactions
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => changePage(pagination.page - 1)}
              disabled={pagination.page <= 1 || loading}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => changePage(pagination.page + 1)}
              disabled={pagination.page >= pagination.totalPages || loading}
            >
              Next
            </Button>
          </div>
        </CardFooter>
      </Card>
    );
  }
};

export default TransactionManager;
