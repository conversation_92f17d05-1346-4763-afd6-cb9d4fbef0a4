/**
 * Prize Distribution List Component
 * @description Component for displaying prize distributions for a challenge
 * @version 1.0.0
 * @status stable
 */

import React, { useState } from 'react';
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle, 
  CardDescription,
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow,
  Badge,
  Button,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger
} from '@/common/components/ui';
import { 
  Search, 
  CheckCircle2, 
  DollarSign, 
  ArrowUpDown, 
  Filter, 
  RefreshCw,
  CheckCheck,
  AlertCircle,
  Wallet,
  Trophy,
  Clock
} from 'lucide-react';
import { formatCurrency, formatDate } from '@/common/utils/formatters';
import { apiService } from '@/common/services/api';
import { toast } from 'sonner';
import BatchPaymentForm from './BatchPaymentForm';

interface PrizeDistribution {
  id: number;
  challengeId: number;
  userId: string;
  rank: number;
  amount: number;
  status: 'pending' | 'approved' | 'paid';
  paymentAddress?: string;
  paymentTxHash?: string;
  paymentTimestamp?: string;
  approvalTimestamp?: string;
  createdAt: string;
  updatedAt: string;
  user: {
    username: string;
    email: string;
    cryptoAddress?: string;
  };
}

interface Challenge {
  id: number;
  type: string;
  prizePool: number;
  status: string;
}

interface PrizeDistributionListProps {
  challengeId: number;
  prizeDistributions: PrizeDistribution[];
  isLoading: boolean;
  onRefresh: () => void;
  challenge?: Challenge;
}

/**
 * Prize Distribution List Component
 * @param props - Component props
 * @returns Prize Distribution List Component
 */
const PrizeDistributionList: React.FC<PrizeDistributionListProps> = ({ 
  challengeId,
  prizeDistributions,
  isLoading,
  onRefresh,
  challenge
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sortField, setSortField] = useState<string>('rank');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [isApproveAllDialogOpen, setIsApproveAllDialogOpen] = useState(false);
  const [isApprovingAll, setIsApprovingAll] = useState(false);
  const [selectedTab, setSelectedTab] = useState<string>('all');

  // Handle sort
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Filter and sort prize distributions
  const filteredDistributions = prizeDistributions
    .filter(distribution => {
      // Apply search filter
      const searchMatch = 
        distribution.user.username.toLowerCase().includes(searchQuery.toLowerCase()) ||
        distribution.user.email.toLowerCase().includes(searchQuery.toLowerCase());
      
      // Apply status filter
      const statusMatch = 
        statusFilter === 'all' || 
        distribution.status === statusFilter;
      
      // Apply tab filter
      const tabMatch = 
        selectedTab === 'all' || 
        (selectedTab === 'top3' && distribution.rank <= 3) ||
        (selectedTab === 'credits' && distribution.rank > 3);
      
      return searchMatch && statusMatch && tabMatch;
    })
    .sort((a, b) => {
      if (sortField === 'rank' || sortField === 'amount') {
        return sortDirection === 'asc' 
          ? a[sortField] - b[sortField]
          : b[sortField] - a[sortField];
      } else if (sortField === 'status') {
        return sortDirection === 'asc'
          ? a[sortField].localeCompare(b[sortField])
          : b[sortField].localeCompare(a[sortField]);
      } else if (sortField === 'username') {
        return sortDirection === 'asc'
          ? a.user.username.localeCompare(b.user.username)
          : b.user.username.localeCompare(a.user.username);
      }
      return 0;
    });

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="bg-amber-500/10 text-amber-500 border-amber-500/20">Pending</Badge>;
      case 'approved':
        return <Badge variant="outline" className="bg-blue-500/10 text-blue-500 border-blue-500/20">Approved</Badge>;
      case 'paid':
        return <Badge variant="outline" className="bg-green-500/10 text-green-500 border-green-500/20">Paid</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Handle approve all
  const handleApproveAll = async () => {
    setIsApprovingAll(true);
    try {
      await apiService.post(`/api/prize-distributions/challenges/${challengeId}/approve-all`);
      toast.success('All prize distributions approved successfully');
      onRefresh();
      setIsApproveAllDialogOpen(false);
    } catch (error) {
      console.error('Error approving all prize distributions:', error);
      toast.error('Failed to approve all prize distributions');
    } finally {
      setIsApprovingAll(false);
    }
  };

  // Handle approve single
  const handleApproveSingle = async (id: number) => {
    try {
      await apiService.post(`/api/prize-distributions/${id}/approve`);
      toast.success('Prize distribution approved successfully');
      onRefresh();
    } catch (error) {
      console.error('Error approving prize distribution:', error);
      toast.error('Failed to approve prize distribution');
    }
  };

  // Count distributions by status
  const pendingCount = prizeDistributions.filter(d => d.status === 'pending').length;
  const approvedCount = prizeDistributions.filter(d => d.status === 'approved').length;
  const paidCount = prizeDistributions.filter(d => d.status === 'paid').length;

  // Calculate total amounts by status
  const pendingAmount = prizeDistributions
    .filter(d => d.status === 'pending')
    .reduce((sum, d) => sum + d.amount, 0);
  
  const approvedAmount = prizeDistributions
    .filter(d => d.status === 'approved')
    .reduce((sum, d) => sum + d.amount, 0);
  
  const paidAmount = prizeDistributions
    .filter(d => d.status === 'paid')
    .reduce((sum, d) => sum + d.amount, 0);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="w-8 h-8 border-4 border-forex-primary border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-3">
          <div className="flex justify-between items-start">
            <div>
              <CardTitle>
                Prize Distributions
                {challenge && (
                  <span className="ml-2 text-sm font-normal text-forex-muted">
                    {challenge.type.charAt(0).toUpperCase() + challenge.type.slice(1)} Challenge
                  </span>
                )}
              </CardTitle>
              <CardDescription>
                Manage prize distributions for this challenge
              </CardDescription>
            </div>
            <div className="flex space-x-2">
              {pendingCount > 0 && (
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => setIsApproveAllDialogOpen(true)}
                >
                  <CheckCheck className="h-4 w-4 mr-2" />
                  Approve All Pending
                </Button>
              )}
              {approvedCount > 0 && (
                <BatchPaymentForm 
                  challengeId={challengeId} 
                  prizeDistributions={prizeDistributions.filter(d => d.status === 'approved')}
                  onSuccess={onRefresh}
                />
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center">
                  <Clock className="h-4 w-4 mr-2 text-amber-500" />
                  Pending
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{pendingCount}</div>
                <p className="text-xs text-forex-muted">{formatCurrency(pendingAmount)}</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center">
                  <CheckCircle2 className="h-4 w-4 mr-2 text-blue-500" />
                  Approved
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{approvedCount}</div>
                <p className="text-xs text-forex-muted">{formatCurrency(approvedAmount)}</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center">
                  <DollarSign className="h-4 w-4 mr-2 text-green-500" />
                  Paid
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{paidCount}</div>
                <p className="text-xs text-forex-muted">{formatCurrency(paidAmount)}</p>
              </CardContent>
            </Card>
          </div>

          <Tabs value={selectedTab} onValueChange={setSelectedTab} className="mb-6">
            <TabsList>
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="top3">
                <Trophy className="h-4 w-4 mr-2" />
                Top 3 Winners
              </TabsTrigger>
              <TabsTrigger value="credits">
                <Wallet className="h-4 w-4 mr-2" />
                Credit Recipients
              </TabsTrigger>
            </TabsList>
          </Tabs>

          <div className="flex items-center space-x-4 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-forex-muted" />
              <Input
                placeholder="Search by username or email..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="paid">Paid</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {filteredDistributions.length === 0 ? (
            <div className="text-center py-8">
              <AlertCircle className="h-12 w-12 text-forex-muted mx-auto mb-3 opacity-20" />
              <p className="text-forex-muted">No prize distributions found</p>
            </div>
          ) : (
            <div className="rounded-md border border-forex-border/30">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[80px]">
                      <div 
                        className="flex items-center cursor-pointer"
                        onClick={() => handleSort('rank')}
                      >
                        Rank
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      </div>
                    </TableHead>
                    <TableHead>
                      <div 
                        className="flex items-center cursor-pointer"
                        onClick={() => handleSort('username')}
                      >
                        User
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      </div>
                    </TableHead>
                    <TableHead>
                      <div 
                        className="flex items-center cursor-pointer"
                        onClick={() => handleSort('amount')}
                      >
                        Amount
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      </div>
                    </TableHead>
                    <TableHead>
                      <div 
                        className="flex items-center cursor-pointer"
                        onClick={() => handleSort('status')}
                      >
                        Status
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      </div>
                    </TableHead>
                    <TableHead>Payment Info</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredDistributions.map((distribution) => (
                    <TableRow key={distribution.id}>
                      <TableCell className="font-medium">{distribution.rank}</TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{distribution.user.username}</div>
                          <div className="text-xs text-forex-muted">{distribution.user.email}</div>
                        </div>
                      </TableCell>
                      <TableCell>{formatCurrency(distribution.amount)}</TableCell>
                      <TableCell>{getStatusBadge(distribution.status)}</TableCell>
                      <TableCell>
                        {distribution.status === 'paid' ? (
                          <div className="text-xs">
                            <div className="font-medium">TX: {distribution.paymentTxHash}</div>
                            <div className="text-forex-muted">
                              {distribution.paymentTimestamp && formatDate(distribution.paymentTimestamp)}
                            </div>
                          </div>
                        ) : (
                          <div className="text-xs text-forex-muted">Not paid yet</div>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        {distribution.status === 'pending' && (
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleApproveSingle(distribution.id)}
                          >
                            <CheckCircle2 className="h-4 w-4 mr-2" />
                            Approve
                          </Button>
                        )}
                        {distribution.status === 'approved' && (
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => {
                              // Open payment dialog
                            }}
                          >
                            <DollarSign className="h-4 w-4 mr-2" />
                            Mark Paid
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Approve All Dialog */}
      <Dialog open={isApproveAllDialogOpen} onOpenChange={setIsApproveAllDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Approve All Pending Distributions</DialogTitle>
            <DialogDescription>
              This will approve all {pendingCount} pending prize distributions for this challenge.
              Are you sure you want to continue?
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsApproveAllDialogOpen(false)}
              disabled={isApprovingAll}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleApproveAll}
              disabled={isApprovingAll}
            >
              {isApprovingAll ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Approving...
                </>
              ) : (
                <>
                  <CheckCheck className="h-4 w-4 mr-2" />
                  Approve All
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default PrizeDistributionList;
