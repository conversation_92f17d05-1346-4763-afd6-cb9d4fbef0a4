import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

// Prompt interface
interface MotivationPrompt {
  id: string;
  text: string;
  author?: string;
}

/**
 * Motivation Prompts component
 *
 * Displays rotating motivational quotes as a minimal fixed footer.
 *
 * @component
 * @status experimental
 * @version 1.0.0
 * @example
 * <MotivationPrompts />
 */
const MotivationPrompts: React.FC = () => {
  // Trading insights quotes
  const prompts: MotivationPrompt[] = [
    {
      id: '1',
      text: "The market doesn't reward perfection; it rewards consistency and discipline."
    },
    {
      id: '2',
      text: "Always set your stop loss before entering a trade. Risk management comes first."
    },
    {
      id: '3',
      text: "The trend is your friend until it ends. Don't fight the market direction."
    },
    {
      id: '4',
      text: "The stock market is a device for transferring money from the impatient to the patient.",
      author: "<PERSON>"
    },
    {
      id: '5',
      text: "It's not about being right, it's about making money. Don't marry your positions."
    },
    {
      id: '6',
      text: "In the short run, the market is a voting machine. In the long run, it's a weighing machine.",
      author: "<PERSON>"
    },
    {
      id: '7',
      text: "Focus on the process, not the outcome. One good trade doesn't make you a genius."
    },
    {
      id: '8',
      text: "The best traders are not those who win the most, but those who lose the least."
    },
    {
      id: '9',
      text: "Your edge is not your system. Your edge is your ability to execute your system."
    },
    {
      id: '10',
      text: "The goal of a successful trader is to make the best trades. Money is secondary.",
      author: "Alexander Elder"
    },
    {
      id: '11',
      text: "The elements of good trading are cutting losses, cutting losses, and cutting losses.",
      author: "Ed Seykota"
    },
    {
      id: '12',
      text: "Risk comes from not knowing what you're doing.",
      author: "Warren Buffett"
    },
    {
      id: '13',
      text: "The key to trading success is emotional discipline. Making money has nothing to do with intelligence."
    },
    {
      id: '14',
      text: "Successful trading is about developing a set of rules and sticking to them."
    },
    {
      id: '15',
      text: "The market can remain irrational longer than you can remain solvent.",
      author: "John Maynard Keynes"
    },
    {
      id: '16',
      text: "Trading is not about being right or wrong. It's about making money when you're right and minimizing losses when you're wrong."
    },
    {
      id: '17',
      text: "The four most dangerous words in investing are: 'this time it's different.'",
      author: "Sir John Templeton"
    },
    {
      id: '18',
      text: "The only way to make money in the markets is to buy low and sell high. Everything else is a distraction."
    },
    {
      id: '19',
      text: "Patience is the key to successful trading. Wait for the right setup."
    },
    {
      id: '20',
      text: "The first rule of trading is don't lose money. The second rule is don't forget rule number one."
    },
    {
      id: '21',
      text: "Trading is simple, but not easy. The hardest part is controlling your emotions."
    },
    {
      id: '22',
      text: "The market is a device for transferring wealth from the impatient to the patient."
    },
    {
      id: '23',
      text: "Successful traders focus on risk management more than profit potential."
    },
    {
      id: '24',
      text: "The biggest risk in trading is not taking a loss, it's missing an opportunity."
    },
    {
      id: '25',
      text: "The best time to exit a trade is usually right after you enter it."
    },
    {
      id: '26',
      text: "Don't focus on making money; focus on protecting what you have.",
      author: "Paul Tudor Jones"
    },
    {
      id: '27',
      text: "The desire to constantly be in the market is what destroys most traders."
    },
    {
      id: '28',
      text: "The market will do whatever it can to prove the majority wrong."
    },
    {
      id: '29',
      text: "Trading is not about hoping. It's about calculating probabilities and managing risk."
    },
    {
      id: '30',
      text: "The most important rule in trading is to play great defense, not great offense.",
      author: "Paul Tudor Jones"
    },
    {
      id: '31',
      text: "Amateurs focus on returns. Professionals focus on risk management."
    },
    {
      id: '32',
      text: "The market doesn't care about your opinion or your feelings."
    },
    {
      id: '33',
      text: "The best traders are those who can change their minds the quickest.",
      author: "Michael Steinhardt"
    },
    {
      id: '34',
      text: "Trading is a psychological game. Master your mind before you try to master the markets."
    },
    {
      id: '35',
      text: "The market is designed to fool most of the people, most of the time.",
      author: "Jesse Livermore"
    },
    {
      id: '36',
      text: "The secret to being successful from a trading perspective is to have an indefatigable and an undying and unquenchable thirst for information and knowledge.",
      author: "Paul Tudor Jones"
    },
    {
      id: '37',
      text: "The hard work in trading comes in the preparation. The actual process of trading should be effortless."
    }
  ];

  // Start with a random quote
  const getRandomIndex = () => Math.floor(Math.random() * prompts.length);
  const [currentPromptIndex, setCurrentPromptIndex] = useState(getRandomIndex());
  const [direction, setDirection] = useState(0); // -1 for left, 1 for right, 0 for initial

  // Function to get a random quote index different from the current one
  const getRandomQuoteIndex = () => {
    if (prompts.length <= 1) return 0;

    let newIndex;
    do {
      newIndex = Math.floor(Math.random() * prompts.length);
    } while (newIndex === currentPromptIndex);

    return newIndex;
  };

  // Auto-rotate prompts randomly
  useEffect(() => {
    const interval = setInterval(() => {
      setDirection(Math.random() > 0.5 ? 1 : -1); // Random direction
      setCurrentPromptIndex(getRandomQuoteIndex());
    }, 10000);

    return () => clearInterval(interval);
  }, [currentPromptIndex, prompts.length]);

  // Animation variants for slide transitions
  const slideVariants = {
    enter: (direction: number) => ({
      x: direction > 0 ? 300 : -300,
      opacity: 0
    }),
    center: {
      x: 0,
      opacity: 1
    },
    exit: (direction: number) => ({
      x: direction < 0 ? 300 : -300,
      opacity: 0
    })
  };

  const currentPrompt = prompts[currentPromptIndex];

  return (
    <div className="flex items-center justify-center h-full overflow-hidden px-4 w-full">
      <div className="relative w-full max-w-4xl mx-auto h-full">
        <AnimatePresence mode="wait" custom={direction}>
          <motion.div
            key={currentPrompt.id}
            className="absolute flex items-center justify-center w-full h-full"
            custom={direction}
            variants={slideVariants}
            initial="enter"
            animate="center"
            exit="exit"
            transition={{
              x: { type: "spring", stiffness: 300, damping: 30 },
              opacity: { duration: 0.2 }
            }}
          >
            <div className="flex items-center justify-center w-full whitespace-nowrap text-center">
              <span className="font-light text-xs text-blue-400/80 mr-2">•</span>
              <span className="text-white/90 text-xs font-light tracking-wide">
                {currentPrompt.text}
                {currentPrompt.author && (
                  <span className="text-blue-400/90 italic ml-1 font-light">— {currentPrompt.author}</span>
                )}
              </span>
              <span className="font-light text-xs text-blue-400/80 ml-2">•</span>
            </div>
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
};

export default MotivationPrompts;