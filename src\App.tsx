const App = () => {
  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#000', color: '#fff', padding: '2rem' }}>
      <div style={{ textAlign: 'center', maxWidth: '1200px', margin: '0 auto' }}>
        <h1 style={{ fontSize: '4rem', fontWeight: 'bold', marginBottom: '2rem' }}>
          Trade. Compete. <span style={{ color: '#60a5fa' }}>Dominate.</span>
        </h1>
        <p style={{ fontSize: '1.25rem', color: '#d1d5db', marginBottom: '3rem' }}>
          Join influencer-hosted trading challenges, compete with real market data,
          and win USDC prizes paid directly to your wallet.
        </p>

        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '2rem', marginBottom: '3rem' }}>
          <div>
            <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#10b981', marginBottom: '0.5rem' }}>$125,000</div>
            <div style={{ fontSize: '0.875rem', color: '#9ca3af' }}>Total Prize Pool</div>
          </div>
          <div>
            <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#10b981', marginBottom: '0.5rem' }}>12</div>
            <div style={{ fontSize: '0.875rem', color: '#9ca3af' }}>Live Challenges</div>
          </div>
          <div>
            <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#06b6d4', marginBottom: '0.5rem' }}>2,847</div>
            <div style={{ fontSize: '0.875rem', color: '#9ca3af' }}>Active Traders</div>
          </div>
          <div>
            <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#f97316', marginBottom: '0.5rem' }}>+156%</div>
            <div style={{ fontSize: '0.875rem', color: '#9ca3af' }}>Top ROI Today</div>
          </div>
        </div>

        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem', alignItems: 'center', marginBottom: '3rem' }}>
          <button style={{
            backgroundColor: '#2563eb',
            color: 'white',
            padding: '1rem 2rem',
            borderRadius: '0.5rem',
            border: 'none',
            fontSize: '1.125rem',
            fontWeight: '600',
            cursor: 'pointer'
          }}>
            🏆 Join Live Challenges
          </button>
          <button style={{
            backgroundColor: 'transparent',
            color: '#60a5fa',
            padding: '1rem 2rem',
            borderRadius: '0.5rem',
            border: '1px solid #3b82f6',
            fontSize: '1.125rem',
            fontWeight: '600',
            cursor: 'pointer'
          }}>
            👑 Become a Host
          </button>
        </div>

        <p style={{ color: '#9ca3af', fontSize: '0.875rem' }}>
          🛡️ Base L2 Network • ✅ Licensed Market Data • 👥 No Wallet Required to Spectate
        </p>
      </div>
    </div>
  );
};

export default App;
