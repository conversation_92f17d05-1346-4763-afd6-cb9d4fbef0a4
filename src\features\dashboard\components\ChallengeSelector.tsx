import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown, Trophy, Calendar, Clock, Loader2 } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Badge,
  Button,
  Skeleton
} from '@/common/components/ui';
import { useChallengeContext, Challenge, ChallengeType } from '../context/ChallengeContext';
import ErrorBoundary from '@/common/components/ErrorBoundary';

/**
 * Challenge selector component
 *
 * Allows users to switch between different challenges or view aggregated data.
 *
 * @component
 * @example
 * <ChallengeSelector />
 */
const ChallengeSelector: React.FC = () => {
  const {
    challenges,
    selectedChallenge,
    selectChallenge,
    isAggregatedView,
    setAggregatedView,
    isLoading,
    error
  } = useChallengeContext();

  const [isOpen, setIsOpen] = useState(false);
  const [hasLoadedOnce, setHasLoadedOnce] = useState(false);

  // Set hasLoadedOnce to true once challenges are loaded
  useEffect(() => {
    if (!isLoading && challenges.length > 0 && !hasLoadedOnce) {
      setHasLoadedOnce(true);
    }
  }, [isLoading, challenges, hasLoadedOnce]);

  // Get challenge type badge color
  const getChallengeTypeColor = (type: ChallengeType): string => {
    switch (type) {
      case 'daily':
        return 'bg-blue-500/20 text-blue-300 border-blue-500/30';
      case 'weekly':
        return 'bg-green-500/20 text-green-300 border-green-500/30';
      case 'monthly':
        return 'bg-purple-500/20 text-purple-300 border-purple-500/30';
      default:
        return 'bg-gray-500/20 text-gray-300 border-gray-500/30';
    }
  };

  // Get challenge type icon
  const getChallengeTypeIcon = (type: ChallengeType) => {
    switch (type) {
      case 'daily':
        return <Clock className="h-3.5 w-3.5 mr-1.5 text-blue-300" />;
      case 'weekly':
        return <Calendar className="h-3.5 w-3.5 mr-1.5 text-green-300" />;
      case 'monthly':
        return <Trophy className="h-3.5 w-3.5 mr-1.5 text-purple-300" />;
      default:
        return null;
    }
  };

  // Handle challenge selection
  const handleChallengeChange = (value: string) => {
    if (value === 'all') {
      setAggregatedView(true);
    } else {
      selectChallenge(value);
    }
    setIsOpen(false);
  };

  // If loading and no challenges have been loaded yet, show skeleton
  if (isLoading && !hasLoadedOnce) {
    return (
      <div className="w-full max-w-md relative">
        <Skeleton className="h-10 w-full rounded-lg" />
      </div>
    );
  }

  // If error, show error state
  if (error) {
    return (
      <div className="w-full max-w-md relative">
        <div className="w-full flex items-center justify-between px-4 py-2.5 rounded-lg border border-red-500/50 bg-red-500/10 text-white">
          <span className="text-red-300">Error loading challenges</span>
        </div>
      </div>
    );
  }

  // If no challenges, show empty state
  if (!isLoading && challenges.length === 0) {
    return (
      <div className="w-full max-w-md relative">
        <div className="w-full flex items-center justify-between px-4 py-2.5 rounded-lg border border-blue-500/20 bg-[#0f1c2e]/60 text-white">
          <span className="text-gray-400">No challenges available</span>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-md relative">
      {/* Custom styled selector with animations */}
      <div
        className="relative"
        onMouseLeave={() => setIsOpen(false)}
      >
        <motion.button
          className={`w-full flex items-center justify-between px-4 py-2.5 rounded-lg border ${
            isOpen ? 'border-blue-500/50 bg-blue-500/10' : 'border-blue-500/20 bg-[#0f1c2e]/60'
          } text-white hover:bg-blue-500/10 hover:border-blue-500/30 transition-colors duration-200`}
          onClick={() => setIsOpen(!isOpen)}
          whileHover={{ scale: 1.01 }}
          whileTap={{ scale: 0.99 }}
          disabled={isLoading}
        >
          <div className="flex items-center">
            {isLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin text-blue-300" />}
            {!isLoading && !isAggregatedView && selectedChallenge && (
              getChallengeTypeIcon(selectedChallenge.type)
            )}
            <span className="truncate max-w-[200px]">
              {isLoading
                ? 'Loading challenges...'
                : isAggregatedView
                  ? 'All Challenges'
                  : selectedChallenge?.name || 'Select a challenge'}
            </span>
          </div>

          <div className="flex items-center">
            {!isLoading && !isAggregatedView && selectedChallenge && (
              <Badge className={`${getChallengeTypeColor(selectedChallenge.type)} border text-xs mr-2`}>
                {selectedChallenge.type}
              </Badge>
            )}
            <motion.div
              animate={{ rotate: isOpen ? 180 : 0 }}
              transition={{ duration: 0.2 }}
            >
              <ChevronDown size={16} className="text-blue-300" />
            </motion.div>
          </div>
        </motion.button>

        {/* Dropdown menu */}
        <AnimatePresence>
          {isOpen && !isLoading && (
            <motion.div
              className="absolute top-full left-0 right-0 mt-1 bg-[#0f1c2e] border border-blue-500/20 rounded-lg shadow-lg overflow-hidden z-50"
              initial={{ opacity: 0, y: -10, height: 0 }}
              animate={{ opacity: 1, y: 0, height: 'auto' }}
              exit={{ opacity: 0, y: -10, height: 0 }}
              transition={{ duration: 0.2 }}
            >
              {/* All challenges option */}
              <motion.div
                className={`px-4 py-2.5 cursor-pointer ${
                  isAggregatedView ? 'bg-blue-500/20' : 'hover:bg-blue-500/10'
                } transition-colors duration-200`}
                onClick={() => handleChallengeChange('all')}
                whileHover={{ x: 5 }}
                transition={{ duration: 0.2 }}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Trophy className="h-3.5 w-3.5 mr-1.5 text-blue-300" />
                    <span>All Challenges</span>
                  </div>
                  <Badge className="bg-blue-500/20 text-blue-300 border border-blue-500/30 text-xs">
                    {challenges.length}
                  </Badge>
                </div>
              </motion.div>

              {/* Divider */}
              <div className="h-px bg-blue-500/10 mx-2" />

              {/* Challenge options */}
              <div className="max-h-60 overflow-y-auto py-1 scrollbar-thin scrollbar-thumb-blue-500/20 scrollbar-track-transparent">
                {challenges.map((challenge: Challenge, index) => (
                  <motion.div
                    key={challenge.id}
                    className={`px-4 py-2.5 cursor-pointer ${
                      selectedChallenge?.id === challenge.id ? 'bg-blue-500/20' : 'hover:bg-blue-500/10'
                    } transition-colors duration-200`}
                    onClick={() => handleChallengeChange(challenge.id)}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.2, delay: index * 0.05 }}
                    whileHover={{ x: 5 }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        {getChallengeTypeIcon(challenge.type)}
                        <span className="truncate max-w-[150px]">{challenge.name}</span>
                      </div>
                      <Badge className={`${getChallengeTypeColor(challenge.type)} border text-xs`}>
                        {challenge.type}
                      </Badge>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

// Wrap the component with an error boundary
const ChallengeSelectorWithErrorBoundary = () => {
  return (
    <ErrorBoundary
      componentName="ChallengeSelector"
      containError={true}
    >
      <ChallengeSelector />
    </ErrorBoundary>
  );
};

export default ChallengeSelectorWithErrorBoundary;
