/**
 * Prize Distribution Manager Component
 * @description Component for managing prize distributions in the admin panel
 * @version 1.0.0
 * @status stable
 */

import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { apiService } from '@/common/services/api';
import { toast } from 'sonner';
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle, 
  CardDescription,
  Tabs, 
  TabsContent, 
  TabsList, 
  TabsTrigger,
  Button
} from '@/common/components/ui';
import { RefreshCw, Filter, Award, CheckCircle2, DollarSign } from 'lucide-react';
import CompletedChallengesList from './prize-distribution/CompletedChallengesList';
import PrizeDistributionList from './prize-distribution/PrizeDistributionList';
import PrizeDistributionDetails from './prize-distribution/PrizeDistributionDetails';

/**
 * Prize Distribution Manager Component
 * @returns Prize Distribution Manager Component
 */
const PrizeDistributionManager: React.FC = () => {
  const [selectedChallengeId, setSelectedChallengeId] = useState<number | null>(null);
  const [selectedTab, setSelectedTab] = useState<string>('challenges');
  const [refreshing, setRefreshing] = useState<boolean>(false);

  // Fetch completed challenges
  const { 
    data: completedChallenges = [], 
    isLoading: isLoadingChallenges,
    refetch: refetchChallenges
  } = useQuery({
    queryKey: ['admin', 'completed-challenges'],
    queryFn: async () => {
      const response = await apiService.get('/api/challenges', { 
        params: { status: 'completed' } 
      });
      return response.data;
    }
  });

  // Fetch prize distributions for selected challenge
  const { 
    data: prizeDistributions = [],
    isLoading: isLoadingDistributions,
    refetch: refetchDistributions
  } = useQuery({
    queryKey: ['admin', 'prize-distributions', selectedChallengeId],
    queryFn: async () => {
      if (!selectedChallengeId) return [];
      const response = await apiService.get(`/api/prize-distributions/challenges/${selectedChallengeId}`);
      return response.data;
    },
    enabled: !!selectedChallengeId
  });

  // Handle challenge selection
  const handleChallengeSelect = (challengeId: number) => {
    setSelectedChallengeId(challengeId);
    setSelectedTab('distributions');
  };

  // Handle refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await refetchChallenges();
      if (selectedChallengeId) {
        await refetchDistributions();
      }
      toast.success('Data refreshed successfully');
    } catch (error) {
      toast.error('Failed to refresh data');
      console.error('Error refreshing data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Prize Distribution Management</h2>
        <Button 
          onClick={handleRefresh} 
          variant="outline" 
          size="sm"
          disabled={refreshing}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="challenges" className="flex items-center">
            <Award className="h-4 w-4 mr-2" />
            Completed Challenges
          </TabsTrigger>
          <TabsTrigger 
            value="distributions" 
            className="flex items-center"
            disabled={!selectedChallengeId}
          >
            <DollarSign className="h-4 w-4 mr-2" />
            Prize Distributions
          </TabsTrigger>
        </TabsList>

        <TabsContent value="challenges">
          <Card>
            <CardHeader>
              <CardTitle>Completed Challenges</CardTitle>
              <CardDescription>
                View and manage prize distributions for completed challenges
              </CardDescription>
            </CardHeader>
            <CardContent>
              <CompletedChallengesList 
                challenges={completedChallenges}
                isLoading={isLoadingChallenges}
                onSelectChallenge={handleChallengeSelect}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="distributions">
          {selectedChallengeId ? (
            <PrizeDistributionList 
              challengeId={selectedChallengeId}
              prizeDistributions={prizeDistributions}
              isLoading={isLoadingDistributions}
              onRefresh={refetchDistributions}
              challenge={completedChallenges.find(c => c.id === selectedChallengeId)}
            />
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8">
                  <p className="text-forex-muted">Select a challenge to view prize distributions</p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default PrizeDistributionManager;
