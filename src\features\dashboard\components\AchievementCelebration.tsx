import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Award, X, Star, Trophy } from 'lucide-react';
import { But<PERSON> } from '@/common/components/ui';

// Achievement interface
export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: React.ElementType;
  rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary';
}

interface AchievementCelebrationProps {
  achievement: Achievement | null;
  onClose: () => void;
}

/**
 * Achievement Celebration component
 *
 * Displays a visual celebration when a user completes an achievement.
 *
 * @component
 * @status experimental
 * @version 1.0.0
 * @example
 * <AchievementCelebration achievement={completedAchievement} onClose={() => setCompletedAchievement(null)} />
 */
const AchievementCelebration: React.FC<AchievementCelebrationProps> = ({
  achievement,
  onClose
}) => {
  const [showParticles, setShowParticles] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  // Handle closing with exit animation
  const handleClose = () => {
    console.log("Closing achievement celebration...");
    setIsVisible(false);
    setShowParticles(false);

    // Wait for exit animation to complete before calling onClose
    setTimeout(() => {
      console.log("Calling onClose callback...");
      onClose();
    }, 300);
  };

  // Set visible when achievement changes
  useEffect(() => {
    if (achievement) {
      setIsVisible(true);

      const timer = setTimeout(() => {
        setShowParticles(true);
      }, 500);

      return () => {
        clearTimeout(timer);
        setShowParticles(false);
      };
    }
  }, [achievement]);

  // Get rarity style
  const getRarityStyle = (rarity: string) => {
    switch (rarity) {
      case 'common':
        return 'from-blue-500 to-blue-700';
      case 'uncommon':
        return 'from-green-500 to-green-700';
      case 'rare':
        return 'from-purple-500 to-purple-700';
      case 'epic':
        return 'from-amber-500 to-amber-700';
      case 'legendary':
        return 'from-amber-500 to-amber-700';
      default:
        return 'from-blue-500 to-blue-700';
    }
  };

  // Get rarity text color
  const getRarityTextColor = (rarity: string) => {
    switch (rarity) {
      case 'common':
        return 'text-blue-300';
      case 'uncommon':
        return 'text-green-300';
      case 'rare':
        return 'text-purple-300';
      case 'epic':
        return 'text-amber-300';
      case 'legendary':
        return 'text-amber-300';
      default:
        return 'text-blue-300';
    }
  };

  // No achievement, no render
  if (!achievement) return null;

  // Return the celebration popup
  return (
    <AnimatePresence mode="wait">
      {isVisible && (
        <motion.div
          className="fixed inset-0 flex items-center justify-center z-50 bg-black/60 backdrop-blur-sm"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={handleClose}
          key="achievement-modal"
        >
          <motion.div
            className="relative bg-[#0f1c2e] border border-blue-500/30 rounded-lg shadow-xl w-full max-w-md overflow-hidden"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.8, opacity: 0 }}
            transition={{ type: "spring", stiffness: 300, damping: 25 }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close button */}
            <Button
              variant="ghost"
              size="sm"
              className="absolute top-2 right-2 text-gray-400 hover:text-white hover:bg-transparent z-10"
              onClick={handleClose}
            >
              <X className="h-5 w-5" />
            </Button>

            {/* Background gradient and stars */}
            <div className={`absolute inset-0 bg-gradient-to-b ${getRarityStyle(achievement.rarity)} opacity-20`} />

            {/* Particle animation */}
            {showParticles && (
              <div className="absolute inset-0 overflow-hidden pointer-events-none">
                {Array.from({ length: 30 }).map((_, i) => (
                  <motion.div
                    key={i}
                    className="absolute"
                    initial={{
                      x: "50%",
                      y: "50%",
                      opacity: 0
                    }}
                    animate={{
                      x: `${Math.random() * 100}%`,
                      y: `${Math.random() * 100}%`,
                      opacity: [0, 1, 0],
                      scale: [0, 1, 0.5]
                    }}
                    transition={{
                      duration: 2 + Math.random() * 2,
                      delay: Math.random() * 0.5
                    }}
                  >
                    <Star
                      className={`h-${Math.floor(Math.random() * 3) + 2} w-${Math.floor(Math.random() * 3) + 2} ${
                        achievement.rarity === 'legendary' ? 'text-amber-300' :
                        achievement.rarity === 'epic' ? 'text-amber-300' :
                        achievement.rarity === 'rare' ? 'text-purple-300' :
                        achievement.rarity === 'uncommon' ? 'text-green-300' : 'text-blue-300'
                      }`}
                    />
                  </motion.div>
                ))}
              </div>
            )}

            <div className="absolute inset-0 overflow-hidden">
              {Array.from({ length: 20 }).map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute"
                  initial={{
                    x: Math.random() * 100 - 50 + "%",
                    y: Math.random() * 100 - 50 + "%",
                    opacity: 0,
                    scale: 0
                  }}
                  animate={{
                    opacity: [0, 0.7, 0],
                    scale: [0, 1, 0.5],
                    rotate: Math.random() * 360
                  }}
                  transition={{
                    duration: 3 + Math.random() * 5,
                    repeat: Infinity,
                    delay: Math.random() * 5
                  }}
                >
                  <Star className="h-3 w-3 text-white opacity-40" />
                </motion.div>
              ))}
            </div>

            {/* Header */}
            <div className="pt-8 pb-4 px-6 text-center">
              <motion.div
                className="mb-2 text-center"
                initial={{ y: -20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.3 }}
              >
                <span className="text-sm font-medium text-blue-300">Achievement Unlocked!</span>
              </motion.div>

              <motion.div
                className="inline-flex justify-center p-4 rounded-full bg-gradient-to-r from-blue-500/20 to-blue-700/20 border border-blue-500/30 mb-4"
                initial={{ scale: 0.5, opacity: 0 }}
                animate={{ scale: 1, opacity: 1, rotate: [0, 5, 0, -5, 0] }}
                transition={{
                  duration: 0.7,
                  delay: 0.2,
                  rotate: { repeat: 1, duration: 0.5, delay: 1 }
                }}
              >
                {achievement.icon ? (
                  <achievement.icon className="h-12 w-12 text-white" />
                ) : (
                  <Award className="h-12 w-12 text-white" />
                )}
              </motion.div>

              <motion.h2
                className="text-2xl font-bold text-white mb-2"
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.4 }}
              >
                {achievement.name}
              </motion.h2>

              <motion.div
                className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium capitalize ${getRarityTextColor(achievement.rarity)} bg-blue-500/10 border border-blue-500/20`}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5 }}
              >
                <Trophy className="h-3 w-3 mr-1" />
                {achievement.rarity}
              </motion.div>

              <motion.p
                className="mt-4 text-gray-300 text-sm"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.6 }}
              >
                {achievement.description}
              </motion.p>
            </div>

            {/* Footer */}
            <motion.div
              className="px-6 py-4 border-t border-blue-500/20 bg-blue-500/5 flex justify-center"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.7 }}
            >
              <Button
                onClick={(e) => {
                  e.stopPropagation(); // Prevent event bubbling
                  handleClose();
                }}
                className="px-8"
              >
                Awesome!
              </Button>
            </motion.div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default AchievementCelebration;