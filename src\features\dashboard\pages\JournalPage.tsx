import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import {
  Search,
  Plus,
  Filter,
  Calendar,
  Menu,
  MoreHorizontal,
  ChevronDown,
  Clock,
  ArrowLeft,
  X,
  Trash2,
  Command,
  Home,
  LayoutGrid,
  Coffee,
  Pencil,
  FileText,
  Star,
  StarOff,
  LayoutList,
  LayoutGrid as GridIcon,
  BookOpen,
  Image as ImageIcon,
  AlertCircle,
  Tag
} from 'lucide-react';
import {
  Button,
  Input,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
  DropdownMenuSeparator,
  DropdownMenuLabel,
  DropdownMenuItem
} from '@/common/components/ui';
import { DashboardLayout } from '@/features/dashboard';
import JournalEntryList from '../components/journal/JournalEntryList';
import JournalEntryEditor from '../components/journal/JournalEntryEditor';
import { JournalEntry, getJournalEntries, getJournalEntryById, toggleFavoriteJournalEntry, deleteJournalEntry } from '../services/journalService';

/**
 * Journal Page Component
 *
 * A Notion-style journal page for traders to record thoughts, strategies, and trade analyses
 *
 * @component
 * @version 3.2.0
 * @status stable
 */
const JournalPage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isCreatingEntry, setIsCreatingEntry] = useState(false);
  const [selectedEntry, setSelectedEntry] = useState<JournalEntry | null>(null);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [dateFilter, setDateFilter] = useState<'all' | 'today' | 'week' | 'month'>('all');
  const [showSidebar, setShowSidebar] = useState(true);
  const [view, setView] = useState<'list' | 'board'>('board');
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [favoriteEntries, setFavoriteEntries] = useState<(string | number)[]>([]);
  const [sidebarWidth] = useState(240);
  const [hoveredCard, setHoveredCard] = useState<string | number | null>(null);
  const [activeView, setActiveView] = useState<'home' | 'all' | 'recent' | 'lessons' | 'favorites'>('home');
  const [showGuideBanner, setShowGuideBanner] = useState(true);
  const searchRef = useRef<HTMLInputElement>(null);
  const [entries, setEntries] = useState<JournalEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch journal entries
  useEffect(() => {
    const fetchEntries = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const data = await getJournalEntries();

        // If we get an empty array, that's fine - it means the user has no entries yet
        if (Array.isArray(data)) {
          setEntries(data);

          // Initialize favorite entries from data
          const favorites = data
            .filter(entry => entry.isFavorite)
            .map(entry => entry.id.toString());
          setFavoriteEntries(favorites);
        } else {
          // If we get something unexpected, set empty entries
          console.warn('Unexpected response format from journal API:', data);
          setEntries([]);
        }
      } catch (err) {
        console.error('Error fetching journal entries:', err);
        // Don't set error for empty entries - we'll show the empty state instead
        setEntries([]);
        setError('Failed to load journal entries. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchEntries();
  }, []);

  // All available tags from entries
  const allTags = Array.from(
    new Set(entries.flatMap(entry => entry.tags || []))
  ) as string[];

  // Filter entries based on search, tags, date, and active view
  const filteredEntries = entries.filter(entry => {
    // Search filter
    const matchesSearch =
      searchQuery === '' ||
      entry.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      entry.content.toLowerCase().includes(searchQuery.toLowerCase());

    // Tag filter
    const matchesTags =
      selectedTags.length === 0 ||
      selectedTags.some(tag => entry.tags?.includes(tag));

    // Date filter
    let matchesDate = true;
    const entryDate = new Date(entry.createdAt);
    const now = new Date();

    if (dateFilter === 'today') {
      matchesDate = entryDate.toDateString() === now.toDateString();
    } else if (dateFilter === 'week') {
      const weekAgo = new Date();
      weekAgo.setDate(now.getDate() - 7);
      matchesDate = entryDate >= weekAgo;
    } else if (dateFilter === 'month') {
      const monthAgo = new Date();
      monthAgo.setMonth(now.getMonth() - 1);
      matchesDate = entryDate >= monthAgo;
    }

    // Active view filter
    let matchesView = true;

    if (activeView === 'recent') {
      // Show entries from the last 3 days
      const threeDaysAgo = new Date();
      threeDaysAgo.setDate(now.getDate() - 3);
      matchesView = entryDate >= threeDaysAgo;
    } else if (activeView === 'lessons') {
      // Show entries tagged with 'lesson' or containing 'lesson' in the title
      matchesView =
        (entry.tags && entry.tags.some(tag => tag.toLowerCase().includes('lesson'))) ||
        entry.title.toLowerCase().includes('lesson');
    } else if (activeView === 'favorites') {
      // Show only favorited entries
      matchesView = favoriteEntries.includes(entry.id.toString());
    } else if (activeView === 'all') {
      // Show all entries
      matchesView = true;
    }

    return matchesSearch && matchesTags && matchesDate && matchesView;
  });

  const handleCreateEntry = () => {
    setSelectedEntry(null);
    setIsCreatingEntry(true);
  };

  const handleCancelCreate = () => {
    setIsCreatingEntry(false);
    setSelectedEntry(null);
  };

  const handleEntryClick = async (entryId: string | number) => {
    console.log(`Opening journal entry with ID: ${entryId}`);

    try {
      // Convert string ID to number if needed
      const id = typeof entryId === 'string' ? parseInt(entryId) : entryId;

      // Get the entry details
      const entry = await getJournalEntryById(id);

      if (entry) {
        console.log('Found entry:', entry);
        setSelectedEntry(entry);
        setIsCreatingEntry(true);
      } else {
        console.error(`Entry with ID ${entryId} not found`);
      }
    } catch (err) {
      console.error('Error opening journal entry:', err);
    }
  };

  const handleTagToggle = (tag: string) => {
    setSelectedTags(prev =>
      prev.includes(tag)
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };

  const toggleFavorite = async (entryId: string | number, e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
    }

    try {
      // Call API to toggle favorite status
      const id = typeof entryId === 'string' ? parseInt(entryId) : entryId;
      const updatedEntry = await toggleFavoriteJournalEntry(id);

      if (updatedEntry) {
        // Update local state
        setEntries(prev =>
          prev.map(entry =>
            entry.id === entryId || entry.id.toString() === entryId.toString()
              ? { ...entry, isFavorite: updatedEntry.isFavorite }
              : entry
          )
        );

        // Update favorite entries list
        setFavoriteEntries(prev =>
          prev.includes(entryId.toString())
            ? prev.filter(id => id !== entryId.toString())
            : [...prev, entryId.toString()]
        );
      } else {
        console.warn(`Could not find entry with ID ${entryId} to toggle favorite status`);
      }
    } catch (err) {
      console.error('Error toggling favorite status:', err);
      // Show error message if needed
    }
  };

  const handleDeleteEntry = async (entryId: string | number, e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
    }

    if (!window.confirm('Are you sure you want to delete this journal entry? This action cannot be undone.')) {
      return;
    }

    try {
      // Call API to delete entry
      const id = typeof entryId === 'string' ? parseInt(entryId) : entryId;
      await deleteJournalEntry(id);

      // Update local state
      setEntries(prev => prev.filter(entry =>
        entry.id !== entryId && entry.id.toString() !== entryId.toString()
      ));

      // Remove from favorites if it was favorited
      setFavoriteEntries(prev => prev.filter(id => id !== entryId));
    } catch (err) {
      console.error('Error deleting journal entry:', err);
      // Show error message if needed
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(undefined, {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Check localStorage for banner preference
  useEffect(() => {
    const bannerDismissed = localStorage.getItem('journalGuideBannerDismissed');
    if (bannerDismissed === 'true') {
      setShowGuideBanner(false);
    }
  }, []);

  // Handle dismissing the banner
  const handleDismissBanner = () => {
    setShowGuideBanner(false);
    localStorage.setItem('journalGuideBannerDismissed', 'true');
  };

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Cmd/Ctrl + K for search
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        if (searchRef.current) {
          searchRef.current.focus();
        }
      }

      // Cmd/Ctrl + N for new entry
      if ((e.metaKey || e.ctrlKey) && e.key === 'n') {
        e.preventDefault();
        if (!isCreatingEntry) {
          handleCreateEntry();
        }
      }

      // Escape to cancel search
      if (e.key === 'Escape' && isSearchFocused) {
        if (searchRef.current) {
          searchRef.current.blur();
          setIsSearchFocused(false);
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isCreatingEntry, isSearchFocused]);

  return (
    <DashboardLayout>
      <div className="flex h-[calc(100vh-64px)] bg-[#191919] text-gray-200">
        {/* Notion-style Sidebar */}
        <AnimatePresence initial={false}>
          {showSidebar && (
            <motion.div
              initial={{ width: 0, opacity: 0 }}
              animate={{ width: sidebarWidth, opacity: 1 }}
              exit={{ width: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="h-full border-r border-[#313131] flex flex-col overflow-hidden bg-[#202020]"
            >
              <div className="p-3 border-b border-[#313131]">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-5 h-5 bg-gradient-to-br from-blue-500 to-blue-600 rounded flex items-center justify-center">
                      <span className="text-xs text-white font-medium">J</span>
                    </div>
                    <span className="font-medium text-gray-200">Trading Journal</span>
                  </div>
                </div>
              </div>

              <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-[#3a3a3a] scrollbar-track-transparent">
                <div className="px-2 py-2 my-1 text-xs font-medium text-gray-400 uppercase tracking-wider">Workspace</div>
                <div className="space-y-1 px-1">
                  <div
                    className={`flex items-center px-2 py-1.5 rounded-md cursor-pointer transition-colors duration-150 ${
                      activeView === 'home' ? 'bg-[#2e2e2e] text-gray-100' : 'hover:bg-[#2e2e2e] text-gray-300'
                    }`}
                    onClick={() => {
                      setActiveView('home');
                      setSelectedTags([]);
                      setDateFilter('all');
                    }}
                  >
                    <Home className={`h-4 w-4 mr-2 ${activeView === 'home' ? 'text-gray-100' : 'text-gray-400'}`} />
                    <span className="text-sm">Home</span>
                  </div>
                  <div
                    className={`flex items-center px-2 py-1.5 rounded-md cursor-pointer transition-colors duration-150 ${
                      activeView === 'all' ? 'bg-[#2e2e2e] text-gray-100' : 'hover:bg-[#2e2e2e] text-gray-300'
                    }`}
                    onClick={() => {
                      setActiveView('all');
                      setSelectedTags([]);
                      setDateFilter('all');
                    }}
                  >
                    <LayoutGrid className={`h-4 w-4 mr-2 ${activeView === 'all' ? 'text-gray-100' : 'text-gray-400'}`} />
                    <span className="text-sm">All Entries</span>
                  </div>
                  <div
                    className={`flex items-center px-2 py-1.5 rounded-md cursor-pointer transition-colors duration-150 ${
                      activeView === 'recent' ? 'bg-[#2e2e2e] text-gray-100' : 'hover:bg-[#2e2e2e] text-gray-300'
                    }`}
                    onClick={() => {
                      setActiveView('recent');
                      setSelectedTags([]);
                      setDateFilter('all');
                    }}
                  >
                    <Clock className={`h-4 w-4 mr-2 ${activeView === 'recent' ? 'text-gray-100' : 'text-gray-400'}`} />
                    <span className="text-sm">Recent</span>
                  </div>
                  <div
                    className={`flex items-center px-2 py-1.5 rounded-md cursor-pointer transition-colors duration-150 ${
                      activeView === 'lessons' ? 'bg-[#2e2e2e] text-gray-100' : 'hover:bg-[#2e2e2e] text-gray-300'
                    }`}
                    onClick={() => {
                      setActiveView('lessons');
                      setSelectedTags([]);
                      setDateFilter('all');
                    }}
                  >
                    <Coffee className={`h-4 w-4 mr-2 ${activeView === 'lessons' ? 'text-gray-100' : 'text-gray-400'}`} />
                    <span className="text-sm">Trading Lessons</span>
                  </div>
                  <div
                    className={`flex items-center px-2 py-1.5 rounded-md cursor-pointer transition-colors duration-150 ${
                      activeView === 'favorites' ? 'bg-[#2e2e2e] text-gray-100' : 'hover:bg-[#2e2e2e] text-gray-300'
                    }`}
                    onClick={() => {
                      setActiveView('favorites');
                      setSelectedTags([]);
                      setDateFilter('all');
                    }}
                  >
                    <Star className={`h-4 w-4 mr-2 ${activeView === 'favorites' ? 'text-gray-100' : 'text-gray-400'}`} />
                    <span className="text-sm">Favorites</span>
                    {favoriteEntries.length > 0 && (
                      <span className="ml-auto bg-blue-500/20 text-blue-300 rounded-full w-5 h-5 flex items-center justify-center text-[10px]">
                        {favoriteEntries.length}
                      </span>
                    )}
                  </div>
                </div>

                <div className="mt-6 px-2 py-2">
                  <span className="text-xs font-medium text-gray-400 uppercase tracking-wider">Tags</span>
                </div>

                <div className="mt-1 space-y-1 px-1">
                  {allTags.map(tag => (
                    <div
                      key={tag}
                      className={`flex items-center px-2 py-1.5 rounded-md cursor-pointer transition-colors duration-150 ${
                        selectedTags.includes(tag)
                          ? 'bg-[#2e2e2e] text-gray-100'
                          : 'hover:bg-[#2e2e2e] text-gray-300'
                      }`}
                      onClick={() => {
                        handleTagToggle(tag);
                        // Reset active view when selecting a tag
                        if (activeView !== 'home' && !selectedTags.includes(tag)) {
                          setActiveView('home');
                        }
                      }}
                    >
                      <div className="h-2 w-2 rounded-full bg-blue-400 mr-2"></div>
                      <span className="text-sm">{tag}</span>
                      {/* Show count of entries with this tag */}
                      <span className="ml-auto text-xs text-gray-500">
                        {entries.filter(entry => entry.tags?.includes(tag)).length}
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Bottom section with user info */}
              <div className="p-3 border-t border-[#313131]">
                <div className="flex items-center gap-2 mb-3">
                  <div className="w-6 h-6 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center">
                    <span className="text-xs text-white font-medium">TC</span>
                  </div>
                  <span className="text-sm text-gray-300">Your Journal</span>
                </div>

                {/* Show guide banner button - only visible when banner is hidden */}
                {!showGuideBanner && activeView === 'home' && (
                  <button
                    onClick={() => {
                      setShowGuideBanner(true);
                      localStorage.removeItem('journalGuideBannerDismissed');
                    }}
                    className="w-full text-xs text-gray-400 hover:text-gray-300 flex items-center justify-center gap-1.5 py-1 px-2 rounded hover:bg-[#2e2e2e] transition-colors duration-150"
                  >
                    <BookOpen className="h-3.5 w-3.5" />
                    <span>Show Journal Guide</span>
                  </button>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Top Navigation - Notion-style */}
          <div className="border-b border-[#313131] px-4 py-2 flex items-center justify-between bg-[#202020] backdrop-blur-sm sticky top-0 z-10">
            {/* Left side */}
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 text-gray-400 hover:text-gray-200 hover:bg-[#2e2e2e] rounded-md transition-colors duration-150"
                onClick={() => setShowSidebar(!showSidebar)}
              >
                <Menu className="h-4 w-4" />
              </Button>

              <div className="flex items-center gap-1 text-gray-200">
                <span className="text-sm font-medium">Trading Journal</span>
                <ChevronDown className="h-3 w-3 opacity-60" />
              </div>

              <div className="h-4 border-r border-[#3a3a3a] mx-2"></div>

              <div className="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  className={`h-7 px-2 text-xs flex items-center gap-1 transition-colors duration-150 ${view === 'list' ? 'bg-[#2e2e2e] text-gray-100' : 'hover:bg-[#2e2e2e] text-gray-400'}`}
                  onClick={() => setView('list')}
                >
                  <LayoutList className="h-3.5 w-3.5 mr-1" />
                  List
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className={`h-7 px-2 text-xs flex items-center gap-1 transition-colors duration-150 ${view === 'board' ? 'bg-[#2e2e2e] text-gray-100' : 'hover:bg-[#2e2e2e] text-gray-400'}`}
                  onClick={() => setView('board')}
                >
                  <GridIcon className="h-3.5 w-3.5 mr-1" />
                  Board
                </Button>
              </div>
            </div>

            {/* Right side */}
            <div className="flex items-center gap-2">
              {/* Search with keyboard shortcut hint - Notion style */}
              <div className="relative">
                <Input
                  ref={searchRef}
                  type="text"
                  placeholder="Search..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onFocus={() => setIsSearchFocused(true)}
                  onBlur={() => setIsSearchFocused(false)}
                  className="h-8 w-[180px] bg-[#2a2a2a] border-[#3a3a3a] rounded-md text-sm text-gray-200 placeholder-gray-400 focus-visible:ring-blue-500"
                />
                <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center gap-1 pointer-events-none">
                  <Search className="h-3.5 w-3.5 text-gray-400" />
                  <div className="text-[10px] text-gray-500 bg-[#3a3a3a] rounded px-1 py-0.5 flex items-center">
                    <Command className="h-3 w-3 mr-0.5" />
                    K
                  </div>
                </div>
              </div>

              {/* Filter dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 text-gray-400 hover:text-gray-200 hover:bg-[#2e2e2e] rounded-md transition-colors duration-150"
                  >
                    <Filter className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="bg-[#202020] border border-[#3a3a3a] shadow-md rounded-md w-48">
                  <DropdownMenuLabel className="text-xs text-gray-400">Filter by Date</DropdownMenuLabel>
                  <DropdownMenuSeparator className="bg-[#3a3a3a]" />
                  <DropdownMenuCheckboxItem
                    checked={dateFilter === 'all'}
                    onCheckedChange={() => setDateFilter('all')}
                    className="text-sm text-gray-200 cursor-pointer focus:bg-[#2e2e2e] focus:text-gray-100"
                  >
                    All Time
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={dateFilter === 'today'}
                    onCheckedChange={() => setDateFilter('today')}
                    className="text-sm text-gray-200 cursor-pointer focus:bg-[#2e2e2e] focus:text-gray-100"
                  >
                    Today
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={dateFilter === 'week'}
                    onCheckedChange={() => setDateFilter('week')}
                    className="text-sm text-gray-200 cursor-pointer focus:bg-[#2e2e2e] focus:text-gray-100"
                  >
                    Past Week
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={dateFilter === 'month'}
                    onCheckedChange={() => setDateFilter('month')}
                    className="text-sm text-gray-200 cursor-pointer focus:bg-[#2e2e2e] focus:text-gray-100"
                  >
                    Past Month
                  </DropdownMenuCheckboxItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Create new entry button */}
              <Button
                className="h-8 bg-blue-600 hover:bg-blue-500 text-white text-xs px-3 transition-colors duration-150"
                onClick={handleCreateEntry}
              >
                <Plus className="h-3.5 w-3.5 mr-1.5" />
                New Entry
              </Button>
            </div>
          </div>

          {/* Content Area - Notion-style */}
          <div className="flex-1 overflow-y-auto p-6 scrollbar-thin scrollbar-thumb-[#3a3a3a] scrollbar-track-transparent bg-[#191919]">
            {isCreatingEntry ? (
              <div className="bg-[#202020] max-w-4xl mx-auto rounded-lg border border-[#313131] shadow-lg p-4">
                <div className="mb-2 flex items-center">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 text-gray-400 hover:text-gray-200 hover:bg-[#2e2e2e] rounded-md mr-2 transition-colors duration-150"
                    onClick={handleCancelCreate}
                  >
                    <ArrowLeft className="h-4 w-4" />
                  </Button>
                  <span className="text-sm text-gray-300">Back to journal</span>
                </div>
                <JournalEntryEditor
                  onCancel={handleCancelCreate}
                  entryId={selectedEntry ? Number(selectedEntry.id) : undefined}
                  initialTitle={selectedEntry?.title}
                  initialContent={selectedEntry?.content}
                  initialTags={selectedEntry?.tags}
                  initialMood={selectedEntry?.mood}
                  initialDayProfitability={selectedEntry?.dayProfitability}
                  initialPnlAmount={selectedEntry?.pnlAmount}
                  initialImages={selectedEntry?.attachments?.filter(a => a.type === 'image').map(a => a.url)}
                  onSaveSuccess={() => {
                    // Refresh entries after saving
                    console.log('Journal entry saved, refreshing entries list...');
                    setIsLoading(true);

                    // Use setTimeout to ensure the localStorage is updated before we read it
                    setTimeout(() => {
                      getJournalEntries()
                        .then(data => {
                          console.log('Refreshed journal entries:', data);
                          setEntries(data);

                          // Update favorite entries
                          const favorites = data
                            .filter(entry => entry.isFavorite)
                            .map(entry => entry.id.toString());
                          setFavoriteEntries(favorites);
                        })
                        .catch(err => {
                          console.error('Error refreshing journal entries:', err);
                        })
                        .finally(() => {
                          setIsLoading(false);
                        });
                    }, 100); // Small delay to ensure localStorage is updated
                  }}
                />
              </div>
            ) : (
              <div className="w-full max-w-[1600px] mx-auto">
                {/* Loading state - Enhanced with better visuals */}
                {isLoading && (
                  <div className="flex flex-col items-center justify-center py-20">
                    <div className="w-20 h-20 bg-gradient-to-br from-blue-500/10 to-indigo-500/10 rounded-full flex items-center justify-center mb-6 relative">
                      <div className="absolute inset-0 rounded-full border-2 border-blue-400/20 border-t-blue-400 animate-spin"></div>
                      <FileText className="h-8 w-8 text-blue-400" />
                    </div>
                    <h3 className="text-xl font-medium text-gray-200 mb-2">Loading Your Journal</h3>
                    <p className="text-gray-400">Please wait while we retrieve your entries...</p>
                  </div>
                )}

                {/* Error state - Enhanced with better visuals */}
                {error && !isLoading && (
                  <div className="flex flex-col items-center justify-center py-20 text-center">
                    <div className="w-20 h-20 bg-red-500/10 rounded-full flex items-center justify-center mb-6">
                      <AlertCircle className="h-10 w-10 text-red-500" />
                    </div>
                    <h3 className="text-xl font-medium text-gray-200 mb-3">Unable to Load Journal</h3>
                    <p className="text-gray-300 mb-4 max-w-md">{error}</p>
                    <p className="text-gray-400 mb-6 max-w-md text-sm">This could be due to a temporary connection issue. Please try again in a moment.</p>
                    <Button
                      onClick={() => {
                        setIsLoading(true);
                        getJournalEntries()
                          .then(data => {
                            // If we get an empty array, that's fine - it means the user has no entries yet
                            if (Array.isArray(data)) {
                              setEntries(data);

                              // Initialize favorite entries from data
                              const favorites = data
                                .filter(entry => entry.isFavorite)
                                .map(entry => entry.id.toString());
                              setFavoriteEntries(favorites);

                              setError(null);
                            } else {
                              // If we get something unexpected, set empty entries
                              console.warn('Unexpected response format from journal API:', data);
                              setEntries([]);
                              setError(null);
                            }
                          })
                          .catch(err => {
                            console.error('Error refreshing journal entries:', err);
                            // Always set empty entries to show empty state
                            setEntries([]);
                            setError('Failed to load journal entries. Please try again.');
                          })
                          .finally(() => setIsLoading(false));
                      }}
                      className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-500 hover:to-indigo-500 text-white px-4 py-2 rounded-md shadow-lg hover:shadow-xl transition-all duration-200"
                    >
                      Try Again
                    </Button>
                  </div>
                )}

                {/* Empty state - Enhanced with better visuals */}
                {!isLoading && !error && entries.length === 0 && (
                  <div className="flex flex-col items-center justify-center py-20 text-center">
                    <div className="w-24 h-24 bg-gradient-to-br from-blue-500/20 to-indigo-500/20 rounded-full flex items-center justify-center mb-6">
                      <FileText className="h-12 w-12 text-blue-400" />
                    </div>
                    <h2 className="text-2xl font-semibold text-gray-200 mb-3">Your Trading Journal Awaits</h2>
                    <p className="text-gray-300 mb-2 max-w-md">Track your trading journey, record insights, and improve your performance</p>
                    <p className="text-gray-400 mb-6 max-w-md">Document your trades, analyze patterns, and build a library of trading knowledge</p>

                    <Button
                      onClick={handleCreateEntry}
                      className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-500 hover:to-indigo-500 text-white px-6 py-2 rounded-md shadow-lg hover:shadow-xl transition-all duration-200"
                    >
                      <Plus className="h-5 w-5 mr-2" />
                      Create Your First Entry
                    </Button>

                    <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4 max-w-3xl">
                      <div className="bg-[#202020] p-4 rounded-lg border border-[#313131]">
                        <div className="w-10 h-10 bg-blue-500/10 rounded-full flex items-center justify-center mb-3">
                          <Pencil className="h-5 w-5 text-blue-400" />
                        </div>
                        <h3 className="text-gray-200 font-medium mb-1">Document Trades</h3>
                        <p className="text-gray-400 text-sm">Record your trades with detailed notes and reflections</p>
                      </div>

                      <div className="bg-[#202020] p-4 rounded-lg border border-[#313131]">
                        <div className="w-10 h-10 bg-blue-500/10 rounded-full flex items-center justify-center mb-3">
                          <BookOpen className="h-5 w-5 text-blue-400" />
                        </div>
                        <h3 className="text-gray-200 font-medium mb-1">Learn & Improve</h3>
                        <p className="text-gray-400 text-sm">Track patterns and extract valuable trading lessons</p>
                      </div>

                      <div className="bg-[#202020] p-4 rounded-lg border border-[#313131]">
                        <div className="w-10 h-10 bg-blue-500/10 rounded-full flex items-center justify-center mb-3">
                          <ImageIcon className="h-5 w-5 text-blue-400" />
                        </div>
                        <h3 className="text-gray-200 font-medium mb-1">Add Screenshots</h3>
                        <p className="text-gray-400 text-sm">Attach charts and images to visualize your analysis</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Content when we have entries */}
                {!isLoading && !error && entries.length > 0 && (
                  <>
                    {/* View title */}
                    <div className="mb-4">
                      <h2 className="text-xl font-medium text-gray-100">
                        {activeView === 'home' && 'Home'}
                        {activeView === 'all' && 'All Entries'}
                        {activeView === 'recent' && 'Recent Entries'}
                        {activeView === 'lessons' && 'Trading Lessons'}
                        {activeView === 'favorites' && 'Favorites'}
                        {activeView === 'home' && selectedTags.length > 0 && ` • ${selectedTags.join(', ')}`}
                      </h2>
                      <p className="text-sm text-gray-400 mt-1">
                        {activeView === 'home' && 'Your personal trading journal dashboard'}
                        {activeView === 'all' && 'View all your journal entries'}
                        {activeView === 'recent' && 'Entries from the last 3 days'}
                        {activeView === 'lessons' && 'Insights and lessons from your trading journey'}
                        {activeView === 'favorites' && 'Your bookmarked journal entries'}
                      </p>
                    </div>

                    {/* Journal Guide Banner - Compact Version */}
                    {activeView === 'home' && showGuideBanner && (
                      <div className="mb-6 rounded-lg overflow-hidden border border-[#313131] relative">
                        {/* Dismiss button */}
                        <button
                          onClick={handleDismissBanner}
                          className="absolute top-2 right-2 z-20 bg-black/20 hover:bg-black/30 rounded-full p-1 transition-colors duration-150"
                          aria-label="Dismiss banner"
                          title="Don't show again"
                        >
                          <X className="h-3.5 w-3.5 text-white" />
                        </button>

                        <div className="bg-gradient-to-r from-blue-600 to-indigo-600 p-3 relative">
                          <div className="flex items-center gap-3 relative z-10">
                            <div className="bg-white/20 rounded-full p-2 flex-shrink-0">
                              <BookOpen className="h-5 w-5 text-white" />
                            </div>
                            <div className="flex-grow">
                              <h3 className="text-white font-medium text-sm">New to trading journals?</h3>
                              <p className="text-blue-100 text-xs mt-0.5">Learn how to use this journal to improve your trading performance</p>
                            </div>
                            <Link to="/journal-guide">
                              <Button className="bg-white text-blue-600 hover:bg-blue-50 transition-colors duration-150 h-8 px-3 text-xs">
                                Learn More
                              </Button>
                            </Link>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Active filters display - Notion style */}
                    {(selectedTags.length > 0 || dateFilter !== 'all') && (
                      <div className="mb-6 flex flex-wrap gap-2 items-center">
                        <span className="text-xs text-gray-400">Filtered by:</span>

                        {dateFilter !== 'all' && (
                          <div className="flex items-center gap-1 bg-[#2e2e2e] text-gray-200 rounded-md px-2 py-1 text-xs border border-[#3a3a3a]">
                            <Calendar className="h-3 w-3 text-gray-400" />
                            <span>
                              {dateFilter === 'today' ? 'Today' :
                               dateFilter === 'week' ? 'Past Week' : 'Past Month'}
                            </span>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-4 w-4 p-0 text-gray-400 hover:text-gray-200 hover:bg-[#3a3a3a] rounded-full ml-1 transition-colors duration-150"
                              onClick={() => setDateFilter('all')}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        )}

                        {selectedTags.map(tag => (
                          <div
                            key={tag}
                            className="flex items-center gap-1 bg-[#2e2e2e] text-gray-200 rounded-md px-2 py-1 text-xs border border-[#3a3a3a]"
                          >
                            <div className="h-2 w-2 rounded-full bg-blue-400"></div>
                            <span>{tag}</span>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-4 w-4 p-0 text-gray-400 hover:text-gray-200 hover:bg-[#3a3a3a] rounded-full ml-1 transition-colors duration-150"
                              onClick={() => handleTagToggle(tag)}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        ))}

                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 px-2 text-xs text-gray-400 hover:bg-[#2e2e2e] hover:text-gray-200 transition-colors duration-150"
                          onClick={() => {
                            setSelectedTags([]);
                            setDateFilter('all');
                          }}
                        >
                          Clear all
                        </Button>
                      </div>
                    )}

                    {/* List or Board view based on selection */}
                    {view === 'list' ? (
                      <div className="w-full max-w-4xl mx-auto">
                        <JournalEntryList entries={filteredEntries} />
                      </div>
                    ) : (
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-5">
                        {/* New Entry Card - Always First - Notion style */}
                        <div
                          className="bg-[#202020] border border-[#313131] border-dashed rounded-lg p-4 h-[280px] flex items-center justify-center cursor-pointer hover:bg-[#252525] transition-colors duration-200"
                          onClick={handleCreateEntry}
                        >
                          <div className="flex flex-col items-center text-gray-400 hover:text-gray-200 transition-colors duration-200">
                            <Plus className="h-10 w-10 mb-2" />
                            <span className="text-sm font-medium">New Entry</span>
                          </div>
                        </div>

                        {/* Entry Cards - Notion style */}
                        {filteredEntries.map(entry => (
                          <div
                            key={entry.id}
                            className="bg-[#202020] border border-[#313131] rounded-lg p-4 shadow-md hover:shadow-lg transition-all duration-200 cursor-pointer flex flex-col h-[280px] group relative overflow-hidden"
                            onMouseEnter={() => setHoveredCard(entry.id)}
                            onMouseLeave={() => setHoveredCard(null)}
                            onClick={() => handleEntryClick(entry.id)}
                          >
                            {/* Profitability indicator */}
                            {entry.dayProfitability && (
                              <div
                                className={`absolute top-0 left-0 w-full h-1 ${
                                  entry.dayProfitability === 'profitable'
                                    ? 'bg-green-500'
                                    : entry.dayProfitability === 'unprofitable'
                                      ? 'bg-red-500'
                                      : 'bg-blue-500'
                                }`}
                              />
                            )}
                            {/* Card Header */}
                            <div className="flex items-start justify-between mb-3">
                              <h3 className="font-medium text-gray-200 text-base line-clamp-2 hover:text-blue-300 transition-colors duration-200 pr-2 flex-1">
                                {entry.title}
                              </h3>

                              <div className="flex items-center flex-shrink-0 ml-1">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className={`h-6 w-6 p-0 ${favoriteEntries.includes(entry.id) ? 'text-yellow-300' : 'text-gray-500 hover:text-yellow-300'} ${hoveredCard === entry.id ? 'opacity-100' : 'opacity-0'} transition-opacity duration-200`}
                                  onClick={(e) => toggleFavorite(entry.id, e)}
                                >
                                  {favoriteEntries.includes(entry.id) ? (
                                    <Star className="h-[14px] w-[14px]" />
                                  ) : (
                                    <StarOff className="h-[14px] w-[14px]" />
                                  )}
                                </Button>
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      className={`h-6 w-6 p-0 text-gray-500 hover:text-gray-300 ${hoveredCard === entry.id ? 'opacity-100' : 'opacity-0'} transition-opacity duration-200`}
                                      onClick={(e) => e.stopPropagation()}
                                    >
                                      <MoreHorizontal className="h-[14px] w-[14px]" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent className="bg-[#202020] border border-[#3a3a3a] shadow-md rounded-md w-40">
                                    <DropdownMenuItem
                                      className="text-sm text-gray-200 cursor-pointer focus:bg-[#2e2e2e] focus:text-gray-100"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleEntryClick(entry.id);
                                      }}
                                    >
                                      <Pencil className="h-3.5 w-3.5 mr-2 text-gray-400" />
                                      Edit
                                    </DropdownMenuItem>
                                    <DropdownMenuItem className="text-sm text-gray-200 cursor-pointer focus:bg-[#2e2e2e] focus:text-gray-100">
                                      <FileText className="h-3.5 w-3.5 mr-2 text-gray-400" />
                                      Duplicate
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator className="bg-[#3a3a3a]" />
                                    <DropdownMenuItem
                                      className="text-sm text-red-300 cursor-pointer focus:bg-[#2e2e2e] focus:text-red-200"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleDeleteEntry(entry.id.toString(), e);
                                      }}
                                    >
                                      <Trash2 className="h-3.5 w-3.5 mr-2 text-red-300" />
                                      Delete
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </div>
                            </div>

                            {/* Card Content */}
                            <div className="flex-grow overflow-hidden">
                              <p className="text-sm text-gray-400 line-clamp-6 mb-2 break-words">
                                {entry.content.split('\n\n')[0]}
                              </p>
                            </div>

                            {/* Card Footer */}
                            <div className="mt-auto pt-3 border-t border-[#313131]">
                              <div className="flex items-center justify-between">
                                <div className="text-xs text-gray-500 flex items-center flex-shrink-0">
                                  <Calendar className="h-3 w-3 mr-1" />
                                  {formatDate(entry.createdAt)}
                                </div>

                                <div className="flex items-center gap-1 ml-2 overflow-hidden">
                                  {/* Image indicator */}
                                  {entry.attachments && entry.attachments.some(a => a.type === 'image') && (
                                    <div className="flex items-center gap-1 text-[10px] text-gray-400 flex-shrink-0">
                                      <ImageIcon className="h-3 w-3" />
                                      <span>{entry.attachments.filter(a => a.type === 'image').length}</span>
                                    </div>
                                  )}

                                  {/* Tags */}
                                  {entry.tags && entry.tags.length > 0 && (
                                    <div className="flex items-center gap-1 overflow-hidden">
                                      {entry.tags.slice(0, 1).map(tag => (
                                        <div
                                          key={tag}
                                          className="text-[10px] px-1.5 py-0.5 bg-[#2e2e2e] text-gray-300 rounded border border-[#3a3a3a] whitespace-nowrap overflow-hidden text-ellipsis max-w-[60px]"
                                        >
                                          {tag}
                                        </div>
                                      ))}
                                      {entry.tags.length > 1 && (
                                        <span className="text-[10px] text-gray-500 flex-shrink-0">+{entry.tags.length - 1}</span>
                                      )}
                                    </div>
                                  )}

                                  {/* Mood */}
                                  {entry.mood && (
                                    <div className="text-lg flex-shrink-0" title={`Mood: ${entry.mood}`}>
                                      {entry.mood === 'positive' ? '😊' : entry.mood === 'neutral' ? '😐' : '😔'}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>

                            {/* Quick action overlay - only visible on hover - Notion style */}
                            <div
                              className={`absolute inset-0 bg-gradient-to-t from-[#202020]/90 via-transparent to-transparent rounded-lg flex items-end justify-center p-4 transition-opacity duration-200 ${hoveredCard === entry.id ? 'opacity-100' : 'opacity-0'} pointer-events-none`}
                            >
                              <div className="flex items-center gap-2 pointer-events-auto max-w-full overflow-hidden">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="h-8 px-3 text-xs bg-[#2a2a2a] hover:bg-[#333333] text-gray-200 border-[#3a3a3a] rounded-md transition-colors duration-150 whitespace-nowrap"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleEntryClick(entry.id);
                                  }}
                                >
                                  <Pencil className="h-3.5 w-3.5 mr-1.5 flex-shrink-0" />
                                  <span className="truncate">Edit</span>
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="h-8 w-8 p-0 bg-[#2a2a2a] hover:bg-[#333333] text-gray-200 border-[#3a3a3a] rounded-md transition-colors duration-150 flex-shrink-0"
                                  onClick={(e) => toggleFavorite(entry.id, e)}
                                >
                                  {favoriteEntries.includes(entry.id) ? (
                                    <Star className="h-3.5 w-3.5 text-yellow-300" />
                                  ) : (
                                    <Star className="h-3.5 w-3.5" />
                                  )}
                                </Button>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}

                    {/* Empty state - Enhanced with better visuals */}
                    {filteredEntries.length === 0 && (
                      <div className="mt-12 text-center max-w-md mx-auto py-12 bg-[#202020] rounded-lg border border-[#313131]">
                        <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-500/10 to-indigo-500/10 rounded-full mb-6">
                          {searchQuery ? (
                            <Search className="h-10 w-10 text-blue-400" />
                          ) : selectedTags.length > 0 ? (
                            <Tag className="h-10 w-10 text-blue-400" />
                          ) : dateFilter !== 'all' ? (
                            <Calendar className="h-10 w-10 text-blue-400" />
                          ) : (
                            <Pencil className="h-10 w-10 text-blue-400" />
                          )}
                        </div>
                        <h3 className="text-xl font-medium text-gray-200 mb-3">No Journal Entries Found</h3>
                        <p className="text-sm text-gray-400 mb-6 max-w-sm mx-auto px-4">
                          {searchQuery
                            ? `No entries match your search "${searchQuery}"`
                            : selectedTags.length > 0 || dateFilter !== 'all'
                              ? "No entries match your current filters"
                              : "Create your first journal entry to start tracking your trading journey"
                          }
                        </p>
                        {searchQuery || selectedTags.length > 0 || dateFilter !== 'all' ? (
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-sm border-[#3a3a3a] bg-[#2a2a2a] hover:bg-[#2e2e2e] text-gray-200 transition-colors duration-150 px-4"
                            onClick={() => {
                              setSearchQuery('');
                              setSelectedTags([]);
                              setDateFilter('all');
                            }}
                          >
                            <X className="h-3.5 w-3.5 mr-1.5" />
                            Clear Filters
                          </Button>
                        ) : (
                          <Button
                            className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-500 hover:to-indigo-500 text-white text-sm px-4 transition-colors duration-150"
                            onClick={handleCreateEntry}
                          >
                            <Plus className="h-4 w-4 mr-1.5" />
                            New Entry
                          </Button>
                        )}
                      </div>
                    )}
                  </>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default JournalPage;