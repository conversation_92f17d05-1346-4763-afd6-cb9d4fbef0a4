/**
 * Settings Manager Component
 * @description Component for managing system settings in the admin panel
 * @version 1.0.0
 * @status stable
 */

import React, { useState, useEffect } from 'react';
import {
  Save,
  RefreshCw,
  Settings,
  Trophy,
  Wallet,
  Bell,
  Database,
  Shield,
  Clock,
  Globe,
  Zap,
  HelpCircle,
  Info
} from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/common/components/ui/card';
import { Button } from '@/common/components/ui/button';
import { Input } from '@/common/components/ui/input';
import { Label } from '@/common/components/ui/label';
import { Switch } from '@/common/components/ui/switch';
import { useToast } from '@/common/components/ui/use-toast';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/common/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/common/components/ui/select';
import {
  <PERSON><PERSON><PERSON>,
  Toolt<PERSON>Content,
  TooltipProvider,
  TooltipTrigger,
} from '@/common/components/ui/tooltip';
import { useSystemSettings } from '../hooks/useAdminData';

/**
 * Settings manager component
 */
const SettingsManager: React.FC = () => {
  const { toast } = useToast();

  // Challenge settings
  const [dailyMaxDrawdown, setDailyMaxDrawdown] = useState<number>(4);
  const [weeklyMaxDrawdown, setWeeklyMaxDrawdown] = useState<number>(8);
  const [monthlyMaxDrawdown, setMonthlyMaxDrawdown] = useState<number>(10);

  const [dailyMaxRiskPerTrade, setDailyMaxRiskPerTrade] = useState<number>(1);
  const [weeklyMaxRiskPerTrade, setWeeklyMaxRiskPerTrade] = useState<number>(1.5);
  const [monthlyMaxRiskPerTrade, setMonthlyMaxRiskPerTrade] = useState<number>(2);

  const [dailyMinTrades, setDailyMinTrades] = useState<number>(1);
  const [weeklyMinTrades, setWeeklyMinTrades] = useState<number>(3);
  const [monthlyMinTrades, setMonthlyMinTrades] = useState<number>(6);

  // Wallet settings
  const [creditExpirationDays, setCreditExpirationDays] = useState<number>(30);
  const [enableCreditExpiration, setEnableCreditExpiration] = useState<boolean>(true);
  const [enablePartialPayments, setEnablePartialPayments] = useState<boolean>(true);

  // Notification settings
  const [enableDiscordNotifications, setEnableDiscordNotifications] = useState<boolean>(true);
  const [enableSystemNotifications, setEnableSystemNotifications] = useState<boolean>(true);
  const [notificationFrequency, setNotificationFrequency] = useState<string>("realtime");

  // System settings
  const [maintenanceMode, setMaintenanceMode] = useState<boolean>(false);
  const [debugMode, setDebugMode] = useState<boolean>(false);
  const [apiRateLimit, setApiRateLimit] = useState<number>(100);
  const [websocketConnectionLimit, setWebsocketConnectionLimit] = useState<number>(500);

  // Security settings
  const [requireTwoFactorForAdmin, setRequireTwoFactorForAdmin] = useState<boolean>(true);
  const [sessionTimeout, setSessionTimeout] = useState<number>(60);
  const [maxLoginAttempts, setMaxLoginAttempts] = useState<number>(5);

  // Use the settings hook
  const {
    settings,
    loading,
    error,
    fetchSettings,
    updateSettings
  } = useSystemSettings();

  // Set settings when API data is loaded
  useEffect(() => {
    if (settings) {
      // Challenge settings
      if (settings.challenge) {
        if (settings.challenge.daily) {
          setDailyMaxDrawdown(settings.challenge.daily.maxDrawdown);
          setDailyMaxRiskPerTrade(settings.challenge.daily.maxRiskPerTrade);
          setDailyMinTrades(settings.challenge.daily.minTrades);
        }

        if (settings.challenge.weekly) {
          setWeeklyMaxDrawdown(settings.challenge.weekly.maxDrawdown);
          setWeeklyMaxRiskPerTrade(settings.challenge.weekly.maxRiskPerTrade);
          setWeeklyMinTrades(settings.challenge.weekly.minTrades);
        }

        if (settings.challenge.monthly) {
          setMonthlyMaxDrawdown(settings.challenge.monthly.maxDrawdown);
          setMonthlyMaxRiskPerTrade(settings.challenge.monthly.maxRiskPerTrade);
          setMonthlyMinTrades(settings.challenge.monthly.minTrades);
        }
      }

      // Wallet settings
      if (settings.wallet) {
        setCreditExpirationDays(settings.wallet.creditExpirationDays);
        setEnableCreditExpiration(settings.wallet.enableCreditExpiration);
        setEnablePartialPayments(settings.wallet.enablePartialPayments);
      }

      // Notification settings
      if (settings.notification) {
        setEnableDiscordNotifications(settings.notification.enableDiscordNotifications);
        setEnableSystemNotifications(settings.notification.enableSystemNotifications);
        setNotificationFrequency(settings.notification.frequency);
      }

      // System settings
      if (settings.system) {
        setMaintenanceMode(settings.system.maintenanceMode);
        setDebugMode(settings.system.debugMode);
        setApiRateLimit(settings.system.apiRateLimit);
        setWebsocketConnectionLimit(settings.system.websocketConnectionLimit);
      }

      // Security settings
      if (settings.security) {
        setRequireTwoFactorForAdmin(settings.security.requireTwoFactorForAdmin);
        setSessionTimeout(settings.security.sessionTimeout);
        setMaxLoginAttempts(settings.security.maxLoginAttempts);
      }
    }
  }, [settings]);

  // Handle save settings
  const handleSaveSettings = async () => {
    const settingsData = {
      challenge: {
        daily: {
          maxDrawdown: dailyMaxDrawdown,
          maxRiskPerTrade: dailyMaxRiskPerTrade,
          minTrades: dailyMinTrades
        },
        weekly: {
          maxDrawdown: weeklyMaxDrawdown,
          maxRiskPerTrade: weeklyMaxRiskPerTrade,
          minTrades: weeklyMinTrades
        },
        monthly: {
          maxDrawdown: monthlyMaxDrawdown,
          maxRiskPerTrade: monthlyMaxRiskPerTrade,
          minTrades: monthlyMinTrades
        }
      },
      wallet: {
        creditExpirationDays,
        enableCreditExpiration,
        enablePartialPayments
      },
      notification: {
        enableDiscordNotifications,
        enableSystemNotifications,
        frequency: notificationFrequency
      },
      system: {
        maintenanceMode,
        debugMode,
        apiRateLimit,
        websocketConnectionLimit
      },
      security: {
        requireTwoFactorForAdmin,
        sessionTimeout,
        maxLoginAttempts
      }
    };

    await updateSettings(settingsData);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">System Settings</h1>
        <Button
          size="sm"
          className="flex items-center gap-2"
          onClick={handleSaveSettings}
          disabled={loading}
        >
          {loading ? (
            <RefreshCw className="h-4 w-4 animate-spin" />
          ) : (
            <Save className="h-4 w-4" />
          )}
          Save All Settings
        </Button>
      </div>

      <Tabs defaultValue="challenge">
        <TabsList className="grid grid-cols-5 mb-4">
          <TabsTrigger value="challenge" className="flex items-center gap-2">
            <Trophy className="h-4 w-4" />
            Challenge
          </TabsTrigger>
          <TabsTrigger value="wallet" className="flex items-center gap-2">
            <Wallet className="h-4 w-4" />
            Wallet
          </TabsTrigger>
          <TabsTrigger value="notification" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            Notifications
          </TabsTrigger>
          <TabsTrigger value="system" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            System
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Security
          </TabsTrigger>
        </TabsList>

        {/* Challenge Settings */}
        <TabsContent value="challenge">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="bg-forex-darker border-forex-border">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <span>Daily Challenge Settings</span>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="h-4 w-4 text-forex-muted" />
                      </TooltipTrigger>
                      <TooltipContent className="bg-forex-darker border-forex-border">
                        <p>Settings for daily challenges</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </CardTitle>
                <CardDescription>Configure rules for daily challenges</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="daily-max-drawdown">Maximum Drawdown (%)</Label>
                  <Input
                    id="daily-max-drawdown"
                    type="number"
                    min="1"
                    max="20"
                    step="0.1"
                    value={dailyMaxDrawdown}
                    onChange={(e) => setDailyMaxDrawdown(parseFloat(e.target.value))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="daily-max-risk">Maximum Risk per Trade (%)</Label>
                  <Input
                    id="daily-max-risk"
                    type="number"
                    min="0.1"
                    max="5"
                    step="0.1"
                    value={dailyMaxRiskPerTrade}
                    onChange={(e) => setDailyMaxRiskPerTrade(parseFloat(e.target.value))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="daily-min-trades">Minimum Trades</Label>
                  <Input
                    id="daily-min-trades"
                    type="number"
                    min="1"
                    max="10"
                    value={dailyMinTrades}
                    onChange={(e) => setDailyMinTrades(parseInt(e.target.value))}
                  />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-forex-darker border-forex-border">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <span>Weekly Challenge Settings</span>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="h-4 w-4 text-forex-muted" />
                      </TooltipTrigger>
                      <TooltipContent className="bg-forex-darker border-forex-border">
                        <p>Settings for weekly challenges</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </CardTitle>
                <CardDescription>Configure rules for weekly challenges</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="weekly-max-drawdown">Maximum Drawdown (%)</Label>
                  <Input
                    id="weekly-max-drawdown"
                    type="number"
                    min="1"
                    max="20"
                    step="0.1"
                    value={weeklyMaxDrawdown}
                    onChange={(e) => setWeeklyMaxDrawdown(parseFloat(e.target.value))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="weekly-max-risk">Maximum Risk per Trade (%)</Label>
                  <Input
                    id="weekly-max-risk"
                    type="number"
                    min="0.1"
                    max="5"
                    step="0.1"
                    value={weeklyMaxRiskPerTrade}
                    onChange={(e) => setWeeklyMaxRiskPerTrade(parseFloat(e.target.value))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="weekly-min-trades">Minimum Trades</Label>
                  <Input
                    id="weekly-min-trades"
                    type="number"
                    min="1"
                    max="10"
                    value={weeklyMinTrades}
                    onChange={(e) => setWeeklyMinTrades(parseInt(e.target.value))}
                  />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-forex-darker border-forex-border">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <span>Monthly Challenge Settings</span>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="h-4 w-4 text-forex-muted" />
                      </TooltipTrigger>
                      <TooltipContent className="bg-forex-darker border-forex-border">
                        <p>Settings for monthly challenges</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </CardTitle>
                <CardDescription>Configure rules for monthly challenges</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="monthly-max-drawdown">Maximum Drawdown (%)</Label>
                  <Input
                    id="monthly-max-drawdown"
                    type="number"
                    min="1"
                    max="20"
                    step="0.1"
                    value={monthlyMaxDrawdown}
                    onChange={(e) => setMonthlyMaxDrawdown(parseFloat(e.target.value))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="monthly-max-risk">Maximum Risk per Trade (%)</Label>
                  <Input
                    id="monthly-max-risk"
                    type="number"
                    min="0.1"
                    max="5"
                    step="0.1"
                    value={monthlyMaxRiskPerTrade}
                    onChange={(e) => setMonthlyMaxRiskPerTrade(parseFloat(e.target.value))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="monthly-min-trades">Minimum Trades</Label>
                  <Input
                    id="monthly-min-trades"
                    type="number"
                    min="1"
                    max="10"
                    value={monthlyMinTrades}
                    onChange={(e) => setMonthlyMinTrades(parseInt(e.target.value))}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Wallet Settings */}
        <TabsContent value="wallet">
          <Card className="bg-forex-darker border-forex-border">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wallet className="h-5 w-5" />
                Wallet Settings
              </CardTitle>
              <CardDescription>Configure wallet and credit settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="credit-expiration">Enable Credit Expiration</Label>
                  <p className="text-sm text-forex-muted">Wallet credits will expire after the specified period of inactivity</p>
                </div>
                <Switch
                  id="credit-expiration"
                  checked={enableCreditExpiration}
                  onCheckedChange={setEnableCreditExpiration}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="expiration-days">Credit Expiration Period (Days)</Label>
                <Input
                  id="expiration-days"
                  type="number"
                  min="1"
                  max="365"
                  value={creditExpirationDays}
                  onChange={(e) => setCreditExpirationDays(parseInt(e.target.value))}
                  disabled={!enableCreditExpiration}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="partial-payments">Enable Partial Payments</Label>
                  <p className="text-sm text-forex-muted">Allow users to combine wallet credits with cryptocurrency for payments</p>
                </div>
                <Switch
                  id="partial-payments"
                  checked={enablePartialPayments}
                  onCheckedChange={setEnablePartialPayments}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Notification Settings */}
        <TabsContent value="notification">
          <Card className="bg-forex-darker border-forex-border">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                Notification Settings
              </CardTitle>
              <CardDescription>Configure notification preferences</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="discord-notifications">Discord Notifications</Label>
                  <p className="text-sm text-forex-muted">Send notifications to Discord</p>
                </div>
                <Switch
                  id="discord-notifications"
                  checked={enableDiscordNotifications}
                  onCheckedChange={setEnableDiscordNotifications}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="system-notifications">System Notifications</Label>
                  <p className="text-sm text-forex-muted">Send notifications within the platform</p>
                </div>
                <Switch
                  id="system-notifications"
                  checked={enableSystemNotifications}
                  onCheckedChange={setEnableSystemNotifications}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="notification-frequency">Notification Frequency</Label>
                <Select
                  value={notificationFrequency}
                  onValueChange={setNotificationFrequency}
                >
                  <SelectTrigger id="notification-frequency">
                    <SelectValue placeholder="Select frequency" />
                  </SelectTrigger>
                  <SelectContent className="bg-forex-darker border-forex-border">
                    <SelectItem value="realtime">Real-time</SelectItem>
                    <SelectItem value="hourly">Hourly Digest</SelectItem>
                    <SelectItem value="daily">Daily Digest</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* System Settings */}
        <TabsContent value="system">
          <Card className="bg-forex-darker border-forex-border">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                System Settings
              </CardTitle>
              <CardDescription>Configure system-wide settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="maintenance-mode" className="flex items-center gap-2">
                    Maintenance Mode
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger>
                          <HelpCircle className="h-4 w-4 text-forex-muted" />
                        </TooltipTrigger>
                        <TooltipContent className="bg-forex-darker border-forex-border">
                          <p>When enabled, the site will be inaccessible to regular users</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </Label>
                  <p className="text-sm text-forex-muted">Put the site in maintenance mode</p>
                </div>
                <Switch
                  id="maintenance-mode"
                  checked={maintenanceMode}
                  onCheckedChange={setMaintenanceMode}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="debug-mode">Debug Mode</Label>
                  <p className="text-sm text-forex-muted">Enable detailed error messages and logging</p>
                </div>
                <Switch
                  id="debug-mode"
                  checked={debugMode}
                  onCheckedChange={setDebugMode}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="api-rate-limit" className="flex items-center gap-2">
                  API Rate Limit
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <HelpCircle className="h-4 w-4 text-forex-muted" />
                      </TooltipTrigger>
                      <TooltipContent className="bg-forex-darker border-forex-border">
                        <p>Maximum number of API requests per minute per user</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </Label>
                <Input
                  id="api-rate-limit"
                  type="number"
                  min="10"
                  max="1000"
                  value={apiRateLimit}
                  onChange={(e) => setApiRateLimit(parseInt(e.target.value))}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="websocket-limit" className="flex items-center gap-2">
                  WebSocket Connection Limit
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <HelpCircle className="h-4 w-4 text-forex-muted" />
                      </TooltipTrigger>
                      <TooltipContent className="bg-forex-darker border-forex-border">
                        <p>Maximum number of concurrent WebSocket connections</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </Label>
                <Input
                  id="websocket-limit"
                  type="number"
                  min="100"
                  max="2000"
                  value={websocketConnectionLimit}
                  onChange={(e) => setWebsocketConnectionLimit(parseInt(e.target.value))}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Security Settings */}
        <TabsContent value="security">
          <Card className="bg-forex-darker border-forex-border">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Security Settings
              </CardTitle>
              <CardDescription>Configure security settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="two-factor-admin">Require 2FA for Admin Access</Label>
                  <p className="text-sm text-forex-muted">Require two-factor authentication for admin panel access</p>
                </div>
                <Switch
                  id="two-factor-admin"
                  checked={requireTwoFactorForAdmin}
                  onCheckedChange={setRequireTwoFactorForAdmin}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="session-timeout" className="flex items-center gap-2">
                  Session Timeout (Minutes)
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <HelpCircle className="h-4 w-4 text-forex-muted" />
                      </TooltipTrigger>
                      <TooltipContent className="bg-forex-darker border-forex-border">
                        <p>Time of inactivity before a user is logged out</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </Label>
                <Input
                  id="session-timeout"
                  type="number"
                  min="5"
                  max="240"
                  value={sessionTimeout}
                  onChange={(e) => setSessionTimeout(parseInt(e.target.value))}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="max-login-attempts" className="flex items-center gap-2">
                  Maximum Login Attempts
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <HelpCircle className="h-4 w-4 text-forex-muted" />
                      </TooltipTrigger>
                      <TooltipContent className="bg-forex-darker border-forex-border">
                        <p>Number of failed login attempts before account lockout</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </Label>
                <Input
                  id="max-login-attempts"
                  type="number"
                  min="3"
                  max="10"
                  value={maxLoginAttempts}
                  onChange={(e) => setMaxLoginAttempts(parseInt(e.target.value))}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="flex justify-end">
        <Button
          size="lg"
          className="flex items-center gap-2"
          onClick={handleSaveSettings}
          disabled={loading}
        >
          {loading ? (
            <RefreshCw className="h-4 w-4 animate-spin" />
          ) : (
            <Save className="h-4 w-4" />
          )}
          Save All Settings
        </Button>
      </div>
    </div>
  );
};

export default SettingsManager;
