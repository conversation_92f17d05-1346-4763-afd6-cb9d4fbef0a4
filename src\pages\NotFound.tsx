import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '@/common/components/ui';
import { ArrowLeft } from 'lucide-react';
import { <PERSON><PERSON>, Footer } from '@/common/components/layout';

const NotFound = () => {
  return (
    <div className="min-h-screen flex flex-col bg-forex-dark">
      <Header />

      <main className="flex-grow flex items-center justify-center px-4 py-24">
        <div className="text-center">
          <h1 className="text-6xl font-bold text-white mb-4">404</h1>
          <h2 className="text-2xl font-semibold text-white mb-6">Page Not Found</h2>
          <p className="text-forex-light/70 max-w-md mx-auto mb-8">
            The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.
          </p>
          <Link to="/">
            <Button className="bg-gradient-to-r from-forex-primary to-forex-accent hover:opacity-90 text-white">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Home
            </Button>
          </Link>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default NotFound;
