/**
 * Simple Journal Service
 * @description Service for journal-related operations using localStorage only
 * @version 1.0.0
 */

/**
 * Journal Entry Interface
 */
export interface JournalEntry {
  id: string | number;
  title: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  tags?: string[];
  mood?: 'positive' | 'neutral' | 'negative';
  tradeIds?: string[];
  attachments?: {
    type: 'image' | 'chart' | 'file';
    url: string;
    name: string;
  }[];
  isFavorite?: boolean;
  dayProfitability?: 'profitable' | 'unprofitable' | 'break-even';
  pnlAmount?: number;
}

/**
 * Interface for creating a new journal entry
 */
export interface CreateJournalEntryDto {
  title: string;
  content: string;
  tags?: string[];
  mood?: 'positive' | 'neutral' | 'negative';
  tradeIds?: string[];
  attachments?: {
    type: 'image' | 'chart' | 'file';
    url: string;
    name: string;
  }[];
  dayProfitability?: 'profitable' | 'unprofitable' | 'break-even';
  pnlAmount?: number;
}

/**
 * Interface for updating an existing journal entry
 */
export interface UpdateJournalEntryDto extends CreateJournalEntryDto {
  id: number;
}

// Local storage key for journal entries
const JOURNAL_ENTRIES_STORAGE_KEY = 'tcx_journal_entries';

/**
 * Get all journal entries from localStorage
 * @returns Array of journal entries
 */
const getEntriesFromStorage = (): JournalEntry[] => {
  try {
    const storedEntries = localStorage.getItem(JOURNAL_ENTRIES_STORAGE_KEY);
    if (storedEntries) {
      return JSON.parse(storedEntries);
    }
  } catch (error) {
    console.error('Error reading journal entries from localStorage:', error);
  }
  return [];
};

/**
 * Save journal entries to localStorage
 * @param entries Array of journal entries to save
 */
const saveEntriesToStorage = (entries: JournalEntry[]): void => {
  try {
    localStorage.setItem(JOURNAL_ENTRIES_STORAGE_KEY, JSON.stringify(entries));
    console.log('Saved entries to localStorage:', entries.length);
  } catch (error) {
    console.error('Error saving journal entries to localStorage:', error);
  }
};

/**
 * Get all journal entries from localStorage
 * @returns Array of journal entries
 */
export const getJournalEntries = async (): Promise<JournalEntry[]> => {
  console.log('Fetching journal entries from localStorage...');
  const entries = getEntriesFromStorage();
  console.log(`Found ${entries.length} entries in localStorage`);
  return entries;
};

/**
 * Get a single journal entry by ID
 * @param id Journal entry ID
 * @returns Journal entry or null if not found
 */
export const getJournalEntryById = async (id: number | string): Promise<JournalEntry | null> => {
  console.log(`Fetching journal entry with ID: ${id}`);
  const entries = getEntriesFromStorage();
  const entry = entries.find(e => e.id === id || e.id.toString() === id.toString());
  return entry || null;
};

/**
 * Create a new journal entry
 * @param entry Journal entry data
 * @returns Created journal entry
 */
export const createJournalEntry = async (entry: CreateJournalEntryDto): Promise<JournalEntry> => {
  console.log('Creating new journal entry:', entry);

  // Create a new entry object
  const newEntry: JournalEntry = {
    id: Date.now().toString(), // Use timestamp as ID
    title: entry.title,
    content: entry.content,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    tags: entry.tags || [],
    mood: entry.mood,
    tradeIds: entry.tradeIds || [],
    attachments: entry.attachments,
    dayProfitability: entry.dayProfitability,
    pnlAmount: entry.pnlAmount,
    isFavorite: false
  };

  // Get current entries and add the new one at the beginning
  const currentEntries = getEntriesFromStorage();
  const updatedEntries = [newEntry, ...currentEntries];

  // Save to localStorage
  saveEntriesToStorage(updatedEntries);
  console.log('Journal entry created successfully:', newEntry);

  return newEntry;
};

/**
 * Update an existing journal entry
 * @param id Journal entry ID
 * @param entry Updated journal entry data
 * @returns Updated journal entry or null if not found
 */
export const updateJournalEntry = async (id: number, entry: Omit<UpdateJournalEntryDto, 'id'>): Promise<JournalEntry | null> => {
  console.log(`Updating journal entry with ID: ${id}`, entry);

  // Get current entries
  const currentEntries = getEntriesFromStorage();
  const entryIndex = currentEntries.findIndex(e =>
    e.id === id || e.id.toString() === id.toString()
  );

  // If entry not found, return null
  if (entryIndex === -1) {
    console.warn(`Journal entry with ID ${id} not found`);
    return null;
  }

  // Create updated entry
  const updatedEntry: JournalEntry = {
    ...currentEntries[entryIndex],
    ...entry,
    id: currentEntries[entryIndex].id, // Preserve the original ID
    updatedAt: new Date().toISOString()
  };

  // Update the entry in the array
  const updatedEntries = [...currentEntries];
  updatedEntries[entryIndex] = updatedEntry;

  // Save to localStorage
  saveEntriesToStorage(updatedEntries);
  console.log(`Journal entry ${id} updated successfully`);

  return updatedEntry;
};

/**
 * Delete a journal entry
 * @param id Journal entry ID
 * @returns Success message
 */
export const deleteJournalEntry = async (id: number): Promise<{ message: string }> => {
  console.log(`Deleting journal entry with ID: ${id}`);

  // Get current entries
  const currentEntries = getEntriesFromStorage();
  const updatedEntries = currentEntries.filter(entry =>
    entry.id !== id && entry.id.toString() !== id.toString()
  );

  // If no entry was removed, return error message
  if (currentEntries.length === updatedEntries.length) {
    console.warn(`Journal entry with ID ${id} not found`);
    return { message: 'Journal entry not found' };
  }

  // Save to localStorage
  saveEntriesToStorage(updatedEntries);
  console.log(`Journal entry ${id} deleted successfully`);

  return { message: 'Journal entry deleted successfully' };
};

/**
 * Toggle favorite status of a journal entry
 * @param id Journal entry ID
 * @returns Updated journal entry or null if not found
 */
export const toggleFavoriteJournalEntry = async (id: number): Promise<JournalEntry | null> => {
  console.log(`Toggling favorite status for journal entry with ID: ${id}`);

  // Get current entries
  const currentEntries = getEntriesFromStorage();
  const entryIndex = currentEntries.findIndex(entry =>
    entry.id === id || entry.id.toString() === id.toString()
  );

  // If entry not found, return null
  if (entryIndex === -1) {
    console.warn(`Journal entry with ID ${id} not found`);
    return null;
  }

  // Toggle the favorite status
  const updatedEntry = {
    ...currentEntries[entryIndex],
    isFavorite: !currentEntries[entryIndex].isFavorite,
    updatedAt: new Date().toISOString()
  };

  // Update the entry in the array
  const updatedEntries = [...currentEntries];
  updatedEntries[entryIndex] = updatedEntry;

  // Save to localStorage
  saveEntriesToStorage(updatedEntries);
  console.log(`Favorite status for journal entry ${id} toggled successfully`);

  return updatedEntry;
};
